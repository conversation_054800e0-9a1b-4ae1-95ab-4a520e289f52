import React from "react";
import { Icon } from "@iconify/react/dist/iconify.js";

const ConfirmDeleteModal = ({
  isOpen,
  onClose,
  onConfirm,
  accountName,
  loading,
}) => {
  if (!isOpen) return null;

  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(0, 0, 0, 0.6)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        zIndex: 1050,
      }}
      onClick={onClose}
    >
      <div
        style={{
          background: "white",
          borderRadius: "8px",
          width: "90%",
          maxWidth: "400px",
          boxShadow: "0 5px 15px rgba(0,0,0,.5)",
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <div style={{ padding: "2rem", textAlign: "center" }}>
          <Icon
            icon="ph:warning-circle-bold"
            className="text-danger mb-3"
            style={{ fontSize: "4rem" }}
          />
          <h5 className="fw-bold"><PERSON><PERSON><PERSON> nhận xóa VA</h5>
          <p className="text-muted">
            Bạn có chắc chắn muốn xóa tài khoản ảo <br />
            <strong className="text-dark">{accountName}</strong> không? Hành
            động này không thể hoàn tác.
          </p>
        </div>
        <div
          style={{
            padding: "1rem",
            borderTop: "1px solid #dee2e6",
            display: "flex",
            justifyContent: "flex-end",
            gap: "0.5rem",
          }}
        >
          <button
            type="button"
            className="btn btn-light border"
            onClick={onClose}
            disabled={loading}
          >
            Hủy
          </button>
          <button
            type="button"
            className="btn btn-danger"
            onClick={onConfirm}
            disabled={loading}
          >
            {loading ? "Đang xóa..." : "Xác nhận xóa"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ConfirmDeleteModal;
