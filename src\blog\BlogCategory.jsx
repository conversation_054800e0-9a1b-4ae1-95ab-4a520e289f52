import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { Helmet } from "react-helmet-async";
import usePageLoading from "../hooks/usePageLoading";
import LoadingSkeleton from "./LoadingSkeleton";
import blogCache from "../utils/blogCache";
import blogApi from "../callapi/BlogApiNew";
import "./blog.css";

const BlogCategory = () => {
  const { slug } = useParams();
  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [category, setCategory] = useState(null);
  const [totalPosts, setTotalPosts] = useState(0);
  const [skeletonReady, setSkeletonReady] = useState(false);

  // Complete loading khi data đã load xong
  usePageLoading(loading);

  // Fetch initial posts by category
  const fetchPostsByCategory = async () => {
    try {
      setError(null);

      // Check cache first
      const cacheKey = blogCache.generateKey("category", { slug, page: 1 });
      const cachedData = blogCache.get(cacheKey);

      if (cachedData) {
        // Use cached data immediately but keep loading for a moment
        setCategory(cachedData.category);
        setTotalPosts(cachedData.totalPosts);
        setPosts(cachedData.posts);
        setCurrentPage(1);
        setHasMore(cachedData.posts.length >= 6);

        // Small delay to show loading bar briefly even with cache
        setTimeout(() => {
          setLoading(false);
        }, 300);
        return;
      }

      // No cache, show loading
      setLoading(true);

      // Get category by slug using the new API method
      const foundCategory = await blogApi.getCategoryBySlug(slug);

      setCategory(foundCategory);
      setTotalPosts(foundCategory.count);

      // Then get first 6 posts for this category
      const result = await blogApi.getPostsByCategoryPaginated(
        foundCategory.id,
        1,
        6
      );

      // Cache the result
      const cacheData = {
        category: foundCategory,
        totalPosts: foundCategory.count,
        posts: result.posts,
      };
      blogCache.set(cacheKey, cacheData);

      setPosts(result.posts);
      setCurrentPage(1);
      setHasMore(result.posts.length === 6 && foundCategory.count > 6);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Load more posts
  const loadMorePosts = async () => {
    if (loadingMore || !hasMore || !category) return;

    try {
      setLoadingMore(true);
      setError(null);

      const nextPage = currentPage + 1;
      const result = await blogApi.getPostsByCategoryPaginated(
        category.id,
        nextPage,
        6
      );

      setPosts((prevPosts) => [...prevPosts, ...result.posts]);
      setCurrentPage(nextPage);
      setHasMore(
        result.posts.length === 6 &&
          posts.length + result.posts.length < totalPosts
      );
    } catch (err) {
      setError(err.message);
    } finally {
      setLoadingMore(false);
    }
  };

  useEffect(() => {
    if (slug) {
      fetchPostsByCategory();
    }
  }, [slug]);

  // Format date
  const formatDate = (dateString) => {
    const options = {
      year: "numeric",
      month: "long",
      day: "numeric",
      timeZone: "Asia/Ho_Chi_Minh",
    };
    return new Date(dateString).toLocaleDateString("vi-VN", options);
  };

  // Extract excerpt from content
  const getExcerpt = (content, maxLength = 150) => {
    const text = content.replace(/<[^>]*>/g, "");
    return text.length > maxLength
      ? text.substring(0, maxLength) + "..."
      : text;
  };

  // Get featured image
  const getFeaturedImage = (post) => {
    if (
      post._embedded &&
      post._embedded["wp:featuredmedia"] &&
      post._embedded["wp:featuredmedia"][0]
    ) {
      return post._embedded["wp:featuredmedia"][0].source_url;
    }
    return "/src/assets/images/blog/blog1.png";
  };

  if (loading) {
    // Set skeleton ready after a brief delay to ensure loading bar shows
    if (!skeletonReady) {
      setTimeout(() => {
        setSkeletonReady(true);
      }, 500); // Loading bar chạy 500ms trước khi skeleton xuất hiện
    }

    return (
      <main className="blog-main">
        <div className="container py-5">
          {/* Skeleton loaders - chỉ hiển thị sau khi loading bar đã chạy */}
          {skeletonReady && (
            <>
              {/* Category header skeleton */}
              <div className="row mb-5">
                <div className="col-12 text-center">
                  <div className="skeleton-title-large mb-3"></div>
                  <div
                    className="skeleton-line"
                    style={{ width: "300px", margin: "0 auto" }}
                  ></div>
                </div>
              </div>

              {/* Posts skeleton */}
              <div className="row">
                {[...Array(6)].map((_, index) => (
                  <div key={index} className="col-lg-4 col-md-6 mb-4">
                    <LoadingSkeleton type="post" />
                  </div>
                ))}
              </div>
            </>
          )}
        </div>
      </main>
    );
  }

  if (error) {
    return (
      <main className="blog-main">
        <div className="container">
          <div className="error-message">
            <h2>Có lỗi xảy ra</h2>
            <p>{error}</p>
            <Link to="/blog" className="retry-btn">
              Quay lại Blog
            </Link>
          </div>
        </div>
      </main>
    );
  }

  return (
    <div className="blog-container">
      <Helmet>
        <title>
          {category?.name
            ? `${category.name} - Blog Pay2S`
            : "Danh mục - Blog Pay2S"}
        </title>
        <meta
          name="description"
          content={`Các bài viết thuộc danh mục ${
            category?.name || "này"
          } từ Blog Pay2S`}
        />
        <meta
          name="keywords"
          content={`pay2s, blog, ${
            category?.name || "danh mục"
          }, thanh toán điện tử`}
        />
        <meta
          property="og:title"
          content={`${category?.name || "Danh mục"} - Blog Pay2S`}
        />
        <meta
          property="og:description"
          content={`Các bài viết thuộc danh mục ${
            category?.name || "này"
          } từ Blog Pay2S`}
        />
        <meta property="og:type" content="website" />
        <meta property="og:url" content={`https://pay2s.vn/blog/${slug}`} />
        <link rel="canonical" href={`https://pay2s.vn/blog/${slug}`} />
      </Helmet>

      <main className="blog-main">
        <div className="container">
          {/* Breadcrumb */}
          <nav className="breadcrumb">
            <Link to="/">Trang chủ</Link>
            <span className="breadcrumb-separator">›</span>
            <Link to="/blog">Blog</Link>
            <span className="breadcrumb-separator">›</span>
            <span className="breadcrumb-current">
              {category?.name || "Danh mục"}
            </span>
          </nav>

          <div className="blog-header">
            <h1>{category?.name || "Danh mục"}</h1>
          </div>

          {posts.length > 0 ? (
            <>
              <div className="blog-grid">
                {posts.map((post) => (
                  <article key={post.id} className="blog-card">
                    <div className="blog-card-image">
                      <img
                        src={getFeaturedImage(post)}
                        alt={post.title.rendered}
                        onError={(e) => {
                          e.target.src = "/src/assets/images/blog/blog1.png";
                        }}
                      />
                    </div>

                    <div className="blog-card-content">
                      <div className="blog-meta">
                        <span className="blog-date">
                          {formatDate(post.date)}
                        </span>
                        <span className="blog-category">{category?.name}</span>
                      </div>

                      <h2 className="blog-title">
                        <Link
                          to={`/blog/${post.slug}`}
                          dangerouslySetInnerHTML={{
                            __html: post.title.rendered,
                          }}
                        />
                      </h2>

                      <div className="blog-excerpt">
                        {getExcerpt(post.excerpt.rendered)}
                      </div>

                      <Link to={`/blog/${post.slug}`} className="read-more-btn">
                        Đọc thêm →
                      </Link>
                    </div>
                  </article>
                ))}
              </div>

              {/* Load More Button */}
              {hasMore && (
                <div className="load-more-section">
                  <button
                    onClick={loadMorePosts}
                    disabled={loadingMore}
                    className="load-more-btn"
                  >
                    {loadingMore ? (
                      <>
                        <i className="fas fa-spinner fa-spin me-2"></i>
                        Đang tải...
                      </>
                    ) : (
                      <>
                        Xem thêm bài viết
                        <i className="fas fa-chevron-down ms-2"></i>
                      </>
                    )}
                  </button>

                  {loadingMore && (
                    <div className="loading-more-posts">
                      <LoadingSkeleton type="posts" count={6} />
                    </div>
                  )}
                </div>
              )}
            </>
          ) : (
            <div className="no-posts">
              <h3>Chưa có bài viết nào trong danh mục này</h3>
              <p>Vui lòng quay lại sau hoặc xem các danh mục khác.</p>
              <Link to="/blog" className="back-btn">
                Quay lại Blog
              </Link>
            </div>
          )}
        </div>
      </main>
    </div>
  );
};

export default BlogCategory;
