// src/callapi/useBankList.jsx

import { useState, useEffect, useCallback } from "react";
import useBankApi from "./Bank.jsx";

// Hook chuyên dụng cho danh sách tài khoản ngân hàng với cache thông minh
const useBankList = (autoLoad = true) => {
  const {
    data,
    loading,
    error,
    isStale,
    callApi,
    preloadApi,
    invalidateCache,
  } = useBankApi();
  const [bankList, setBankList] = useState([]);
  const [lastUpdated, setLastUpdated] = useState(null);

  // Load danh sách tài khoản ngân hàng
  const loadBankList = useCallback(
    async (forceRefresh = false) => {
      const result = await callApi(
        { action: "get", type: "list" },
        { forceRefresh }
      );

      if (result && result.data) {
        setBankList(result.data);
        setLastUpdated(new Date());
      }

      return result;
    },
    [callApi]
  );

  // Refresh với optimistic update
  const refreshBankList = useCallback(async () => {
    return await loadBankList(true);
  }, [loadBankList]);

  // Thêm tài khoản mới với optimistic update
  const addBankAccount = useCallback(
    async (bankData) => {
      // Optimistic update - thêm vào danh sách ngay
      const optimisticAccount = {
        ...bankData,
        id: Date.now(), // Temporary ID
        status: "pending",
      };

      setBankList((prev) => [optimisticAccount, ...prev]);

      const result = await callApi(
        { action: "add", ...bankData },
        { optimistic: { data: [optimisticAccount, ...bankList] } }
      );

      if (result && result.status) {
        // Refresh để lấy data chính xác từ server
        await loadBankList(true);
        invalidateCache({ action: "get", type: "list" });
      } else {
        // Rollback optimistic update nếu thất bại
        setBankList((prev) =>
          prev.filter((acc) => acc.id !== optimisticAccount.id)
        );
      }

      return result;
    },
    [callApi, bankList, loadBankList, invalidateCache]
  );

  // Xóa tài khoản với optimistic update
  const deleteBankAccount = useCallback(
    async (accountId) => {
      // Optimistic update - xóa khỏi danh sách ngay
      const originalList = [...bankList];
      setBankList((prev) => prev.filter((acc) => acc.id !== accountId));

      const result = await callApi({ action: "delete", account_id: accountId });

      if (result && result.status) {
        invalidateCache({ action: "get", type: "list" });
      } else {
        // Rollback nếu thất bại
        setBankList(originalList);
      }

      return result;
    },
    [callApi, bankList, invalidateCache]
  );

  // Auto load khi component mount
  useEffect(() => {
    if (autoLoad) {
      loadBankList();
    }
  }, [autoLoad, loadBankList]);

  // Preload related data
  useEffect(() => {
    if (bankList.length > 0) {
      // Preload thông tin chi tiết của tài khoản đầu tiên
      const firstAccount = bankList[0];
      if (firstAccount && firstAccount.id) {
        preloadApi({
          action: "get",
          type: "detail",
          account_id: firstAccount.id,
        }).catch(() => {});
      }
    }
  }, [bankList, preloadApi]);

  return {
    bankList,
    loading,
    error,
    isStale,
    lastUpdated,
    loadBankList,
    refreshBankList,
    addBankAccount,
    deleteBankAccount,
    // Raw data từ API
    rawData: data,
  };
};

export default useBankList;
