import React, { useState, useEffect } from "react";
import { Outlet, useLocation } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import { Helmet } from "react-helmet-async";
import Header from "../blog/Header";
import Footer from "../landing/Footer";
import "./BlogTransition.css";

const BlogTransition = () => {
  const [isContentLoading, setIsContentLoading] = useState(false);
  const [contentKey, setContentKey] = useState(0);
  const location = useLocation();

  // Handle route changes - giống evonhub.dev
  useEffect(() => {
    setIsContentLoading(true);
    setContentKey((prev) => prev + 1);

    // Thời gian loading ngắn để UX mượt mà
    const timer = setTimeout(() => {
      setIsContentLoading(false);
    }, 250);

    return () => clearTimeout(timer);
  }, [location.pathname]);

  // Animation variants - subtle như evonhub.dev
  const contentVariants = {
    initial: {
      opacity: 0,
      y: 8,
    },
    animate: {
      opacity: 1,
      y: 0,
    },
    exit: {
      opacity: 0,
      y: -4,
    },
  };

  return (
    <>
      <Helmet>
        <title>Blog Pay2S - Tin tức và kiến thức thanh toán điện tử</title>
        <meta
          name="description"
          content="Cập nhật tin tức mới nhất về thanh toán điện tử, fintech và các giải pháp ngân hàng số từ Pay2S"
        />
      </Helmet>

      {/* Fixed Header - Không bao giờ reload */}
      <Header />

      {/* Loading Bar ở top - giống evonhub.dev */}
      {isContentLoading && (
        <div className="blog-loading-bar">
          <div className="loading-progress"></div>
        </div>
      )}

      {/* Content Area - Chỉ phần này update */}
      <main className="blog-transition-main">
        {/* Animated Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={contentKey}
            initial="initial"
            animate="animate"
            exit="exit"
            variants={contentVariants}
            transition={{
              duration: 0.2,
              ease: [0.25, 0.46, 0.45, 0.94],
            }}
            className="blog-content-wrapper"
          >
            <Outlet />
          </motion.div>
        </AnimatePresence>
      </main>

      {/* Fixed Footer - Không bao giờ reload */}
      <Footer />
    </>
  );
};

// Loading component cho content riêng lẻ
export const ContentLoader = ({
  isLoading,
  children,
  message = "Đang tải...",
}) => {
  if (isLoading) {
    return (
      <div className="content-loader-wrapper">
        <div className="content-loader">
          <div className="loader-spinner">
            <div className="spinner-ring"></div>
            <div className="spinner-ring"></div>
            <div className="spinner-ring"></div>
          </div>
          <p className="loader-text">{message}</p>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 6 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.25, ease: "easeOut" }}
    >
      {children}
    </motion.div>
  );
};

// Skeleton loader cho blog posts
export const BlogSkeleton = ({ count = 6 }) => {
  return (
    <div className="blog-skeleton">
      {[...Array(count)].map((_, index) => (
        <div key={index} className="skeleton-card">
          <div className="skeleton-image"></div>
          <div className="skeleton-content">
            <div className="skeleton-line skeleton-title"></div>
            <div className="skeleton-line skeleton-text"></div>
            <div className="skeleton-line skeleton-text short"></div>
            <div className="skeleton-meta">
              <div className="skeleton-date"></div>
              <div className="skeleton-category"></div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

// Skeleton cho single post
export const PostSkeleton = () => {
  return (
    <div className="post-skeleton">
      <div className="skeleton-breadcrumb">
        <div className="skeleton-line short"></div>
      </div>
      <div className="skeleton-post-header">
        <div className="skeleton-line skeleton-post-title"></div>
        <div className="skeleton-post-meta">
          <div className="skeleton-date"></div>
          <div className="skeleton-author"></div>
        </div>
      </div>
      <div className="skeleton-featured-image"></div>
      <div className="skeleton-post-content">
        {[...Array(8)].map((_, i) => (
          <div key={i} className="skeleton-line skeleton-paragraph"></div>
        ))}
      </div>
    </div>
  );
};

export default BlogTransition;
