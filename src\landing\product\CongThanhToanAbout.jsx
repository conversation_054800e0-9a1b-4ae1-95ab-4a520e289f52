import React from "react";
import aboutImg from "../../assets/landing/img/normal/about_3_1.svg";
import titleShape2 from "../../assets/landing/img/theme-img/title_shape_2.svg";

const CongThanhToanAbout = () => (
  <div className="overflow-hidden">
    <div className="container">
      <div className="row align-items-center">
        <div className="col-xl-6 mb-30 mb-xl-0">
          <div className="img-box4 tilt-active">
            <img src={aboutImg} alt="About" />
          </div>
        </div>
        <div className="col-xl-6">
          <div className="title-area mb-35 text-center text-xl-start">
            <span className="sub-title">
              <div className="icon-masking me-2">
                <span className="mask-icon" data-mask-src={titleShape2}></span>
                <img src={titleShape2} alt="shape" />
              </div>
              pay2s đem đến giải pháp cho bạn
            </span>
            <h2 className="sec-title" id="anchor-sec-title">
              Bạn đang bán hàng online, vận hành chatbot,
              <br />
              hay quản lý hệ thống phần mềm?
            </h2>
          </div>
          <h6 className="mt-n2 mb-30 text-center text-xl-start">
            Mỗi đơn hàng – Một lần bối rối vì không nhận được thông báo chuyển
            khoản kịp thời? Bạn đau đầu vì những:
          </h6>
          <ul
            className="liststyle"
            style={{
              color: "black",
            }}
          >
            <li>Thủ tục đăng ký, định danh rườm rà</li>
            <li>Phí trung gian, chi phí giao dịch nhiều</li>
            <li>Tiền chậm về, đối soát lâu, mất thời gian</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
);

export default CongThanhToanAbout;
