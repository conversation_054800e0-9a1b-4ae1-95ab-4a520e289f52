import React, { useState } from "react";
import MasterLayout from "../masterLayout/MasterLayout";
import Breadcrumb from "../components/Breadcrumb";
import { Icon } from "@iconify/react";

const SmsTestPage = () => {
  const [testResults, setTestResults] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  const addTestResult = (message, type = "info") => {
    const timestamp = new Date().toLocaleTimeString();
    setTestResults((prev) => [...prev, { message, type, timestamp }]);
  };

  const runSmsTest = async () => {
    setIsLoading(true);
    setTestResults([]);

    addTestResult("🚀 Bắt đầu kiểm tra SMS service...", "info");

    // Test 1: Kiểm tra thông tin user
    try {
      const userId = localStorage.getItem("user_id");
      const token = localStorage.getItem("token");

      if (!userId || !token) {
        addTestResult("❌ Không tìm thấy thông tin đăng nhập", "error");
        setIsLoading(false);
        return;
      }

      addTestResult(`✅ User ID: ${userId}`, "success");
      addTestResult(`✅ Token: ${token.substring(0, 20)}...`, "success");

      // Test 2: Kiểm tra kết nối API
      const API_BASE_URL = import.meta.env.DEV
        ? "/api/v1"
        : "https://api.pay2s.vn/api/v1";
      addTestResult(`🔗 API Base URL: ${API_BASE_URL}`, "info");

      // Test 3: Thử gọi API để lấy thông tin user
      const userResponse = await fetch(`${API_BASE_URL}/user`, {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          Authorization: `Bearer ${token}`,
        },
        body: new URLSearchParams({
          action: "profile",
          user_id: userId,
        }),
      });

      if (userResponse.ok) {
        const userData = await userResponse.json();
        addTestResult("✅ Kết nối API thành công", "success");
        if (userData.data && userData.data.phone) {
          addTestResult(
            `📞 Số điện thoại đã đăng ký: ${userData.data.phone}`,
            "success"
          );
        } else {
          addTestResult(
            "⚠️ Không tìm thấy số điện thoại trong profile",
            "warning"
          );
        }
      } else {
        addTestResult(`❌ Lỗi kết nối API: ${userResponse.status}`, "error");
      }

      // Test 4: Kiểm tra cấu hình SMS
      addTestResult("🔍 Kiểm tra cấu hình SMS service...", "info");

      // Test 5: Thử gọi một API khác để kiểm tra backend
      const systemResponse = await fetch(`${API_BASE_URL}/system`, {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          Authorization: `Bearer ${token}`,
        },
        body: new URLSearchParams({
          action: "check_sms_service", // Giả sử có endpoint này
        }),
      });

      if (systemResponse.ok) {
        const systemData = await systemResponse.json();
        addTestResult("✅ Backend SMS service có vẻ hoạt động", "success");
      } else {
        addTestResult(
          "⚠️ Không thể kiểm tra trạng thái SMS service",
          "warning"
        );
      }
    } catch (error) {
      addTestResult(`❌ Lỗi khi test: ${error.message}`, "error");
    } finally {
      setIsLoading(false);
      addTestResult("🏁 Hoàn thành kiểm tra", "info");
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <MasterLayout>
      <Breadcrumb title="SMS Service Test" />
      <div className="container-fluid mt-4">
        <div className="card">
          <div className="card-header">
            <h5 className="card-title mb-0">
              <Icon icon="ph:test-tube" className="me-2" />
              Kiểm tra SMS Service
            </h5>
          </div>
          <div className="card-body">
            <div className="alert alert-info">
              <strong>ℹ️ Về trang này:</strong>
              <p className="mb-0 mt-1">
                Trang này giúp kiểm tra các thành phần liên quan đến SMS OTP
                service để debug vấn đề không nhận được tin nhắn OTP khi xóa tài
                khoản ngân hàng MBB.
              </p>
            </div>

            <div className="mb-4">
              <button
                className="btn btn-primary me-2"
                onClick={runSmsTest}
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <span className="spinner-border spinner-border-sm me-2" />
                    Đang kiểm tra...
                  </>
                ) : (
                  <>
                    <Icon icon="ph:play" className="me-2" />
                    Chạy test SMS
                  </>
                )}
              </button>

              <button
                className="btn btn-secondary"
                onClick={clearResults}
                disabled={isLoading}
              >
                <Icon icon="ph:trash" className="me-2" />
                Xóa kết quả
              </button>
            </div>

            {testResults.length > 0 && (
              <div className="card">
                <div className="card-header">
                  <h6 className="mb-0">Kết quả kiểm tra</h6>
                </div>
                <div className="card-body">
                  <div style={{ maxHeight: "400px", overflowY: "auto" }}>
                    {testResults.map((result, index) => (
                      <div
                        key={index}
                        className={`alert alert-${
                          result.type === "success"
                            ? "success"
                            : result.type === "error"
                            ? "danger"
                            : result.type === "warning"
                            ? "warning"
                            : "info"
                        } py-2 mb-2`}
                      >
                        <small className="text-muted me-2">
                          [{result.timestamp}]
                        </small>
                        {result.message}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            <div className="mt-4">
              <div className="card">
                <div className="card-header">
                  <h6 className="mb-0">
                    <Icon icon="ph:lightbulb" className="me-2" />
                    Gợi ý khắc phục
                  </h6>
                </div>
                <div className="card-body">
                  <h6>Các nguyên nhân có thể gây ra vấn đề SMS OTP:</h6>
                  <ol>
                    <li>
                      <strong>Backend SMS Gateway:</strong> Cấu hình SMS
                      provider (Viettel, Mobifone, VNPT, etc.) không hoạt động
                    </li>
                    <li>
                      <strong>Số điện thoại:</strong> Số điện thoại trong
                      profile không đúng định dạng hoặc không còn hoạt động
                    </li>
                    <li>
                      <strong>Nhà mạng:</strong> Nhà mạng chặn tin nhắn từ SMS
                      gateway
                    </li>
                    <li>
                      <strong>Cấu hình server:</strong> Firewall hoặc network
                      blocking outbound SMS requests
                    </li>
                    <li>
                      <strong>Rate limiting:</strong> Quá nhiều requests SMS
                      trong thời gian ngắn
                    </li>
                    <li>
                      <strong>Credit hết:</strong> Tài khoản SMS service hết
                      credit
                    </li>
                  </ol>

                  <h6 className="mt-3">Cách khắc phục:</h6>
                  <ul>
                    <li>
                      Kiểm tra logs backend để xem request SMS có được gửi không
                    </li>
                    <li>Kiểm tra response từ SMS provider</li>
                    <li>Test với số điện thoại khác</li>
                    <li>
                      Kiểm tra cấu hình environment variables cho SMS service
                    </li>
                    <li>Liên hệ SMS provider để kiểm tra trạng thái account</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </MasterLayout>
  );
};

export default SmsTestPage;
