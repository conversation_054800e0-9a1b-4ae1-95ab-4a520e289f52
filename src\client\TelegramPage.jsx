import MasterLayout from "../masterLayout/MasterLayout";
import { Icon } from "@iconify/react";
import Breadcrumb from "../components/Breadcrumb";
import React, { useState, useMemo, useEffect } from "react";
import { bankLogos } from "../client/ImportImage";
import * as XLSX from "xlsx";
import { useNavigate } from "react-router-dom";
import useBankApi from "../callapi/Bank";
import useTelegramApi from "../callapi/useTelegramApi";
import axios from "axios";
import { API_BASE_URL } from "../callapi/apiConfig";

const asset = {
  ACB: { bg: bankLogos.logoAcb },
  BIDV: { bg: bankLogos.logoBidv },
  MBBank: { bg: bankLogos.logoMbb },
  SEAB: { bg: bankLogos.logoSeab },
  TCB: { bg: bankLogos.logoTcb },
  VCB: { bg: bankLogos.logoVcb },
  VTB: { bg: bankLogos.logoVtb },
};

const TelegramPage = () => {
  const navigate = useNavigate();
  const {
    data: bankRes,
    loading: bankLoading,
    callApi: callBankApi,
  } = useBankApi();

  const {
    data: apiRes,
    loading: telegramLoading,
    error: telegramError,
    callApi,
  } = useTelegramApi();

  // State chính
  const [telegramData, setTelegramData] = useState([]);
  const [accountFilter, setAccountFilter] = useState("");
  const [eventFilter, setEventFilter] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [selected, setSelected] = useState(null);

  // State cho modal "Thêm"
  const [showAddModal, setShowAddModal] = useState(false);
  const [addBankId, setAddBankId] = useState("");
  const [addEvent, setAddEvent] = useState("IN");
  const [addChatId, setAddChatId] = useState("");
  const [addDescription, setAddDescription] = useState("");
  const [showBankDropdown, setShowBankDropdown] = useState(false);
  const [selectedBankName, setSelectedBankName] = useState("");

  // State cho modal "Sửa"
  const [showEditModal, setShowEditModal] = useState(false);
  const [editEvent, setEditEvent] = useState("");
  const [editChatId, setEditChatId] = useState("");
  const [editDescription, setEditDescription] = useState("");

  // State cho modal "Xóa"
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteTarget, setDeleteTarget] = useState(null);

  // State phân trang
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  const bankAccounts = bankRes?.banks || bankRes?.data?.banks || [];

  const allBankAccounts = useMemo(() => {
    const accounts = [];
    bankAccounts.forEach((account) => {
      if (account.vaNumber && account.vaNumber !== account.accountNumber) {
        accounts.push({
          ...account,
          displayName: `${account.vaNumber} - ${account.bankName} (VA)`,
          accountType: "va",
          accountNumber: account.vaNumber,
        });
      } else {
        accounts.push({
          ...account,
          displayName: `${account.accountNumber} - ${account.bankName}`,
          accountType: "main",
        });
      }
      if (account.vaAccounts && account.vaAccounts.length > 0) {
        account.vaAccounts.forEach((vaAccount) => {
          accounts.push({
            ...vaAccount,
            displayName: `${vaAccount.vaNumber} - ${vaAccount.bankName} (VA)`,
            accountType: "va",
            accountNumber: vaAccount.vaNumber,
          });
        });
      }
    });
    return accounts;
  }, [bankAccounts]);

  const getEventLabel = (code) =>
    ({ IN: "Nhận", OUT: "Gửi", ALL: "Gửi và nhận" }[code] || code);

  useEffect(() => {
    callApi({ action: "list" });
    const userId = localStorage.getItem("user_id");
    if (userId) callBankApi({ action: "bank_account", user_id: userId });
  }, [callApi, callBankApi]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showBankDropdown && !event.target.closest(".position-relative")) {
        setShowBankDropdown(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [showBankDropdown]);

  useEffect(() => {
    if (apiRes && apiRes.data && Array.isArray(apiRes.data)) {
      setTelegramData(apiRes.data);
    }
  }, [apiRes]);

  const data = useMemo(() => {
    if (!Array.isArray(telegramData)) return [];
    return telegramData.map((item) => {
      const bank = bankAccounts.find((b) => b.id === item.user_bank_id) || {};
      let displayBankName = bank.bankName;
      let displayAccountNumber = bank.accountNumber || "";
      if (bank.vaNumber && bank.vaNumber !== bank.accountNumber) {
        displayBankName = `${bank.bankName} (VA)`;
        displayAccountNumber = bank.vaNumber;
      }
      const match = bank.bankName?.match(/\(([^)]+)\)/);
      const bankCode = match ? match[1] : bank.bankName;
      return {
        id: item.id,
        accountLogo: asset[bankCode]?.bg || "",
        accountName: displayBankName,
        accountNumber: displayAccountNumber,
        event: item.event || item.type || "ALL",
        chatId: item.chat_id?.toString() || "",
        description: item.name || "",
        addedAt: item.created_at,
        updatedAt: item.updated_at,
        active: item.status === 1,
        token: item.token || "",
      };
    });
  }, [telegramData, bankAccounts]);

  const list = useMemo(() => {
    return data.filter((w) => {
      if (accountFilter && w.accountName !== accountFilter) return false;
      if (eventFilter && w.event !== eventFilter) return false;
      if (statusFilter) {
        const st = w.active ? "Hoạt động" : "Không hoạt động";
        if (st !== statusFilter) return false;
      }
      if (searchTerm) {
        const t = searchTerm.toLowerCase();
        return Object.values(w).some((val) =>
          String(val).toLowerCase().includes(t)
        );
      }
      return true;
    });
  }, [data, accountFilter, eventFilter, statusFilter, searchTerm]);

  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return list.slice(startIndex, startIndex + itemsPerPage);
  }, [list, currentPage, itemsPerPage]);

  const totalPages = Math.ceil(list.length / itemsPerPage);

  // --- HÀNH ĐỘNG CRUD ---

  const toggleActive = async (id) => {
    const userId = localStorage.getItem("user_id");
    const token = localStorage.getItem("token");
    if (!userId || !token) return;

    const currentItem = telegramData.find((d) => d.id === id);
    if (!currentItem) return;

    const newStatus = currentItem.status === 1 ? 0 : 1;

    setTelegramData((prev) =>
      prev.map((item) =>
        item.id === id ? { ...item, status: newStatus } : item
      )
    );

    try {
      await axios.post(
        `${API_BASE_URL}/telegram`,
        new URLSearchParams({
          action: "update",
          user_id: userId,
          telegram_id: id,
          status: newStatus,
        }),
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
            Authorization: `Bearer ${token}`,
          },
        }
      );
    } catch (err) {
      console.error("Lỗi cập nhật trạng thái, đang khôi phục:", err);
      setTelegramData((prev) =>
        prev.map((item) =>
          item.id === id ? { ...item, status: currentItem.status } : item
        )
      );
    }
  };

  const confirmAdd = async () => {
    const userId = localStorage.getItem("user_id");
    const token = localStorage.getItem("token");
    if (!addBankId || !addChatId.trim()) return;

    try {
      await axios.post(
        `${API_BASE_URL}/telegram`,
        new URLSearchParams({
          action: "add",
          user_id: userId,
          user_bank_id: addBankId,
          chat_id: addChatId.trim(),
          type: addEvent,
          name: addDescription.trim(),
        }),
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      setShowAddModal(false);
      callApi({ action: "list" }); // Tải lại danh sách
    } catch (err) {
      console.error("Lỗi khi thêm Telegram:", err);
      alert(
        "Thêm mới thất bại: " + (err.response?.data?.message || err.message)
      );
    }
  };

  const updateTelegram = async () => {
    const userId = localStorage.getItem("user_id");
    const token = localStorage.getItem("token");
    if (!selected) return;

    try {
      await axios.post(
        `${API_BASE_URL}/telegram`,
        new URLSearchParams({
          action: "edit",
          user_id: userId,
          telegram_id: selected.id,
          new_chat_id: editChatId.trim() || selected.chatId,
          new_type: editEvent,
          new_name: editDescription.trim() || selected.description,
        }),
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      setShowEditModal(false);
      callApi({ action: "list" }); // Tải lại danh sách
    } catch (err) {
      console.error("Lỗi khi cập nhật Telegram:", err);
      alert(
        "Cập nhật thất bại: " + (err.response?.data?.message || err.message)
      );
    }
  };

  const confirmDelete = async () => {
    const userId = localStorage.getItem("user_id");
    const token = localStorage.getItem("token");
    if (!deleteTarget) return;

    try {
      await axios.post(
        `${API_BASE_URL}/telegram`,
        new URLSearchParams({
          action: "delete",
          user_id: userId,
          telegram_id: deleteTarget.id,
        }),
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      setShowDeleteModal(false);
      callApi({ action: "list" }); // Tải lại danh sách
    } catch (err) {
      console.error("Lỗi khi xóa Telegram:", err);
      alert("Xóa thất bại: " + (err.response?.data?.message || err.message));
    }
  };

  const openAddModal = () => {
    setAddBankId("");
    setAddEvent("IN");
    setAddChatId("");
    setAddDescription("");
    setSelectedBankName("");
    setShowBankDropdown(false);
    setShowAddModal(true);
  };

  const openEdit = (w) => {
    setSelected(w);
    setEditEvent(w.event);
    setEditChatId("");
    setEditDescription("");
    setShowEditModal(true);
  };

  const openDeleteModal = (w) => {
    setDeleteTarget(w);
    setShowDeleteModal(true);
  };

  const exportToExcel = () => {
    const exportData = list.map((item, idx) => ({
      STT: idx + 1,
      ID: item.id,
      "Tài khoản": item.accountName,
      "Số TK": item.accountNumber,
      "Sự kiện": item.event,
      "Chat ID": item.chatId,
      "Mô tả": item.description,
      "Ngày thêm": item.addedAt,
      "Trạng thái": item.active ? "Hoạt động" : "Không hoạt động",
    }));
    const ws = XLSX.utils.json_to_sheet(exportData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "Telegram");
    ws["!cols"] = [
      { wch: 5 },
      { wch: 8 },
      { wch: 20 },
      { wch: 15 },
      { wch: 15 },
      { wch: 15 },
      { wch: 30 },
      { wch: 20 },
      { wch: 15 },
    ];
    XLSX.writeFile(
      wb,
      `telegram_${new Date().toISOString().split("T")[0]}.xlsx`
    );
  };

  const printTable = () => {
    const printWindow = window.open("", "_blank");
    const htmlContent = `<!DOCTYPE html><html><head><meta charset="utf-8"><title>Danh sách Telegram</title><style>
      body{font-family:Arial,sans-serif;margin:20px}
      table{width:100%;border-collapse:collapse}
      th,td{border:1px solid #ccc;padding:6px;font-size:12px;text-align:left}
      th{background:#f2f2f2}
      .text-center{text-align:center}
      .status-active{color:green;font-weight:bold}
      .status-inactive{color:red;font-weight:bold}
    </style></head><body>
      <h3>DANH SÁCH TELEGRAM</h3>
      <p>Ngày in: ${new Date().toLocaleDateString("vi-VN")}</p>
      <table><thead><tr>
        <th>STT</th><th>ID</th><th>Tài khoản</th><th>Số TK</th><th>Sự kiện</th><th>Chat ID</th><th>Mô tả</th><th>Ngày thêm</th><th>Trạng thái</th>
      </tr></thead><tbody>
      ${list
        .map(
          (item, idx) => `
        <tr>
          <td class="text-center">${idx + 1}</td>
          <td class="text-center">${item.id}</td>
          <td>${item.accountName}</td>
          <td>${item.accountNumber}</td>
          <td class="text-center">${getEventLabel(item.event)}</td>
          <td>${item.chatId}</td>
          <td>${item.description}</td>
          <td>${item.addedAt}</td>
          <td class="text-center ${
            item.active ? "status-active" : "status-inactive"
          }">
            ${item.active ? "Hoạt động" : "Không hoạt động"}
          </td>
        </tr>`
        )
        .join("")}
      </tbody></table>
    </body></html>`;
    printWindow.document.write(htmlContent);
    printWindow.document.close();
    printWindow.focus();
    setTimeout(() => {
      printWindow.print();
      printWindow.close();
    }, 200);
  };

  const handlePageChange = (page) => setCurrentPage(page);
  const handlePrevPage = () => {
    if (currentPage > 1) setCurrentPage(currentPage - 1);
  };
  const handleNextPage = () => {
    if (currentPage < totalPages) setCurrentPage(currentPage + 1);
  };

  return (
    <MasterLayout>
      <Breadcrumb title="Danh sách Telegram" />

      <div className="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center gap-2 mb-3">
        <div className="d-md-flex w-100 gap-2">
          <select
            className="form-select"
            value={accountFilter}
            onChange={(e) => setAccountFilter(e.target.value)}
          >
            <option value="">Tất cả tài khoản</option>
            {Array.from(new Set(data.map((w) => w.accountName))).map((name) => (
              <option key={name} value={name}>
                {name}
              </option>
            ))}
          </select>
          <select
            className="form-select mt-3 mt-md-0"
            value={eventFilter}
            onChange={(e) => setEventFilter(e.target.value)}
          >
            <option value="">Tất cả sự kiện</option>
            {Array.from(new Set(data.map((w) => w.event))).map((ev) => (
              <option key={ev} value={ev}>
                {getEventLabel(ev)}
              </option>
            ))}
          </select>
          <select
            className="form-select mt-3 mt-md-0"
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          >
            <option value="">Tất cả trạng thái</option>
            <option value="Hoạt động">Hoạt động</option>
            <option value="Không hoạt động">Không hoạt động</option>
          </select>
        </div>

        <div className="d-none d-md-flex gap-2 w-100 justify-content-md-end">
          <button className="btn btn-effect btn-sm" onClick={exportToExcel}>
            <Icon className="me-1" icon="mdi:file-excel" /> Excel
          </button>
          <button className="btn btn-effect btn-sm" onClick={printTable}>
            <Icon className="me-1" icon="mdi:file-pdf" /> Print
          </button>
        </div>
      </div>

      <div className="card mb-4">
        <div className="card-header d-flex justify-content-between align-items-center">
          <input
            type="search"
            className="form-control"
            style={{ maxWidth: "300px" }}
            placeholder="Tìm kiếm..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <button className="btn-effect btn btn-sm" onClick={openAddModal}>
            <Icon className="mx-2" icon="solar:add-circle-outline" />
            Thêm Telegram
          </button>
        </div>
        <div className="card-body p-3">
          <div className="table-responsive">
            <table className="table bordered-table mb-0">
              <thead>
                <tr className="text-center">
                  <th>ID</th>
                  <th className="text-center" style={{ width: "150px" }}>
                    Tài khoản
                  </th>
                  <th className="text-center">Sự kiện</th>
                  <th className="text-center">Chat ID</th>
                  <th className="text-center">Mô tả</th>
                  <th className="text-center">Hoạt động</th>
                  <th className="text-center">Trạng thái</th>
                  <th className="text-center">Hành động</th>
                </tr>
              </thead>
              <tbody>
                {telegramLoading ? (
                  <tr>
                    <td colSpan={8} className="text-center p-4">
                      <div
                        className="spinner-border text-primary"
                        role="status"
                      >
                        <span className="visually-hidden">Loading...</span>
                      </div>
                      <div className="mt-2">Đang tải dữ liệu...</div>
                    </td>
                  </tr>
                ) : telegramError ? (
                  <tr>
                    <td colSpan={8} className="text-center p-4 text-danger">
                      Lỗi: {telegramError}
                    </td>
                  </tr>
                ) : paginatedData.length > 0 ? (
                  paginatedData.map((w) => (
                    <tr key={w.id}>
                      <td>{w.id}</td>
                      <td className="text-center" style={{ width: "150px" }}>
                        <img
                          src={w.accountLogo}
                          alt={w.accountName}
                          width={80}
                          className="me-2"
                        />
                        <br />
                        {w.accountNumber}
                      </td>
                      <td className="text-center">
                        <button className="alert alert-primary p-2 small mb-0">
                          {getEventLabel(w.event)}
                        </button>
                      </td>
                      <td className="text-center">{w.chatId}</td>
                      <td>{w.description}</td>
                      <td className="text-center">
                        <label className="custom-switch">
                          <input
                            type="checkbox"
                            checked={w.active}
                            onChange={() => toggleActive(w.id)}
                          />
                          <span className="slider round"></span>
                        </label>
                      </td>
                      <td className="text-center">
                        <span
                          className={`badge ${
                            w.active ? "bg-success" : "bg-danger"
                          } text-white`}
                        >
                          {w.active ? "Hoạt động" : "Tạm dừng"}
                        </span>
                      </td>
                      <td>
                        <div className="d-flex gap-2 justify-content-center">
                          <button
                            className="btn btn-success btn-sm"
                            title="Sửa"
                            onClick={() => openEdit(w)}
                          >
                            <Icon icon="mdi:pencil" />
                          </button>
                          <button
                            className="btn btn-danger btn-sm"
                            title="Xóa"
                            onClick={() => openDeleteModal(w)}
                          >
                            <Icon icon="mdi:delete" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={8} className="text-center p-4">
                      Không có dữ liệu.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>

          <div className="d-flex justify-content-end mt-3">
            <nav>
              <ul className="pagination pagination-sm mb-0">
                <li
                  className={`page-item ${currentPage === 1 ? "disabled" : ""}`}
                  onClick={handlePrevPage}
                >
                  <button className="page-link" disabled={currentPage === 1}>
                    &laquo;
                  </button>
                </li>
                {Array.from({ length: totalPages }, (_, i) => (
                  <li
                    key={i + 1}
                    className={`page-item ${
                      currentPage === i + 1 ? "active" : ""
                    }`}
                    onClick={() => handlePageChange(i + 1)}
                  >
                    <button className="page-link">{i + 1}</button>
                  </li>
                ))}
                <li
                  className={`page-item ${
                    currentPage === totalPages ? "disabled" : ""
                  }`}
                  onClick={handleNextPage}
                >
                  <button
                    className="page-link"
                    disabled={currentPage === totalPages}
                  >
                    &raquo;
                  </button>
                </li>
              </ul>
            </nav>
          </div>
        </div>
      </div>

      {showAddModal && (
        <>
          <div className="modal-backdrop fade show"></div>
          <div className="modal fade show" style={{ display: "block" }}>
            <div className="modal-dialog modal-dialog-centered modal-lg">
              <div className="modal-content">
                <div className="modal-header bg-success text-white justify-content-between">
                  <p className="m-0">Thêm Telegram</p>
                  <button
                    type="button"
                    className="btn-close"
                    onClick={() => setShowAddModal(false)}
                  ></button>
                </div>
                <div className="modal-body">
                  <div className="mb-3">
                    <label className="form-label">Chọn tài khoản</label>
                    <div className="position-relative">
                      <div
                        className="form-select"
                        style={{ cursor: "pointer" }}
                        onClick={() => setShowBankDropdown(!showBankDropdown)}
                      >
                        {selectedBankName || "Chọn tài khoản ngân hàng"}
                      </div>
                      {showBankDropdown && (
                        <div
                          className="dropdown-menu show w-100"
                          style={{ maxHeight: "200px", overflowY: "auto" }}
                        >
                          {allBankAccounts.map((bank) => (
                            <button
                              key={bank.id}
                              type="button"
                              className="dropdown-item"
                              onClick={() => {
                                setAddBankId(bank.id);
                                setSelectedBankName(bank.displayName);
                                setShowBankDropdown(false);
                              }}
                            >
                              <span>
                                {bank.displayName.includes("(VA)") ? (
                                  <>
                                    {bank.displayName.replace(" (VA)", "")}
                                    <span className="badge bg-success ms-2">
                                      VA
                                    </span>
                                  </>
                                ) : (
                                  bank.displayName
                                )}
                              </span>
                            </button>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="mb-3">
                    <label className="form-label">Sự kiện</label>
                    <select
                      className="form-select"
                      value={addEvent}
                      onChange={(e) => setAddEvent(e.target.value)}
                    >
                      <option value="IN">Nhận tiền</option>
                      <option value="OUT">Gửi tiền</option>
                      <option value="ALL">Gửi và nhận</option>
                    </select>
                  </div>
                  <div className="mb-3">
                    <label className="form-label">
                      Telegram chat ID <span className="text-danger">*</span>
                    </label>
                    <input
                      type="text"
                      className="form-control"
                      value={addChatId}
                      onChange={(e) => setAddChatId(e.target.value)}
                    />
                  </div>
                  <div className="mb-3">
                    <label className="form-label">Mô tả</label>
                    <input
                      type="text"
                      className="form-control"
                      value={addDescription}
                      onChange={(e) => setAddDescription(e.target.value)}
                    />
                  </div>
                </div>
                <div className="modal-footer">
                  <button
                    className="btn btn-success"
                    onClick={confirmAdd}
                    disabled={!addBankId || !addChatId}
                  >
                    Thêm
                  </button>
                  <button
                    className="btn btn-secondary"
                    onClick={() => setShowAddModal(false)}
                  >
                    Đóng
                  </button>
                </div>
              </div>
            </div>
          </div>
        </>
      )}

      {showEditModal && selected && (
        <>
          <div className="modal-backdrop fade show"></div>
          <div className="modal fade show" style={{ display: "block" }}>
            <div className="modal-dialog modal-dialog-centered">
              <div className="modal-content">
                <div className="modal-header bg-success text-white justify-content-between">
                  <p className="m-0">Sửa Telegram</p>
                  <button
                    type="button"
                    className="btn-close"
                    onClick={() => setShowEditModal(false)}
                  ></button>
                </div>
                <div className="modal-body">
                  <div className="mb-3">
                    <label className="form-label">Tài khoản</label>
                    <input
                      type="text"
                      readOnly
                      className="form-control-plaintext"
                      value={`${selected.accountNumber}`}
                    />
                  </div>
                  <div className="mb-3">
                    <label className="form-label">Sự kiện</label>
                    <select
                      className="form-select"
                      value={editEvent}
                      onChange={(e) => setEditEvent(e.target.value)}
                    >
                      <option value="IN">Nhận</option>
                      <option value="OUT">Gửi</option>
                      <option value="ALL">Gửi và nhận</option>
                    </select>
                  </div>
                  <div className="mb-3">
                    <label className="form-label">Mô tả</label>
                    <input
                      type="text"
                      className="form-control"
                      placeholder={`Giữ nguyên: ${selected.description}`}
                      value={editDescription}
                      onChange={(e) => setEditDescription(e.target.value)}
                    />
                  </div>
                </div>
                <div className="modal-footer">
                  <button className="btn btn-success" onClick={updateTelegram}>
                    Cập nhật
                  </button>
                  <button
                    className="btn btn-secondary"
                    onClick={() => setShowEditModal(false)}
                  >
                    Đóng
                  </button>
                </div>
              </div>
            </div>
          </div>
        </>
      )}

      {showDeleteModal && deleteTarget && (
        <>
          <div className="modal-backdrop fade show"></div>
          <div className="modal fade show" style={{ display: "block" }}>
            <div className="modal-dialog modal-dialog-centered">
              <div className="modal-content">
                <div className="modal-header">
                  <h5 className="modal-title">Xác nhận xóa</h5>
                  <button
                    type="button"
                    className="btn-close"
                    onClick={() => setShowDeleteModal(false)}
                  ></button>
                </div>
                <div className="modal-body">
                  Bạn có chắc chắn muốn xóa cấu hình Telegram cho tài khoản{" "}
                  <strong>{deleteTarget.accountNumber}</strong>?
                </div>
                <div className="modal-footer">
                  <button className="btn btn-danger" onClick={confirmDelete}>
                    Xóa
                  </button>
                  <button
                    className="btn btn-secondary"
                    onClick={() => setShowDeleteModal(false)}
                  >
                    Hủy
                  </button>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </MasterLayout>
  );
};

export default TelegramPage;
