/* Global Loading Bar - giống evonhub.dev */
.global-loading-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: transparent;
  z-index: 9999;
  overflow: hidden;
}

.global-loading-progress {
  height: 100%;
  background: linear-gradient(90deg, 
    var(--success-main, #2b7c51) 0%, 
    var(--success-hover, #009f5e) 50%, 
    var(--success-main, #2b7c51) 100%
  );
  background-size: 200% 100%;
  animation: loadingShimmer 1.5s ease-in-out infinite;
  transition: width 0.3s ease;
  border-radius: 0 3px 3px 0;
  box-shadow: 0 0 10px rgba(43, 124, 81, 0.3);
}

@keyframes loadingShimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Responsive */
@media (max-width: 768px) {
  .global-loading-bar {
    height: 2px;
  }
}
