import React from "react";
import heroImg from "../../assets/landing/img/hero/hero_img_4_1.png";

const CongThanhToanHero = () => (
  <section className="th-hero-wrapper hero-4" id="hero">
    <div className="hero-img tilt-active">
      <img src={heroImg} alt="Hero Image" />
    </div>
    <div className="container">
      <div className="hero-style4">
        <div className="ripple-shape">
          <span className="ripple-1"></span>
          <span className="ripple-2"></span>
          <span className="ripple-3"></span>
          <span className="ripple-4"></span>
          <span className="ripple-5"></span>
          <span className="ripple-6"></span>
        </div>
        <span className="hero-subtitle">Giải pháp thanh toán hiện đại</span>
        <h1 className="hero-title">Tự Động <PERSON></h1>
        <h1 className="hero-title">Giao <PERSON>ịch Trong Tích Tắc</h1>
        <p className="hero-text">
          Kết nối API với ngân hàng và ví điện tử, xử lý biến động số dư theo
          thời gian thực, tích hợp dễ dàng vào website, app hoặc hệ thống quản
          lý bán hàng của bạn.
        </p>
        <div className="btn-group">
          <a href="#anchor-sec-title" className="th-btn">
            Tìm hiểu thêm
          </a>
        </div>
      </div>
    </div>
    <div className="triangle-1"></div>
    <div className="triangle-2"></div>
  </section>
);

export default CongThanhToanHero;
