import React, { useEffect, useState } from "react";
import { Icon } from "@iconify/react/dist/iconify.js";
import { <PERSON> } from "react-router-dom";
import useBankApi from "../../callapi/Bank.jsx";

import { bankLogos } from "../../client/ImportImage";

const bankAssets = {
  ACB: { bg: bankLogos.bgAcb, logo: bankLogos.logoAcbWhite },
  BIDV: { bg: bankLogos.bgBidv, logo: bankLogos.logoBidvWhite },
  MBB: { bg: bankLogos.bgMbb, logo: bankLogos.logoMbbWhite },
  MOMO: { bg: bankLogos.bgMomo, logo: bankLogos.logoMomoWhite },
  SEAB: { bg: bankLogos.bgSeab, logo: bankLogos.logoSeabWhite },
  TCB: { bg: bankLogos.bgTcb, logo: bankLogos.logoTcbWhite },
  TPB: { bg: bankLogos.bgTpb, logo: bankLogos.logoTpbWhite },
  VCB: { bg: bankLogos.bgVcb, logo: bankLogos.logoVcbWhite },
  VTB: { bg: bankLogos.bgVtb, logo: bankLogos.logoVtbWhite },
};

const BankListUser = () => {
  const [groupedBanks, setGroupedBanks] = useState([]);
  const {
    data: apiData,
    loading,
    error,
    callApi: getBankAccounts,
  } = useBankApi();

  useEffect(() => {
    const userId = localStorage.getItem("user_id");
    if (userId) {
      getBankAccounts({ action: "bank_account", user_id: userId });
    }
  }, [getBankAccounts]);

  useEffect(() => {
    if (apiData && apiData.banks && Array.isArray(apiData.banks)) {
      const grouped = apiData.banks.reduce((accumulator, currentAccount) => {
        const key = currentAccount.shortBankName;
        if (!key) return accumulator;
        if (!accumulator[key]) {
          accumulator[key] = {
            bankName: key,
            accounts: [],
            assets: bankAssets[key] || {},
          };
        }
        accumulator[key].accounts.push(currentAccount);
        return accumulator;
      }, {});
      setGroupedBanks(Object.values(grouped));
    }
  }, [apiData]);

  return (
    <div className="container-fluid mt-4">
      <div className="d-flex justify-content-between align-items-center mb-3">
        <Link to="/client/add-bank" className="btn btn-success">
          + Thêm tài khoản
        </Link>
      </div>

      {loading && <p className="text-center">Đang tải...</p>}
      {error && <p className="text-center text-danger">Lỗi: {error}</p>}

      {!loading && !error && (
        <div className="row g-3">
          {groupedBanks.map((bankGroup) => {
            const assets = bankGroup.assets;
            return (
              <div
                className="col-lg-3 col-md-4 col-sm-6 col-12"
                key={bankGroup.bankName}
              >
                <div className="p-20 h-200-px radius-8 overflow-hidden position-relative z-1 d-flex flex-column justify-content-between text-white">
                  <Link
                    to={`/client/account-bank/${bankGroup.bankName}`}
                    className="text-white"
                  >
                    <img
                      src={assets.bg}
                      alt={`${bankGroup.bankName} background`}
                      className="position-absolute start-0 top-0 w-100 h-100 object-fit-cover z-n1"
                    />

                    <div>
                      <img
                        src={assets.logo}
                        alt={`${bankGroup.bankName} logo`}
                        style={{
                          height: "32px",
                          maxWidth: "150px",
                        }}
                      />
                    </div>
                    <div className="h-50"></div>
                    <div>
                      <span className="text-xs fw-normal text-opacity-75">
                        {bankGroup.accounts.every((acc) => acc.status === 1)
                          ? "Đang hoạt động"
                          : "Có tài khoản ngưng"}
                      </span>
                      <div className="d-flex align-items-center mt-1">
                        <h6 className="text-white text-xl fw-semibold mb-0">
                          {bankGroup.accounts.length} tài khoản
                        </h6>

                        <Icon
                          icon="ph:arrow-circle-right-bold"
                          className="icon fs-1"
                        />
                      </div>
                    </div>
                  </Link>
                </div>
              </div>
            );
          })}
          {groupedBanks.length === 0 && !loading && (
            <div className="col-12">
              <p className="text-center text-muted p-5">
                Chưa có tài khoản ngân hàng nào được liên kết.
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default BankListUser;
