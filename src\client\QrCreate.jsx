import MasterLayout from "../masterLayout/MasterLayout";
import Breadcrumb from "../components/Breadcrumb";
import React, { useState, useEffect, useRef } from "react";
import { Icon } from "@iconify/react/dist/iconify.js";
import useBankApi from "../callapi/Bank.jsx";
import { bankLogos } from "./ImportImage";

const QrCreate = () => {
  const [bankAccounts, setBankAccounts] = useState([]);
  const [selectedBank, setSelectedBank] = useState("");
  const [accountNumber, setAccountNumber] = useState("");
  const [accountName, setAccountName] = useState("");
  const [amount, setAmount] = useState("");
  const [memo, setMemo] = useState("");
  const [qrUrl, setQrUrl] = useState("");
  const [qrImageUrl, setQrImageUrl] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [bankSearchTerm, setBankSearchTerm] = useState("");
  const [showBankDropdown, setShowBankDropdown] = useState(false);
  const [isMask, setIsMask] = useState(false); // che số tài khoản
  const [frameType, setFrameType] = useState("vietqr"); // khung: "vietqr" hoặc "none"
  const abortControllerRef = useRef(null);

  const {
    data: bankData,
    loading: bankLoading,
    callApi: getBankAccounts,
  } = useBankApi();

  // Bank assets mapping
  const bankAssets = {
    ACB: { bg: bankLogos.logoAcb },
    BIDV: { bg: bankLogos.logoBidv },
    MBB: { bg: bankLogos.logoMbb },
    SEAB: { bg: bankLogos.logoSeab },
    TCB: { bg: bankLogos.logoTcb },
    VCB: { bg: bankLogos.logoVcb },
    VTB: { bg: bankLogos.logoVtb },
  };

  // Fetch bank accounts on component mount
  useEffect(() => {
    const fetchBankAccounts = async () => {
      try {
        const userId = localStorage.getItem("user_id");
        if (userId) {
          await getBankAccounts({
            action: "bank_account",
            user_id: userId,
          });
        }
      } catch (error) {
        console.error("Error fetching bank accounts:", error);
      }
    };

    fetchBankAccounts();
  }, [getBankAccounts]);

  // Process bank data when received
  useEffect(() => {
    if (bankData && bankData.banks) {
      const processedBanks = [];

      bankData.banks.forEach((bank) => {
        // Add main account
        processedBanks.push({
          ...bank,
          displayName: `${bank.accountNumber} - ${bank.bankName}`,
          accountType: "main",
        });

        // Add VA accounts if they exist and are different from main account
        if (bank.vaNumber && bank.vaNumber !== bank.accountNumber) {
          processedBanks.push({
            ...bank,
            accountNumber: bank.vaNumber,
            displayName: `${bank.vaNumber} - ${bank.bankName} (VA)`,
            accountType: "va",
          });
        }

        // Add VA accounts from vaAccounts array if exists
        if (bank.vaAccounts && bank.vaAccounts.length > 0) {
          bank.vaAccounts.forEach((vaAccount) => {
            processedBanks.push({
              ...vaAccount,
              displayName: `${vaAccount.vaNumber} - ${vaAccount.bankName} (VA)`,
              accountType: "va",
              parentAccount: bank,
            });
          });
        }
      });

      setBankAccounts(processedBanks);
    }
  }, [bankData]);

  // Filter bank accounts based on search term
  const filteredBankAccounts = bankSearchTerm.trim()
    ? bankAccounts.filter((bank) => {
        const searchLower = bankSearchTerm.toLowerCase();
        const accountNumber =
          bank.accountNumber?.toString().toLowerCase() || "";
        const bankName = bank.bankName?.toLowerCase() || "";

        return (
          accountNumber.includes(searchLower) || bankName.includes(searchLower)
        );
      })
    : bankAccounts;

  // Handle bank selection
  const selectBank = (bank) => {
    setSelectedBank(bank);
    setAccountNumber(bank.accountNumber);
    setAccountName(bank.name || bank.username || "");
    setBankSearchTerm(bank.displayName);
    setShowBankDropdown(false);
  };

  // Handle bank search input change
  const handleBankSearchChange = (e) => {
    const value = e.target.value;
    setBankSearchTerm(value);
    setSelectedBank("");
    setAccountNumber("");
    setAccountName("");
    setShowBankDropdown(true);
  };

  // Generate QR Code automatically on input change
  useEffect(() => {
    // Only generate if đã chọn tài khoản và có số tài khoản
    if (!selectedBank || !accountNumber) {
      setQrUrl("");
      setQrImageUrl("");
      return;
    }
    setIsGenerating(true);
    setQrImageUrl("");
    try {
      const baseUrl = "https://payment.pay2s.vn/quicklink";
      const bankName = selectedBank.shortBankName || "BIDV";
      const encodedAccountName = encodeURIComponent(accountName || "");
      // Build params dynamically
      const params = new URLSearchParams();
      if (amount) params.set("amount", amount);
      if (memo) params.set("memo", memo);
      if (isMask) params.set("is_mask", "1");
      else params.set("is_mask", "0");
      if (frameType === "vietqr") params.set("bg", "0");
      // Nếu không chọn khung thì không set bg
      const url = `${baseUrl}/${bankName}/${accountNumber}/${encodedAccountName}?${params.toString()}`;
      setQrUrl(url);
      setQrImageUrl(url);
    } catch (error) {
      // Không alert khi nhập, chỉ log
      if (error && error.name !== "AbortError") {
        console.error("Error generating QR:", error);
      }
    } finally {
      setIsGenerating(false);
    }
    // eslint-disable-next-line
  }, [
    selectedBank,
    accountNumber,
    accountName,
    amount,
    memo,
    isMask,
    frameType,
  ]);

  // Download QR image
  const downloadQR = () => {
    if (!qrImageUrl) return;

    // Open the payment link in new tab since it's a pay2s URL
    window.open(qrImageUrl, "_blank");
  };

  // Copy QR URL to clipboard
  const copyQRUrl = async () => {
    if (!qrUrl) return;

    try {
      await navigator.clipboard.writeText(qrUrl);
      alert("Đã sao chép URL vào clipboard");
    } catch (error) {
      console.error("Error copying to clipboard:", error);
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showBankDropdown && !event.target.closest(".position-relative")) {
        setShowBankDropdown(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [showBankDropdown]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return (
    <>
      {/* MasterLayout */}
      <MasterLayout>
        <div className="card">
          <div className="card-header">
            <h5 className="card-title mb-0">
              <Icon icon="mdi:qrcode" className="me-2" />
              Tạo mã QR thanh toán
            </h5>
          </div>
          <div className="card-body">
            <div className="row">
              {/* Left side - Form inputs */}
              <div className="col-md-6">
                <div className="mb-3">
                  <div className="row g-2 align-items-end">
                    <div className="col-6">
                      <label className="form-label fw-medium">Chọn khung</label>
                      <select
                        className="form-select"
                        value={frameType}
                        onChange={(e) => setFrameType(e.target.value)}
                      >
                        <option value="none">Không hiển thị khung</option>
                        <option value="vietqr">Khung VietQR</option>
                      </select>
                    </div>
                    <div
                      className="col-6 d-flex align-items-center"
                      style={{ height: "100%" }}
                    >
                      <div className="form-check ms-2">
                        <input
                          className="form-check-input"
                          type="checkbox"
                          id="maskAccountCheckbox"
                          checked={isMask}
                          onChange={(e) => setIsMask(e.target.checked)}
                        />
                        <label
                          className="form-check-label ms-1"
                          htmlFor="maskAccountCheckbox"
                        >
                          Che số tài khoản
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="mb-3">
                  <label className="form-label fw-medium">
                    Chọn tài khoản ngân hàng{" "}
                    <span className="text-danger">*</span>
                  </label>
                  <div className="position-relative">
                    <input
                      type="text"
                      className="form-control"
                      placeholder="Tìm kiếm theo STK hoặc tên ngân hàng..."
                      value={bankSearchTerm}
                      onChange={handleBankSearchChange}
                      onFocus={() => setShowBankDropdown(true)}
                    />
                    {showBankDropdown && filteredBankAccounts.length > 0 && (
                      <div
                        className="dropdown-menu show position-absolute w-100"
                        style={{
                          zIndex: 1050,
                          maxHeight: "200px",
                          overflowY: "auto",
                        }}
                      >
                        {bankLoading ? (
                          <div className="dropdown-item-text">Đang tải...</div>
                        ) : (
                          filteredBankAccounts.map((bank, index) => (
                            <button
                              key={`${bank.id}-${bank.accountType}-${index}`}
                              type="button"
                              className="dropdown-item text-start d-flex align-items-center"
                              onClick={() => selectBank(bank)}
                            >
                              {bank.shortBankName &&
                                bankAssets[bank.shortBankName] && (
                                  <img
                                    src={bankAssets[bank.shortBankName].bg}
                                    alt={bank.bankName}
                                    width="30"
                                    height="30"
                                    className="me-2"
                                  />
                                )}
                              <div>
                                <div className="fw-medium">
                                  {bank.accountNumber}
                                </div>
                                <small className="text-muted">
                                  {bank.bankName}
                                  {bank.accountType === "va" && (
                                    <span className="badge bg-primary ms-1">
                                      VA
                                    </span>
                                  )}
                                </small>
                              </div>
                            </button>
                          ))
                        )}
                      </div>
                    )}
                  </div>
                </div>

                <div className="mb-3">
                  <label className="form-label fw-medium">Tên tài khoản</label>
                  <input
                    type="text"
                    className="form-control"
                    value={accountName}
                    onChange={(e) => setAccountName(e.target.value)}
                    placeholder="Nhập tên chủ tài khoản"
                  />
                </div>

                <div className="mb-3">
                  <label className="form-label fw-medium">Số tiền (VNĐ)</label>
                  <input
                    type="number"
                    className="form-control"
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                    placeholder="Nhập số tiền (để trống nếu không cố định)"
                  />
                </div>

                <div className="mb-3">
                  <label className="form-label fw-medium">
                    Nội dung chuyển khoản
                  </label>
                  <textarea
                    className="form-control"
                    rows="3"
                    value={memo}
                    onChange={(e) => setMemo(e.target.value)}
                    placeholder="Nhập nội dung chuyển khoản (để trống nếu không có)"
                  />
                </div>

                {/* Nút tạo mã QR đã được loại bỏ, QR sẽ tự động sinh khi nhập */}
              </div>

              {/* Right side - QR display */}
              <div className="col-md-6">
                <div className="text-center">
                  <h6 className="mb-3">Mã QR thanh toán</h6>

                  {isGenerating ? (
                    <div
                      className="d-flex flex-column align-items-center justify-content-center"
                      style={{ minHeight: "300px" }}
                    >
                      <div
                        className="spinner-border text-primary mb-3"
                        style={{ width: "3rem", height: "3rem" }}
                      />
                      <p className="text-muted">Đang tạo mã QR...</p>
                    </div>
                  ) : qrImageUrl ? (
                    <div>
                      <div
                        className="border rounded p-3 mb-3 bg-light d-flex justify-content-center align-items-center"
                        style={{ minHeight: "500px", height: "100%" }}
                      >
                        <img
                          src={qrImageUrl}
                          alt="QR Code Payment"
                          className="img-fluid rounded shadow"
                          style={{
                            width: "100%",
                            height: "auto",
                            maxWidth: "600px",
                            objectFit: "contain",
                            display: "block",
                          }}
                        />
                      </div>

                      <div className="d-flex gap-2 justify-content-center mb-3">
                        <button
                          className="btn btn-success btn-sm"
                          onClick={downloadQR}
                        >
                          <Icon icon="mdi:open-in-new" className="me-1" />
                          Mở link
                        </button>
                        <button
                          className="btn btn-info btn-sm"
                          onClick={copyQRUrl}
                        >
                          <Icon icon="mdi:content-copy" className="me-1" />
                          Sao chép URL
                        </button>
                      </div>

                      {qrUrl && (
                        <div className="text-start">
                          <small className="text-muted">URL thanh toán:</small>
                          <div className="border rounded p-2 bg-light">
                            <small className="text-break">{qrUrl}</small>
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div
                      className="d-flex flex-column align-items-center justify-content-center text-muted"
                      style={{ minHeight: "300px" }}
                    >
                      <Icon
                        icon="mdi:qrcode"
                        style={{ fontSize: "80px" }}
                        className="mb-3 opacity-50"
                      />
                      <p>Chọn tài khoản và nhấn "Tạo mã QR" để bắt đầu</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </MasterLayout>
    </>
  );
};

export default QrCreate;
