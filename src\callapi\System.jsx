import { useState, useCallback } from "react";
import axios from "axios";
import { API_BASE_URL } from "./apiConfig";

// Hook dành riêng cho các API liên quan đến System
const useSystemApi = () => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const callApi = useCallback(async (body) => {
    setLoading(true);
    setError(null);
    setData(null);

    // Chỉ thay đổi endpoint ở đây
    const API_ENDPOINT = `${API_BASE_URL}/system`;

    try {
      const token = localStorage.getItem("token");
      const headers = { "Content-Type": "application/x-www-form-urlencoded" };

      if (token) {
        headers["Authorization"] = `Bearer ${token}`;
      } else {
        throw new Error("Không tìm thấy token xác thực. Vui lòng đăng nhập.");
      }

      const requestBody = new URLSearchParams(body);
      const response = await axios.post(API_ENDPOINT, requestBody, { headers });
      const result = response.data;

      if (result.status === true) {
        setData(result);
      } else {
        setError(result.message || "API trả về lỗi.");
      }
    } catch (err) {
      const errorMessage = err.response
        ? `${err.response.status}: ${err.response.statusText}`
        : err.message;
      setError(errorMessage);
      console.error("Lỗi khi gọi System API:", err);
    } finally {
      setLoading(false);
    }
  }, []);

  return { data, loading, error, callApi };
};

export default useSystemApi;
