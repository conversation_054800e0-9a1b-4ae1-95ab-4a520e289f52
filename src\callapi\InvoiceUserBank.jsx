import { useState, useCallback } from "react"; // 1. Import thêm useCallback
import axios from "axios";
import { API_BASE_URL } from "./apiConfig";

// Đ<PERSON>i tên hook thành useInvoiceApi cho đúng chuẩn React
const useInvoiceApi = () => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // 2. Bọc hàm callApi trong useCallback
  const callApi = useCallback(async (body) => {
    setLoading(true);
    setError(null);
    setData(null);

    const API_ENDPOINT = `${API_BASE_URL}/invoices`;

    try {
      const token = localStorage.getItem("token");
      const headers = { "Content-Type": "application/x-www-form-urlencoded" };
      if (token) {
        headers["Authorization"] = `Bearer ${token}`;
      } else {
        throw new Error("Không tìm thấy token xác thực. Vui lòng đăng nhập.");
      }
      const requestBody = new URLSearchParams(body);
      const response = await axios.post(API_ENDPOINT, requestBody, { headers });
      const result = response.data;
      if (result.status === true) {
        setData(result);
      } else {
        setError(result.message || "API trả về lỗi.");
      }
    } catch (err) {
      const errorMessage = err.response
        ? `${err.response.status}: ${err.response.statusText}`
        : err.message;
      setError(errorMessage);
      console.error("Lỗi khi gọi API:", err);
    } finally {
      setLoading(false);
    }
  }, []); // 3. Thêm mảng phụ thuộc rỗng `[]`

  return { data, loading, error, callApi };
};

// Export với tên mới
export default useInvoiceApi;
