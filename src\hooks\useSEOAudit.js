import { useState, useEffect } from "react";
import { seoAudit } from "../utils/seoAudit";

export const useSEOAudit = (pageKey) => {
  const [auditResults, setAuditResults] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const runAudit = async () => {
      setLoading(true);
      
      const results = {
        metaTags: seoAudit.checkMetaTags(pageKey),
        pageSpeed: seoAudit.checkPageSpeed(),
        timestamp: new Date().toISOString()
      };
      
      setAuditResults(results);
      setLoading(false);
      
      // Log to console for development
      console.log(`SEO Audit for ${pageKey}:`, results);
    };

    runAudit();
  }, [pageKey]);

  return { auditResults, loading };
};