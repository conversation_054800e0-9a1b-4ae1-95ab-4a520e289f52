import React, { useState, useEffect } from 'react';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { Helmet } from 'react-helmet-async';
import Header from '../blog/Header';
import Footer from '../landing/Footer';
import './BlogLayoutWrapper.css';

const BlogLayoutWrapper = () => {
  const [isContentLoading, setIsContentLoading] = useState(false);
  const [contentKey, setContentKey] = useState(0);
  const location = useLocation();

  // Handle route changes - simulate evonhub.dev behavior
  useEffect(() => {
    // Start loading animation
    setIsContentLoading(true);
    setContentKey(prev => prev + 1);
    
    // Simulate content loading time (like evonhub.dev)
    const timer = setTimeout(() => {
      setIsContentLoading(false);
    }, 300); // Short loading time for smooth UX

    return () => clearTimeout(timer);
  }, [location.pathname]);

  // Content transition variants (similar to evonhub.dev)
  const contentVariants = {
    initial: {
      opacity: 0,
      y: 10,
      scale: 0.99
    },
    animate: {
      opacity: 1,
      y: 0,
      scale: 1
    },
    exit: {
      opacity: 0,
      y: -5,
      scale: 1.01
    }
  };

  const contentTransition = {
    type: "tween",
    ease: [0.25, 0.46, 0.45, 0.94], // Custom easing like evonhub
    duration: 0.25
  };

  return (
    <>
      <Helmet>
        <title>Blog Pay2S - Tin tức và kiến thức thanh toán điện tử</title>
        <meta name="description" content="Cập nhật tin tức mới nhất về thanh toán điện tử, fintech và các giải pháp ngân hàng số từ Pay2S" />
      </Helmet>

      {/* Fixed Header - Never reloads (like evonhub.dev) */}
      <Header />

      {/* Content Loading Bar (like evonhub.dev top loading bar) */}
      {isContentLoading && (
        <div className="content-loading-bar">
          <div className="loading-progress"></div>
        </div>
      )}

      {/* Dynamic Content Area - Only this part updates */}
      <main className="blog-main-wrapper">
        <div className="blog-content-container">
          {/* Loading Overlay */}
          {isContentLoading && (
            <div className="content-loading-overlay">
              <div className="loading-spinner">
                <div className="spinner-ring"></div>
                <div className="spinner-ring"></div>
                <div className="spinner-ring"></div>
              </div>
            </div>
          )}

          {/* Animated Content */}
          <AnimatePresence mode="wait">
            <motion.div
              key={contentKey}
              initial="initial"
              animate="animate"
              exit="exit"
              variants={contentVariants}
              transition={contentTransition}
              className="content-wrapper"
            >
              <Outlet />
            </motion.div>
          </AnimatePresence>
        </div>
      </main>

      {/* Fixed Footer - Never reloads */}
      <Footer />
    </>
  );
};

// Custom hook for smooth navigation (like evonhub.dev)
export const useSmoothNavigation = () => {
  const navigate = useNavigate();
  const [isNavigating, setIsNavigating] = useState(false);

  const navigateTo = (path, options = {}) => {
    setIsNavigating(true);
    
    // Add small delay for smooth UX (like evonhub.dev)
    setTimeout(() => {
      navigate(path, options);
      setIsNavigating(false);
    }, 50);
  };

  return {
    navigateTo,
    isNavigating
  };
};

// Loading component for content
export const ContentLoader = ({ isLoading, children, message = "Đang tải..." }) => {
  if (isLoading) {
    return (
      <div className="content-loader">
        <div className="loader-content">
          <div className="loader-spinner">
            <div className="spinner-dot"></div>
            <div className="spinner-dot"></div>
            <div className="spinner-dot"></div>
          </div>
          <p className="loader-message">{message}</p>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
    >
      {children}
    </motion.div>
  );
};

export default BlogLayoutWrapper;
