import React from "react";
// Import Swiper React components
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination, Autoplay } from "swiper/modules";

// Import Swiper styles
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";

// Import technology logos
import androidLogo from "../../assets/landing/img/logo/android.png";
import phpLogo from "../../assets/landing/img/logo/php.png";
import nodejsLogo from "../../assets/landing/img/logo/nodejs.png";
import woocommerceLogo from "../../assets/landing/img/logo/woocommerce-logo2.png";
import whmcsLogo from "../../assets/landing/img/logo/whmcs-logo.png";
import hostbillLogo from "../../assets/landing/img/logo/logo_big2.png";
import checkIcon from "../../assets/landing/img/icon/check_1.png";

// Import service icons
import serviceIcon1 from "../../assets/landing/img/icon/service_card_1.svg";
import serviceIcon2 from "../../assets/landing/img/icon/service_card_2.svg";
import serviceIcon3 from "../../assets/landing/img/icon/service_card_3.svg";
import serviceIcon5 from "../../assets/landing/img/icon/service_card_5.svg";
import serviceBg from "../../assets/landing/img/bg/service_grid_bg.png";

// Import background and theme images
import serviceBg2 from "../../assets/landing/img/bg/service_bg_2.jpg";
import titleShape2 from "../../assets/landing/img/theme-img/title_shape_2.svg";

const Solution = () => {
  // Technology platforms data
  const techPlatforms = [
    {
      id: 1,
      name: "Android",
      logo: androidLogo,
      alt: "Android",
    },
    {
      id: 2,
      name: "PHP",
      logo: phpLogo,
      alt: "PHP",
    },
    {
      id: 3,
      name: "NodeJS",
      logo: nodejsLogo,
      alt: "NodeJS",
    },
  ];

  // E-commerce plugins data
  const ecommercePlugins = [
    {
      id: 1,
      name: "WooCommerce",
      logo: woocommerceLogo,
      alt: "WooCommerce",
    },
    {
      id: 2,
      name: "WHMCS",
      logo: whmcsLogo,
      alt: "WHMCS",
    },
    {
      id: 3,
      name: "HostBill",
      logo: hostbillLogo,
      alt: "HostBill",
    },
  ];

  // Features checklist data
  const features = [
    {
      id: 1,
      text: "Tích hợp cho nhiều nền tảng",
    },
    {
      id: 2,
      text: "Tài liệu webhook, API",
    },
    {
      id: 3,
      text: "Tương thích Web, App, POS ...",
    },
    {
      id: 4,
      text: "Tạo QR Code",
    },
  ];

  // Services data
  const services = [
    {
      id: 1,
      title: "Tự động quy trình",
      description:
        "Tự động xác nhận đơn hàng, nhận thông báo chuyển tiền từ ngân hàng và ví điện tử.",
      icon: serviceIcon1,
      link: "service-details.html",
    },
    {
      id: 2,
      title: "Đa dạng kết nối",
      description:
        "Tích hợp webhook vào chatbot, website, CRM, ERP chỉ với 3 bước đơn giản.",
      icon: serviceIcon2,
      link: "service-details.html",
    },
    {
      id: 3,
      title: "Sẵn sàng tích hợp",
      description:
        "Có sẵn cho WooCommerce, WHMCS, Hostbill, tích hợp chỉ trong vài bước.",
      icon: serviceIcon3,
      link: "service-details.html",
    },
    {
      id: 4,
      title: "Đa kênh, tiện dụng",
      description:
        "Hỗ trợ đa ngân hàng: VCB, MB, BIDV, ACB, Techcombank, MoMo, v.v.",
      icon: serviceIcon5,
      link: "service-details.html",
    },
  ];

  return (
    <>
      <div className="row-tech">
        <div className="container space-bottom">
          <div className="row align-items-center flex-row-reverse">
            <div className="col-lg-6 mb-30 mb-lg-0">
              <div className="container py-5">
                {/* Technology Platforms */}
                <div className="mb-5">
                  <div className="row text-center">
                    {techPlatforms.map((platform) => (
                      <div key={platform.id} className="col-4 col-md-4 mb-4">
                        <img
                          src={platform.logo}
                          alt={platform.alt}
                          className="tech-logo"
                          onError={(e) => {
                            e.target.style.display = "none";
                            console.warn(
                              `Failed to load image: ${platform.name}`
                            );
                          }}
                        />
                      </div>
                    ))}
                  </div>
                </div>

                {/* E-commerce Plugins */}
                <div className="mb-4">
                  <div className="row text-center">
                    {ecommercePlugins.map((plugin) => (
                      <div key={plugin.id} className="col-4 col-md-4 mb-4">
                        <img
                          src={plugin.logo}
                          alt={plugin.alt}
                          className="tech-logo"
                          onError={(e) => {
                            e.target.style.display = "none";
                            console.warn(
                              `Failed to load image: ${plugin.name}`
                            );
                          }}
                        />
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            <div className="col-lg-6 text-lg-start text-center">
              <div className="title-area mb-35">
                <h2 className="sec-title" id="sec-title">
                  Vấn đề của bạn sẽ được giải quyết
                </h2>
              </div>
              <p className="mt-n2 mb-30">
                Chúng tôi có sẵn plugin, API và webhook cho tất cả các nền tảng
                mà bạn cần
              </p>

              <div className="two-column mb-40 list-center">
                <div className="checklist style2">
                  <ul>
                    {features.slice(0, 2).map((feature) => (
                      <li key={feature.id}>
                        <img src={checkIcon} alt="check icon" />
                        {feature.text}
                      </li>
                    ))}
                  </ul>
                </div>
                <div className="checklist style2">
                  <ul>
                    {features.slice(2, 4).map((feature) => (
                      <li key={feature.id}>
                        <img src={checkIcon} alt="check icon" />
                        {feature.text}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <section
        className="bg-top-center z-index-common space-top space-bottom"
        id="service-sec"
        style={{
          backgroundImage: `url(${serviceBg2})`,
          backgroundRepeat: "no-repeat",
        }}
      >
        <div className="container">
          <div className="row justify-content-lg-between justify-content-center align-items-center">
            <div className="col-lg-6 col-sm-9 pe-xl-5">
              <div className="title-area text-center text-lg-start">
                <div className="shadow-title color2">Solution</div>
                <span className="sub-title">
                  <div className="icon-masking me-2">
                    <span
                      className="mask-icon"
                      style={{
                        maskImage: `url(${titleShape2})`,
                      }}
                    ></span>
                    <img src={titleShape2} alt="shape" />
                  </div>
                  Pay2S làm gì?
                </span>
                <h2 className="sec-title text-white">
                  Pay2S cung cấp giải pháp
                  <span className="text-theme"> API thanh toán realtime</span>
                </h2>
              </div>
            </div>
            <div className="col-auto">
              <div className="sec-btn">
                <a href="/client/signup" className="th-btn style3">
                  Đăng ký ngay
                </a>
              </div>
            </div>
          </div>

          <div className="slider-area">
            <Swiper
              modules={[Navigation, Pagination, Autoplay]}
              spaceBetween={30}
              slidesPerView={1}
              navigation={true}
              pagination={{ clickable: true }}
              autoplay={{
                delay: 3000,
                disableOnInteraction: false,
              }}
              breakpoints={{
                576: {
                  slidesPerView: 1,
                  spaceBetween: 20,
                },
                768: {
                  slidesPerView: 2,
                  spaceBetween: 30,
                },
                992: {
                  slidesPerView: 3,
                  spaceBetween: 30,
                },
                1200: {
                  slidesPerView: 4,
                  spaceBetween: 30,
                },
              }}
              className="th-slider has-shadow"
              id="serviceSlider1"
            >
              {services.map((service) => (
                <SwiperSlide key={service.id}>
                  <div className="service-grid">
                    <div className="service-grid_icon">
                      <img
                        src={service.icon}
                        alt="Icon"
                        onError={(e) => {
                          e.target.style.display = "none";
                          console.warn(
                            `Failed to load service icon: ${service.title}`
                          );
                        }}
                      />
                    </div>
                    <div className="service-grid_content">
                      <h3 className="box-title">
                        <a href={service.link}>{service.title}</a>
                      </h3>
                      <p className="service-grid_text">{service.description}</p>
                      <div className="bg-shape">
                        <img
                          src={serviceBg}
                          alt="bg"
                          onError={(e) => {
                            e.target.style.display = "none";
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </SwiperSlide>
              ))}
            </Swiper>
          </div>
        </div>
      </section>
    </>
  );
};

export default Solution;
