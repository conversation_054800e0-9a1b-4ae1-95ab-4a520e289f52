# VA Modals Components

<PERSON><PERSON><PERSON> mục này chứa các modal components đư<PERSON>c tách ra từ BankAccountDetail.jsx để quản lý các thao tác với tài kho<PERSON>n <PERSON>o (VA).

## Components

### 1. AddVAModal.jsx

- Modal để thêm tài khoản ảo mới cho BIDV OpenAPI
- Auto-fill thông tin từ API GetInfoBIDV
- Hỗ trợ xác nhận OTP nếu cần
- Props: `{ isOpen, onClose, accountNumber, bankShortName }`

### 2. ConfirmDeleteModal.jsx

- Modal xác nhận xóa tài khoản ảo
- Hiển thị cảnh báo và yêu cầu xác nhận
- Props: `{ isOpen, onClose, onConfirm, accountName, loading }`

### 3. ConfirmOtpModal.jsx

- Modal nhập OTP để xác nhận xóa tà<PERSON> (dành cho MBB)
- Props: `{ isOpen, onClose, onSubmit, loading, error }`

### 4. QRCodeModal.jsx

- Modal tạo QR Code thanh toán cho tài khoản ngân hàng
- Props: `{ isOpen, onClose, accountInfo }`
- Features:
  - Tự động tạo link QR theo format Pay2S
  - Tùy chỉnh số tiền, nội dung chuyển khoản
  - Tùy chọn ẩn thông tin tài khoản và nền có màu
  - Sao chép link và mở QR trong tab mới

## Usage

```jsx
import {
  AddVAModal,
  ConfirmDeleteModal,
  ConfirmOtpModal,
  QRCodeModal,
} from "./vamodals";

// Sử dụng trong component
<AddVAModal
  isOpen={isAddVAModalOpen}
  onClose={() => setIsAddVAModalOpen(false)}
  accountNumber={accountNumber}
  bankShortName={bankShortName}
/>;
<QRCodeModal
  isOpen={isQRModalOpen}
  onClose={() => setIsQRModalOpen(false)}
  accountInfo={qrAccountInfo}
/>;
```

## Benefits

- **Tách biệt concerns**: Mỗi modal có một trách nhiệm riêng
- **Dễ bảo trì**: Code được chia nhỏ, dễ debug và sửa lỗi
- **Tái sử dụng**: Có thể import và sử dụng ở nơi khác
- **Clean code**: File chính ngắn gọn hơn, dễ đọc hơn
