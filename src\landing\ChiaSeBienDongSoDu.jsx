import React from "react";
import SEOHead from "../components/SEOHead";
import Header from "./Header";
import Footer from "./Footer";
import usePageLoading from "../hooks/usePageLoading";
import ChiaSeBienDongHero from "./product/ChiaSeBienDongHero";
import OpenApiComparisonTable from "./product/OpenApiComparisonTable";
import BankPartnersSection from "./product/BankPartnersSection";
import OpenApiCta from "./product/OpenApiCta";

const ChiaSeBienDongSoDu = () => {
  // Complete loading khi component đã mount
  usePageLoading();

  return (
    <>
      <SEOHead pageKey="chiaSeBienDong" />
      <Header />
      <ChiaSeBienDongHero />
      <OpenApiComparisonTable />
      <BankPartnersSection />
      <OpenApiCta />
      <Footer />
    </>
  );
};

export default ChiaSeBienDongSoDu;
