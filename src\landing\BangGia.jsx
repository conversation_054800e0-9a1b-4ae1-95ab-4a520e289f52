import React from "react";
import SEOHead from "../components/SEOHead";
import Header from "./Header";
import Footer from "./Footer";
import usePageLoading from "../hooks/usePageLoading";
import Breadcrumb from "./Breadcrumb";
import BangGiaPricing from "./product/BangGiaPricing";
import Cta from "./product/OpenApiCta";
import Testimonial from "./home/<USER>";

const BangGia = () => {
  // Complete loading khi component đã mount
  usePageLoading();

  return (
    <>
      <SEOHead pageKey="bangGia" />
      <Header />
      <Breadcrumb />
      <BangGiaPricing />
      <Cta />
      <Testimonial />
      <Footer />
    </>
  );
};

export default BangGia;
