import { useState, useCallback } from "react";
import axios from "axios";
import { API_BASE_URL } from "./apiConfig";

// Hook dành riêng cho API lấy thông tin tài khoản BIDV
const useGetInfoBIDVApi = () => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const callApi = useCallback(async (body) => {
    setLoading(true);
    setError(null);
    setData(null);

    const API_ENDPOINT = `${API_BASE_URL}/getInfoBIDV`;

    try {
      const token = localStorage.getItem("token");
      const headers = { "Content-Type": "application/x-www-form-urlencoded" };

      if (token) {
        headers["Authorization"] = `Bearer ${token}`;
      } else {
        throw new Error("Không tìm thấy token xác thực. Vui lòng đăng nhập.");
      }

      // Debug logging
      console.log("GetInfoBIDV API Call:", {
        endpoint: API_ENDPOINT,
        body: body,
        headers: headers,
      });

      const requestBody = new URLSearchParams(body);
      console.log("Request body (URLSearchParams):", requestBody.toString());

      const response = await axios.post(API_ENDPOINT, requestBody, { headers });
      const result = response.data;

      console.log("GetInfoBIDV API Response:", result);

      if (result.success === true) {
        setData(result);
        return result;
      } else {
        setError(result.message || "API trả về lỗi không xác định.");
        return null;
        a;
      }
    } catch (err) {
      console.log("GetInfoBIDV API Error:", err);
      console.log("Error response:", err.response?.data);

      let errorMessage = "Đã có lỗi xảy ra.";
      if (err.response) {
        errorMessage =
          err.response.data.message ||
          err.response.data.error ||
          `Lỗi ${err.response.status}: ${err.response.statusText}`;
      } else {
        errorMessage = err.message;
      }
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  return { data, loading, error, callApi };
};

export default useGetInfoBIDVApi;
