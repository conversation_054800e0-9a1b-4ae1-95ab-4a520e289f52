import React from "react";
import MasterLayout from "../masterLayout/MasterLayout";
import Breadcrumb from "../components/Breadcrumb";
import { Icon } from "@iconify/react/dist/iconify.js";
import { useState, useEffect } from "react";
import { callOrderApi, callUserApi } from "../callapi/OrderApi";
import { useLocation, useNavigate } from "react-router-dom";

const CheckOutPage = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const planData = location.state;

  // Helper function để lấy user_id an toàn
  const getUserId = () => {
    const userId = localStorage.getItem("user_id");
    if (
      !userId ||
      userId === "null" ||
      userId === "undefined" ||
      userId.trim() === ""
    ) {
      return null;
    }
    return userId.trim();
  };

  // Redirect nếu không có plan data
  useEffect(() => {
    if (!planData) {
      navigate("/client/pricetable");
    }
  }, [planData, navigate]);

  const [vatRequest, setVatRequest] = useState(false);
  const [promoCode, setPromoCode] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [discount, setDiscount] = useState(0);
  const [originalAmount] = useState(planData?.amount || 150000);
  const [promoApplied, setPromoApplied] = useState(false);
  const [promoMessage, setPromoMessage] = useState("");

  // State cho thông tin VAT
  const [vatInfo, setVatInfo] = useState({
    taxCode: "",
    companyName: "",
    address: "",
    email: "",
  });
  const [vatInfoLoaded, setVatInfoLoaded] = useState(false);
  const [vatValidationMessage, setVatValidationMessage] = useState("");

  const handleVatRequest = async (e) => {
    const isChecked = e.target.checked;
    setVatRequest(isChecked);
    setVatValidationMessage("");

    if (isChecked) {
      // Load thông tin VAT từ API
      await loadVatInfo();
    } else {
      // Reset thông tin VAT khi bỏ chọn
      setVatInfo({
        taxCode: "",
        companyName: "",
        address: "",
        email: "",
      });
      setVatInfoLoaded(false);
    }
  };

  // Load thông tin VAT từ API
  const loadVatInfo = async () => {
    const userId = localStorage.getItem("user_id");
    if (!userId) {
      setVatValidationMessage("Vui lòng đăng nhập để sử dụng chức năng này");
      return;
    }

    try {
      const response = await callUserApi({
        action: "get_profile",
        user_id: userId,
      });

      if (response.data.status) {
        const userInfo = response.data.message || {};

        const vatData = {
          taxCode: userInfo.tax_number || "",
          companyName: userInfo.company_name || "",
          address: userInfo.address || "",
          email: userInfo.email || "",
        };

        setVatInfo(vatData);
        setVatInfoLoaded(true);

        // Kiểm tra thông tin bắt buộc
        const requiredFields = ["taxCode", "companyName", "address", "email"];
        const missingFields = requiredFields.filter(
          (field) => !vatData[field]?.trim()
        );

        if (missingFields.length > 0) {
          setVatValidationMessage(
            `Thông tin VAT chưa đầy đủ. Vui lòng cập nhật đầy đủ thông tin để xuất hóa đơn.`
          );
        }
      } else {
        setVatValidationMessage(
          "Không thể tải thông tin VAT: " + response.data.message
        );
      }
    } catch (error) {
      console.error("Lỗi khi tải thông tin VAT:", error);
      setVatValidationMessage("Có lỗi xảy ra khi tải thông tin VAT");
    }
  };

  const handlePromoCodeChange = (e) => {
    setPromoCode(e.target.value);
  };

  // Xử lý áp dụng mã khuyến mãi
  const handleApplyPromoCode = async () => {
    if (!promoCode.trim()) {
      setPromoMessage("Vui lòng nhập mã khuyến mãi");
      return;
    }

    const userId = getUserId();
    if (!userId) {
      setPromoMessage("Vui lòng đăng nhập để sử dụng mã khuyến mãi");
      return;
    }

    setIsLoading(true);
    setPromoMessage("");

    const promoData = {
      action: "checkpromocode", // Sửa action thành "checkpromocode"
      user_id: userId,
      promo_code: promoCode,
    };

    console.log("📤 Dữ liệu gửi API checkpromocode:", promoData);

    try {
      const response = await callOrderApi(promoData);
      console.log("📥 Response từ API checkpromocode:", response);

      if (response.data.status) {
        const discountAmount = response.data.discount || 0;
        console.log("✅ Áp dụng thành công! Discount amount:", discountAmount);

        setDiscount(discountAmount);
        setPromoApplied(true);
        setPromoMessage(
          `Áp dụng mã khuyến mãi thành công! Giảm ${discountAmount.toLocaleString()}đ`
        );
      } else {
        console.log("❌ API trả về thất bại:", response.data.message);
        setPromoMessage(response.data.message || "Mã khuyến mãi không hợp lệ");
        setDiscount(0);
        setPromoApplied(false);
      }
    } catch (error) {
      console.log("❌ Lỗi exception khi gọi API:", error);
      setPromoMessage("Có lỗi xảy ra khi áp dụng mã khuyến mãi");
      setDiscount(0);
      setPromoApplied(false);
      console.error("Promo code error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Xử lý tạo hóa đơn và thanh toán
  const handleSubmit = async (e) => {
    e.preventDefault();

    console.log("=== BẮT ĐẦU XỬ LÝ THANH TOÁN ===");

    // Kiểm tra thông tin VAT nếu có yêu cầu xuất hóa đơn
    if (vatRequest) {
      console.log("📋 Kiểm tra thông tin VAT...");
      const requiredFields = ["taxCode", "companyName", "address", "email"];
      const missingFields = requiredFields.filter(
        (field) => !vatInfo[field]?.trim()
      );

      if (missingFields.length > 0) {
        console.log("❌ Thiếu thông tin VAT:", missingFields);
        setVatValidationMessage(
          `Vui lòng cập nhật đầy đủ thông tin VAT để xuất hóa đơn.`
        );
        return; // Không cho submit
      }
      console.log("✅ Thông tin VAT đầy đủ");
    }

    setIsLoading(true);
    const userId = getUserId();
    console.log("👤 User ID processed:", userId);
    if (!userId) {
      console.log("❌ Không có user_id hợp lệ");
      alert("Vui lòng đăng nhập để tiếp tục");
      setIsLoading(false);
      return;
    }

    try {
      // Luôn tạo invoice mới khi submit
      const createInvoiceData = {
        action: "create_plan_account_and_invoice",
        user_id: getUserId(),
        plan_id: planData?.planId?.toString() || "1",
        billing_cycle: mapCycleToPeriod(planData?.billingCycle) || "monthly",
        amount: finalAmount.toString(),
        vat: vatRequest ? "1" : "0",
      };

      // Thêm mã khuyến mãi nếu đã được áp dụng thành công
      if (promoApplied && promoCode) {
        createInvoiceData.promo_code = promoCode;
      }

      console.log("📤 Dữ liệu tạo invoice cuối cùng:", createInvoiceData);

      const response = await callOrderApi(createInvoiceData);
      if (response.data.status) {
        const invoiceDetails = response.data.invoiceDetails;
        window.location.href = invoiceDetails.payUrl;
        return;
      } else {
        // Nếu lỗi do đã có gói, show popup
        if (response.data.message?.includes("đã có gói")) {
          setShowInvoiceExistsModal(true);
          setTimeout(() => {
            setShowInvoiceExistsModal(false);
            navigate("/client/invoice");
          }, 2000);
          return;
        }
        alert("Có lỗi xảy ra: " + response.data.message);
      }
    } catch (error) {
      alert("Có lỗi xảy ra khi xử lý yêu cầu");
    } finally {
      setIsLoading(false);
    }
  };

  // Tính tổng tiền sau khi áp dụng khuyến mãi
  const finalAmount = originalAmount - discount;

  // Map billing cycle to API format
  const mapCycleToPeriod = (cycle) => {
    switch (cycle) {
      case "1m":
        return "monthly";
      case "3m":
        return "quarterly";
      case "6m":
        return "semi-annually";
      case "12m":
        return "annually";
      default:
        return "monthly";
    }
  };

  // Return early nếu không có plan data
  if (!planData) {
    return null;
  }

  // Modal thông báo lỗi hóa đơn chưa thanh toán
  const InvoiceExistsModal = ({ show, onClose }) =>
    show ? (
      <div
        className="modal fade show d-block"
        tabIndex="-1"
        style={{ background: "rgba(0,0,0,0.3)", zIndex: 9999 }}
        aria-modal="true"
        role="dialog"
      >
        <div className="modal-dialog modal-dialog-centered">
          <div className="modal-content">
            <div className="modal-header">
              <p className="modal-title text-danger">Không thể tạo gói mới</p>
            </div>
            <div className="modal-body text-center">
              <p className="mb-3 fs-5">
                Bạn đã có hóa đơn chưa thanh toán.
                <br />
                Vui lòng thanh toán hoặc kiểm tra trạng thái hóa đơn trước khi
                tạo gói mới.
              </p>
              <div className="alert alert-warning">
                Đang chuyển hướng về trang hóa đơn...
              </div>
            </div>
            <div className="modal-footer justify-content-center">
              <button
                type="button"
                className="btn btn-success"
                onClick={onClose}
              >
                Đóng
              </button>
            </div>
          </div>
        </div>
      </div>
    ) : null;

  const [showInvoiceExistsModal, setShowInvoiceExistsModal] = useState(false);

  return (
    <>
      {/* MasterLayout */}
      <MasterLayout>
        {/* Breadcrumb */}
        <Breadcrumb
          title={planData?.isUpgrade ? "Nâng cấp gói" : "Thanh toán"}
        />
        {/* Popup modal khi có hóa đơn chưa thanh toán */}
        <InvoiceExistsModal
          show={showInvoiceExistsModal}
          onClose={() => setShowInvoiceExistsModal(false)}
        />
        <form onSubmit={handleSubmit} className="checkout-form">
          <div className="container px-3 px-md-4">
            <div className="row g-3 g-lg-4">
              {/* Phần trái - Form thanh toán */}
              <div className="col-12 col-lg-7 col-xl-8">
                <div className="card shadow-sm">
                  <div
                    className="card-header fw-bold"
                    style={{
                      backgroundColor: "var(--neutral-200)",
                      color: "black",
                    }}
                  >
                    1. Kỳ hạn thanh toán
                  </div>
                  <div className="card-body">
                    <div
                      className="form-check p-3 rounded border"
                      style={{
                        backgroundColor: "var(--success-200)",
                      }}
                    >
                      <input
                        className="form-check-input"
                        type="radio"
                        value=""
                        checked={true}
                        readOnly
                      />
                      <label
                        className="form-check-label fw-medium"
                        htmlFor="checkDefault"
                      >
                        {planData?.selectedOption || "Thanh toán 1 tháng"}
                      </label>
                    </div>
                  </div>
                </div>

                <div className="card shadow-sm mt-3">
                  <div
                    className="card-header fw-bold"
                    style={{
                      backgroundColor: "var(--neutral-200)",
                      color: "black",
                    }}
                  >
                    2. Hình thức thanh toán
                  </div>
                  <div className="card-body">
                    <div
                      className="form-check p-3 rounded border"
                      style={{
                        backgroundColor: "var(--success-200)",
                      }}
                    >
                      <input
                        className="form-check-input"
                        type="radio"
                        value=""
                        checked={true}
                        readOnly
                      />
                      <label
                        className="form-check-label fw-medium"
                        htmlFor="checkDefault2"
                      >
                        Chuyển khoản ngân hàng
                      </label>
                    </div>
                  </div>
                </div>

                <div className="card shadow-sm mt-3">
                  <div
                    className="card-header fw-bold"
                    style={{
                      backgroundColor: "var(--neutral-200)",
                      color: "black",
                    }}
                  >
                    3. Xuất hóa đơn
                  </div>
                  <div className="card-body">
                    <div className="form-check form-switch p-3">
                      <input
                        className="form-check-input"
                        type="checkbox"
                        role="switch"
                        checked={vatRequest === true}
                        onChange={handleVatRequest}
                      />
                      <label
                        className="form-check-label fw-medium"
                        htmlFor="switchCheckChecked"
                      >
                        Yêu cầu xuất hóa đơn VAT
                      </label>
                    </div>

                    {vatRequest && (
                      <>
                        <hr className="my-3" />
                        {vatValidationMessage && (
                          <div className="alert alert-warning border-0 shadow-sm mb-3">
                            <div className="d-flex flex-column flex-sm-row align-items-start align-items-sm-center">
                              <div className="flex-grow-1 mb-2 mb-sm-0">
                                <div className="d-flex align-items-center">
                                  <div>
                                    <strong>Thông báo:</strong>
                                    <br className="d-sm-none" />
                                    <span className="ms-sm-1">
                                      {vatValidationMessage}
                                    </span>
                                  </div>
                                </div>
                              </div>
                              {(vatValidationMessage.includes("chưa đầy đủ") ||
                                vatValidationMessage.includes(
                                  "cập nhật đầy đủ thông tin"
                                )) && (
                                <a
                                  href="/client/profile"
                                  className="btn btn-sm btn-success shadow-sm"
                                  style={{ minWidth: "100px" }}
                                >
                                  Cập nhật
                                </a>
                              )}
                            </div>
                          </div>
                        )}

                        <div className="row g-3">
                          <div className="col-12 col-md-6">
                            <label className="form-label fw-medium text-dark">
                              Mã số thuế
                            </label>
                            <input
                              type="text"
                              readOnly
                              className="form-control form-control-lg border-2"
                              value={vatInfo.taxCode}
                              disabled
                              placeholder="Chưa có thông tin"
                              style={{ backgroundColor: "#f8f9fa" }}
                            />
                          </div>

                          <div className="col-12 col-md-6">
                            <label className="form-label fw-medium text-dark">
                              Tên doanh nghiệp
                            </label>
                            <input
                              type="text"
                              readOnly
                              className="form-control form-control-lg border-2"
                              value={vatInfo.companyName}
                              disabled
                              placeholder="Chưa có thông tin"
                              style={{ backgroundColor: "#f8f9fa" }}
                            />
                          </div>

                          <div className="col-12">
                            <label className="form-label fw-medium text-dark">
                              Địa chỉ
                            </label>
                            <input
                              type="text"
                              readOnly
                              className="form-control form-control-lg border-2"
                              value={vatInfo.address}
                              disabled
                              placeholder="Chưa có thông tin"
                              style={{ backgroundColor: "#f8f9fa" }}
                            />
                          </div>

                          <div className="col-12">
                            <label className="form-label fw-medium text-dark">
                              Email nhận hóa đơn
                            </label>
                            <input
                              type="text"
                              readOnly
                              className="form-control form-control-lg border-2"
                              value={vatInfo.email}
                              disabled
                              placeholder="Chưa có thông tin"
                              style={{ backgroundColor: "#f8f9fa" }}
                            />
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>

              {/* Phần phải - Thông tin hóa đơn */}
              <div className="col-12 col-lg-5 col-xl-4">
                <div
                  className="card shadow-sm position-lg-sticky"
                  style={{ top: "20px" }}
                >
                  <div
                    className="card-header fw-bold"
                    style={{
                      backgroundColor: "var(--neutral-200)",
                      color: "black",
                    }}
                  >
                    Thông tin hóa đơn
                  </div>
                  <div className="card-body">
                    {/* Gói dịch vụ */}
                    <div className="d-flex justify-content-between align-items-start mb-3 p-3">
                      <div className="flex-grow-1">
                        <h6
                          className="mb-1 fw-bold"
                          style={{ color: "var(--success-700)" }}
                        >
                          Gói {planData?.planName || "Basic"}
                        </h6>
                        <small className="text-muted">
                          {planData?.selectedOption || "x 1 tháng"}
                        </small>
                      </div>
                      <div className="fw-bold text-end">
                        <div
                          className="h6 mb-0"
                          style={{ color: "var(--success-700)" }}
                        >
                          {originalAmount.toLocaleString()}
                        </div>
                        <small className="text-muted">VNĐ</small>
                      </div>
                    </div>

                    <hr className="my-3" />

                    {/* Tạm tính */}
                    <div className="d-flex justify-content-between align-items-center mb-3">
                      <div className="fw-medium">Tạm tính</div>
                      <div className="fw-bold">
                        {originalAmount.toLocaleString()} VNĐ
                      </div>
                    </div>

                    {/* Giảm giá nếu có */}
                    {promoApplied && discount > 0 && (
                      <div
                        className="d-flex justify-content-between align-items-center mb-3 p-2 rounded"
                        style={{ backgroundColor: "var(--success-200)" }}
                      >
                        <div
                          className="fw-medium"
                          style={{ color: "var(--success-700)" }}
                        >
                          Giảm giá ({promoCode})
                        </div>
                        <div
                          className="fw-bold"
                          style={{ color: "var(--success-700)" }}
                        >
                          -{discount.toLocaleString()} VNĐ
                        </div>
                      </div>
                    )}

                    {/* Mã khuyến mãi */}
                    <div className="mb-3">
                      <div className="input-group">
                        <input
                          type="text"
                          className="form-control"
                          placeholder="Mã khuyến mãi"
                          value={promoCode}
                          onChange={handlePromoCodeChange}
                          disabled={promoApplied || isLoading}
                        />
                        <button
                          className="btn btn-success"
                          type="button"
                          onClick={handleApplyPromoCode}
                          disabled={promoApplied || isLoading}
                        >
                          {isLoading
                            ? "..."
                            : promoApplied
                            ? "ĐÃ ÁP DỤNG"
                            : "THÊM"}
                        </button>
                      </div>
                      {promoMessage && (
                        <small
                          className={`mt-1 d-block ${
                            promoApplied ? "text-success" : "text-danger"
                          }`}
                        >
                          {promoMessage}
                        </small>
                      )}
                    </div>

                    <hr className="my-4" />

                    {/* Tổng cộng */}
                    <div className="p-4  my-3 ">
                      <div className="d-flex justify-content-between align-items-center">
                        <div className="fw-bold fs-5 text-dark">Tổng cộng:</div>
                        <div className="text-end">
                          <div
                            className="fw-bold fs-4"
                            style={{ color: "var(--success-700)" }}
                          >
                            {finalAmount.toLocaleString()}
                            <small className="text-muted fw-medium"> VNĐ</small>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Nút thanh toán */}
                    <div className="d-grid gap-2">
                      <button
                        type="submit"
                        className={`btn btn-success btn-lg fw-bold py-3 shadow-sm ${
                          isLoading ? "disabled" : ""
                        }`}
                        disabled={isLoading}
                        style={{
                          transition: "all 0.3s ease",
                          transform: isLoading ? "scale(0.98)" : "scale(1)",
                        }}
                      >
                        {isLoading ? (
                          <>
                            <span
                              className="spinner-border spinner-border-sm me-2"
                              role="status"
                            ></span>
                            ĐANG XỬ LÝ...
                          </>
                        ) : planData?.isUpgrade ? (
                          "NÂNG CẤP NGAY"
                        ) : (
                          "THANH TOÁN NGAY"
                        )}
                      </button>
                    </div>

                    {/* Thông tin bảo mật */}
                    <div className="text-center mt-3">
                      <small className="text-muted">
                        Thanh toán được bảo mật bởi SSL
                      </small>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </form>
      </MasterLayout>
    </>
  );
};

export default CheckOutPage;
