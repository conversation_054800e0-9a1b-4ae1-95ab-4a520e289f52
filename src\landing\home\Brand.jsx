import React from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import brand_1_1 from "../../assets/landing/img/brand/brand_1_1.png";
import brand_1_2 from "../../assets/landing/img/brand/brand_1_2.png";
import brand_1_3 from "../../assets/landing/img/brand/brand_1_3.png";
import brand_1_4 from "../../assets/landing/img/brand/brand_1_4.png";
import brand_1_5 from "../../assets/landing/img/brand/brand_1_5.png";
import brand_1_6 from "../../assets/landing/img/brand/brand_1_6.png";
import brand_2_1 from "../../assets/landing/img/brand/brand_2_1.png";
import brand_2_2 from "../../assets/landing/img/brand/brand_2_2.png";
import brand_2_3 from "../../assets/landing/img/brand/brand_2_3.png";

const brandImages = [
  brand_1_1,
  brand_1_2,
  brand_1_3,
  brand_1_4,
  brand_1_5,
  brand_1_6,
  brand_1_1,
  brand_1_2,
  brand_1_3,
  brand_1_4,
  brand_1_5,
  brand_1_6,
  brand_2_1,
  brand_2_2,
  brand_2_3,
];

const Brand = () => (
  <div className="brand-sec3 overflow-hidden space-bottom">
    <div className="container th-container4">
      <div className="slider-area text-center">
        <Swiper
          className="th-slider"
          breakpoints={{
            0: { slidesPerView: 2 },
            576: { slidesPerView: 2 },
            768: { slidesPerView: 3 },
            992: { slidesPerView: 3 },
            1200: { slidesPerView: 4 },
            1400: { slidesPerView: 5 },
          }}
          spaceBetween={24}
          loop={true}
        >
          {brandImages.map((img, idx) => (
            <SwiperSlide key={idx}>
              <a href="/client/signup" className="brand-box">
                <img src={img} alt="Brand Logo" />
              </a>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </div>
  </div>
);

export default Brand;
