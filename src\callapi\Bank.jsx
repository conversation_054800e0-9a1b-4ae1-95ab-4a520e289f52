// src/callapi/Bank.jsx

import { useState, useCallback, useRef, useEffect } from "react";
import axios from "axios";
import { API_BASE_URL } from "./apiConfig";

// Global cache để chia sẻ giữa các component
const globalCache = new Map();
const pendingRequests = new Map();

// Cache config
const CACHE_DURATION = 5 * 60 * 1000; // 5 phút
const STALE_WHILE_REVALIDATE_DURATION = 2 * 60 * 1000; // 2 phút

// Helper function để tạo cache key
const getCacheKey = (body) => {
  return JSON.stringify(body);
};

// Helper function kiểm tra cache hợp lệ
const isCacheValid = (cacheEntry) => {
  return Date.now() - cacheEntry.timestamp < CACHE_DURATION;
};

// Helper function kiểm tra cache còn fresh
const isCacheFresh = (cacheEntry) => {
  return Date.now() - cacheEntry.timestamp < STALE_WHILE_REVALIDATE_DURATION;
};

const useBankApi = () => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isStale, setIsStale] = useState(false); // Cho biết data đang hiển thị có phải từ cache cũ không
  const abortControllerRef = useRef(null);

  const callApi = useCallback(async (body, options = {}) => {
    const {
      forceRefresh = false,
      optimistic = null,
      background = false,
    } = options;

    const cacheKey = getCacheKey(body);

    // Không cancel request trước đó trong dev mode
    if (!import.meta.env.DEV && abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Tạo AbortController mới
    abortControllerRef.current = new AbortController();

    // Kiểm tra cache trước
    const cachedData = globalCache.get(cacheKey);

    if (!forceRefresh && cachedData && isCacheValid(cachedData)) {
      // Cache hợp lệ - trả về ngay
      setData(cachedData.data);
      setError(null);
      setIsStale(!isCacheFresh(cachedData));

      if (!background) {
        setLoading(false);
      }

      return cachedData.data;
    }

    // Nếu có cache cũ và không phải force refresh, hiển thị cache cũ trước
    if (!forceRefresh && cachedData && !background) {
      setData(cachedData.data);
      setIsStale(true);
    }

    // Nếu có optimistic data, hiển thị trước
    if (optimistic && !background) {
      setData(optimistic);
      setIsStale(false);
    }

    // Kiểm tra pending request
    if (pendingRequests.has(cacheKey)) {
      return await pendingRequests.get(cacheKey);
    }

    if (!background) {
      setLoading(true);
      setError(null);
    }

    const API_ENDPOINT = `${API_BASE_URL}/bank`;

    const requestPromise = (async () => {
      try {
        const token = localStorage.getItem("token");
        const headers = { "Content-Type": "application/x-www-form-urlencoded" };

        if (token) {
          headers["Authorization"] = `Bearer ${token}`;
        } else {
          throw new Error("Không tìm thấy token xác thực. Vui lòng đăng nhập.");
        }

        const requestBody = new URLSearchParams(body);

        const response = await axios.post(API_ENDPOINT, requestBody, {
          headers,
          signal: import.meta.env.DEV
            ? undefined
            : abortControllerRef.current.signal,
          timeout: 30000, // 30 second timeout
        });

        const result = response.data;

        if (
          (body.action === "delete" && response.status === 200) ||
          result.status === true
        ) {
          // Lưu vào cache
          globalCache.set(cacheKey, {
            data: result,
            timestamp: Date.now(),
          });

          if (!background) {
            setData(result);
            setIsStale(false);
          }

          return result;
        } else {
          const errorMsg = result.message || "API trả về lỗi không xác định.";
          if (!background) {
            setError(errorMsg);
          }
          return null;
        }
      } catch (err) {
        // Ignore AbortError chỉ trong production
        if (err.name === "AbortError" && !import.meta.env.DEV) {
          return null;
        }

        let errorMessage = "Đã có lỗi xảy ra.";
        if (err.response) {
          errorMessage =
            err.response.data.message ||
            err.response.data.error ||
            "Lỗi từ máy chủ nhưng không có thông điệp cụ thể.";
        } else {
          errorMessage = err.message;
        }

        if (!background) {
          setError(errorMessage);
        }
        return null;
      } finally {
        pendingRequests.delete(cacheKey);
        if (!background) {
          setLoading(false);
        }
      }
    })();

    // Lưu pending request
    pendingRequests.set(cacheKey, requestPromise);

    return await requestPromise;
  }, []);

  // Preload function
  const preloadApi = useCallback(
    async (body) => {
      const cacheKey = getCacheKey(body);
      const cachedData = globalCache.get(cacheKey);

      // Chỉ preload nếu chưa có cache hoặc cache đã cũ
      if (!cachedData || !isCacheFresh(cachedData)) {
        return await callApi(body, { background: true });
      }

      return cachedData.data;
    },
    [callApi]
  );

  // Invalidate cache function
  const invalidateCache = useCallback((bodyPattern = null) => {
    if (bodyPattern) {
      // Xóa cache theo pattern
      const patternKey = getCacheKey(bodyPattern);
      globalCache.delete(patternKey);
    } else {
      // Xóa toàn bộ cache
      globalCache.clear();
    }
  }, []);

  // Clear stale cache on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    data,
    loading,
    error,
    isStale,
    callApi,
    preloadApi,
    invalidateCache,
  };
};

export default useBankApi;
