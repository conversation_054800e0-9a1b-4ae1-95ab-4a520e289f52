import MasterLayout from "../masterLayout/MasterLayout";
import { Icon } from "@iconify/react";
import Breadcrumb from "../components/Breadcrumb";
import React, { useState, useMemo, useEffect } from "react";
import { bankLogos } from "../client/ImportImage";
import * as XLSX from "xlsx";
import { useNavigate } from "react-router-dom";
import useBankApi from "../callapi/Bank";
import useLarkApi from "../callapi/useLarkApi";

const asset = {
  ACB: { bg: bankLogos.logoAcb },
  BIDV: { bg: bankLogos.logoBidv },
  MBBank: { bg: bankLogos.logoMbb },
  SEAB: { bg: bankLogos.logoSeab },
  TCB: { bg: bankLogos.logoTcb },
  VCB: { bg: bankLogos.logoVcb },
  VTB: { bg: bankLogos.logoVtb },
};

const LarkSuitePage = () => {
  const navigate = useNavigate();
  const {
    data: bankRes,
    loading: bankLoading,
    error: bankError,
    callApi: callBankApi,
  } = useBankApi();

  const {
    data: larkData,
    loading: larkLoading,
    error: larkError,
    fetchList,
    addLark,
    editLark,
    deleteLark,
    toggleStatus,
  } = useLarkApi();

  // Main data and modal state
  const [accountFilter, setAccountFilter] = useState("");
  const [eventFilter, setEventFilter] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [selected, setSelected] = useState(null);
  const [showFullToken, setShowFullToken] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);

  // Add LarkSuite modal state
  const [showAddModal, setShowAddModal] = useState(false);
  const [addBankId, setAddBankId] = useState("");
  const [addEvent, setAddEvent] = useState("IN");
  const [addHookId, setAddHookId] = useState("");
  const [addDescription, setAddDescription] = useState("");
  const [bankSearchTerm, setBankSearchTerm] = useState("");
  const [showBankDropdown, setShowBankDropdown] = useState(false);

  // Extract bank account list from API response
  const bankAccounts = bankRes?.banks || bankRes?.data?.banks || [];

  // Flatten VA accounts with main accounts
  const allBankAccounts = useMemo(() => {
    const accounts = [];

    bankAccounts.forEach((account) => {
      // Check if this account has a different vaNumber (indicating it's a VA account)
      if (account.vaNumber && account.vaNumber !== account.accountNumber) {
        // Has VA - only add VA account, skip main account
        accounts.push({
          ...account,
          displayName: `${account.vaNumber} - ${account.bankName} (VA)`,
          accountType: "va",
          accountNumber: account.vaNumber, // Use VA number as account number
          parentAccount: account,
        });
      } else {
        // No VA - add main account
        accounts.push({
          ...account,
          displayName: `${account.accountNumber} - ${account.bankName}`,
          accountType: "main",
        });
      }

      // Also check for vaAccounts array (in case some accounts have this structure)
      if (account.vaAccounts && account.vaAccounts.length > 0) {
        account.vaAccounts.forEach((vaAccount) => {
          accounts.push({
            ...vaAccount,
            displayName: `${vaAccount.vaNumber} - ${vaAccount.bankName} (VA)`,
            accountType: "va",
            accountNumber: vaAccount.vaNumber, // Use VA number as account number
            parentAccount: account,
          });
        });
      }
    });

    return accounts;
  }, [bankAccounts]);

  // Filtered bank accounts based on search term
  const filteredBankAccounts = useMemo(() => {
    if (!bankSearchTerm.trim()) {
      return allBankAccounts;
    }

    const searchLower = bankSearchTerm.toLowerCase().trim();

    const filtered = allBankAccounts.filter((bank) => {
      // Tìm kiếm chính xác hơn
      const accountNumber = bank.accountNumber?.toString().toLowerCase() || "";
      const vaNumber = bank.vaNumber?.toString().toLowerCase() || "";
      const bankName = bank.bankName?.toLowerCase() || "";

      // Ưu tiên tìm kiếm theo:
      // 1. Bắt đầu bằng search term
      // 2. Chứa search term như một từ riêng biệt
      // 3. Chứa search term ở giữa
      return (
        // Số tài khoản chính bắt đầu bằng search term
        accountNumber.startsWith(searchLower) ||
        // VA number bắt đầu bằng search term
        vaNumber.startsWith(searchLower) ||
        // Tên ngân hàng chứa search term như từ riêng biệt
        bankName.includes(searchLower) ||
        // Fallback: chứa search term nhưng phải có ít nhất 3 ký tự
        (searchLower.length >= 3 &&
          (accountNumber.includes(searchLower) ||
            vaNumber.includes(searchLower)))
      );
    });

    return filtered;
  }, [allBankAccounts, bankSearchTerm]);

  // Delete LarkSuite modal state
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteTarget, setDeleteTarget] = useState(null);
  // Edit LarkSuite modal state
  const [showEditModal, setShowEditModal] = useState(false);
  const [editEvent, setEditEvent] = useState("");
  const [editHookId, setEditHookId] = useState("");
  const [editDescription, setEditDescription] = useState("");

  // Pagination and advanced search state
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;
  const [searchField, setSearchField] = useState("all");

  // Helper to map event codes to display labels
  const getEventLabel = (code) => {
    switch (code) {
      case "IN":
        return "Nhận";
      case "OUT":
        return "Gửi";
      case "ALL":
        return "Gửi và nhận";
      default:
        return code;
    }
  };

  // Fetch bank accounts when opening add-modal
  useEffect(() => {
    if (showAddModal) {
      const userId = localStorage.getItem("user_id");
      callBankApi({ action: "bank_account", user_id: userId });
    }
  }, [showAddModal, callBankApi]);

  // Fetch list of LarkSuite integrations
  const fetchLarks = async () => {
    try {
      await fetchList();
    } catch (err) {
      console.error("Error fetching LarkSuite list:", err);
    }
  };

  useEffect(() => {
    fetchLarks();
    // Also fetch bank accounts for mapping
    const userId = localStorage.getItem("user_id");
    if (userId) {
      callBankApi({ action: "bank_account", user_id: userId });
    }
  }, []);

  // Map larkData to table format
  const data = useMemo(() => {
    if (!Array.isArray(larkData)) return [];

    return larkData.map((item) => {
      const bank = bankAccounts.find((b) => b.id === item.user_bank_id) || {};

      // Check if this bank has VA accounts and prefer VA display
      let displayBankName = bank.bankName;
      let displayAccountNumber = bank.accountNumber || "";

      // If bank has VA number and it's different from main account, use VA details
      if (bank.vaNumber && bank.vaNumber !== bank.accountNumber) {
        displayBankName = `${bank.bankName} (VA)`;
        displayAccountNumber = bank.vaNumber;
      }

      // Better bank code extraction
      let bankCode = "";
      if (bank.bankName) {
        // Extract from format "Bank Name (CODE)" or just use direct mapping
        const match = bank.bankName.match(/\(([^)]+)\)/);
        if (match) {
          bankCode = match[1];
        } else {
          // Direct mapping for common bank names
          const bankNameUpper = bank.bankName.toUpperCase();
          if (bankNameUpper.includes("ACB")) bankCode = "ACB";
          else if (bankNameUpper.includes("BIDV")) bankCode = "BIDV";
          else if (
            bankNameUpper.includes("MBBANK") ||
            bankNameUpper.includes("MB BANK")
          )
            bankCode = "MBBank";
          else if (bankNameUpper.includes("SEAB")) bankCode = "SEAB";
          else if (bankNameUpper.includes("TCB")) bankCode = "TCB";
          else if (
            bankNameUpper.includes("VCB") ||
            bankNameUpper.includes("VIETCOMBANK")
          )
            bankCode = "VCB";
          else if (
            bankNameUpper.includes("VTB") ||
            bankNameUpper.includes("VIETINBANK")
          )
            bankCode = "VTB";
        }
      }

      return {
        id: item.id,
        accountLogo: asset[bankCode]?.bg || "",
        accountName: displayBankName,
        accountNumber: displayAccountNumber,
        event: item.event || item.type || "ALL",
        hookId:
          item.hook_id !== null && item.hook_id !== undefined
            ? item.hook_id.toString()
            : "",
        description: item.name || "",
        addedAt: item.created_at,
        updatedAt: item.updated_at,
        active: item.status === 1,
        token: item.token || "",
      };
    });
  }, [larkData, bankAccounts]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showBankDropdown && !event.target.closest(".position-relative")) {
        setShowBankDropdown(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [showBankDropdown]);

  // Filtered list
  const list = useMemo(() => {
    return data.filter((w) => {
      if (accountFilter && w.accountName !== accountFilter) return false;
      if (eventFilter && w.event !== eventFilter) return false;
      if (statusFilter) {
        const st = w.active ? "Hoạt động" : "Không hoạt động";
        if (st !== statusFilter) return false;
      }
      if (searchTerm) {
        const t = searchTerm.toLowerCase();
        if (searchField === "all") {
          // Search in all fields
          return (
            w.id.toString().toLowerCase().includes(t) ||
            w.accountName.toLowerCase().includes(t) ||
            w.accountNumber.toLowerCase().includes(t) ||
            w.event.toLowerCase().includes(t) ||
            w.hookId.toLowerCase().includes(t) ||
            w.description.toLowerCase().includes(t) ||
            w.token.toLowerCase().includes(t)
          );
        } else if (searchField === "id") {
          return w.id.toString().toLowerCase().includes(t);
        } else if (searchField === "account") {
          return (
            w.accountName.toLowerCase().includes(t) ||
            w.accountNumber.toLowerCase().includes(t)
          );
        } else if (searchField === "event") {
          return w.event.toLowerCase().includes(t);
        } else if (searchField === "hookId") {
          return w.hookId.toLowerCase().includes(t);
        } else if (searchField === "description") {
          return w.description.toLowerCase().includes(t);
        } else if (searchField === "token") {
          return w.token.toLowerCase().includes(t);
        }
      }
      return true;
    });
  }, [data, accountFilter, eventFilter, statusFilter, searchTerm, searchField]);

  // Paginated data
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return list.slice(startIndex, endIndex);
  }, [list, currentPage, itemsPerPage]);

  // Total pages
  const totalPages = Math.ceil(list.length / itemsPerPage);

  // Toggle active status
  const toggleActive = async (id) => {
    try {
      const currentItem = data.find((d) => d.id === id);
      await toggleStatus(id, currentItem?.active ? 0 : 1);
      fetchLarks();
    } catch (err) {
      console.error("Error toggling LarkSuite status:", err);
    }
  };

  const openToken = (w) => {
    setSelected(w);
    setShowFullToken(false);
    setShowModal(true);
  };

  const copyToken = () => {
    navigator.clipboard.writeText(selected.token || "");
    setCopySuccess(true);
    setTimeout(() => setCopySuccess(false), 2000);
  };

  // Edit LarkSuite state and handlers
  const openEdit = (w) => {
    setSelected(w);
    setEditEvent(w.event);
    setEditHookId(""); // Để trống input, sẽ dùng hook ID cũ nếu không nhập gì
    setEditDescription(""); // Để trống input, sẽ dùng description cũ nếu không nhập gì
    setShowEditModal(true);
  };

  // Update LarkSuite integration
  const updateLarkSuite = async () => {
    try {
      if (!editEvent) {
        alert("Vui lòng chọn sự kiện");
        return;
      }

      const updates = {
        new_type: editEvent,
        status: selected.active ? 1 : 0,
      };

      // Gửi hook_id: nếu input có nội dung thì dùng mới, nếu trống thì dùng cũ
      if (editHookId && editHookId.trim() !== "") {
        updates.new_hook_id = editHookId.trim();
      } else if (
        selected.hookId &&
        selected.hookId !== "0" &&
        selected.hookId !== ""
      ) {
        updates.new_hook_id = selected.hookId;
      }

      // Gửi name: nếu input có nội dung thì dùng mới, nếu trống thì dùng cũ
      if (editDescription && editDescription.trim() !== "") {
        updates.new_name = editDescription.trim();
      } else if (selected.description && selected.description !== "") {
        updates.new_name = selected.description;
      }

      await editLark(selected.id, updates);
      fetchLarks();
      setShowEditModal(false);
    } catch (err) {
      console.error("Error updating LarkSuite integration:", err);
      alert("Có lỗi xảy ra khi cập nhật: " + (err.message || "Unknown error"));
    }
  };

  // Open delete confirmation modal
  const openDeleteModal = (w) => {
    setDeleteTarget(w);
    setShowDeleteModal(true);
  };

  // Confirm and perform deletion
  const confirmDelete = async () => {
    try {
      await deleteLark(deleteTarget.id);
      fetchLarks();
      setShowDeleteModal(false);
    } catch (err) {
      console.error("Error deleting LarkSuite integration:", err);
    }
  };

  const cancelDelete = () => {
    setShowDeleteModal(false);
    setDeleteTarget(null);
  };

  // Handlers for Add LarkSuite
  const openAddModal = () => {
    setAddBankId("");
    setAddEvent("IN");
    setAddHookId("");
    setAddDescription("");
    setBankSearchTerm("");
    setShowBankDropdown(false);
    setShowAddModal(true);
  };

  const selectBank = (bank) => {
    setAddBankId(bank.id);
    setBankSearchTerm(bank.displayName);
    setShowBankDropdown(false);
  };

  const handleBankSearchChange = (e) => {
    const value = e.target.value;
    setBankSearchTerm(value);
    setAddBankId("");
    setShowBankDropdown(true);
  };

  // Add new LarkSuite integration
  const confirmAdd = async () => {
    try {
      if (!addBankId || !addHookId) {
        alert("Vui lòng chọn tài khoản và nhập Hook ID");
        return;
      }
      await addLark(addBankId, addHookId, addEvent, addDescription, 1);
      fetchLarks();
      setShowAddModal(false);
    } catch (err) {
      console.error("Error adding LarkSuite integration:", err);
      alert(
        "Có lỗi xảy ra khi thêm LarkSuite: " + (err.message || "Unknown error")
      );
    }
  };

  const cancelAdd = () => {
    setShowBankDropdown(false);
    setShowAddModal(false);
  };

  // Utility functions
  const clearAllFilters = () => {
    setAccountFilter("");
    setEventFilter("");
    setStatusFilter("");
    setSearchTerm("");
    setSearchField("all");
    setCurrentPage(1);
  };

  const exportToExcel = () => {
    const exportData = list.map((item, idx) => ({
      STT: idx + 1,
      ID: item.id,
      "Tài khoản": item.accountName,
      "Số TK": item.accountNumber,
      "Sự kiện": item.event,
      "Hook ID": item.hookId,
      "Mô tả": item.name,
      "Ngày thêm": item.addedAt,
      "Ngày cập nhật": item.updatedAt,
      "Trạng thái": item.active ? "Hoạt động" : "Không hoạt động",
      Token: item.token,
    }));

    const ws = XLSX.utils.json_to_sheet(exportData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "LarkSuite");
    ws["!cols"] = [
      { wch: 5 },
      { wch: 8 },
      { wch: 20 },
      { wch: 15 },
      { wch: 15 },
      { wch: 15 },
      { wch: 30 },
      { wch: 20 },
      { wch: 20 },
      { wch: 15 },
      { wch: 30 },
    ];
    XLSX.writeFile(
      wb,
      `larksuite_${new Date().toISOString().split("T")[0]}.xlsx`
    );
  };

  const printTable = () => {
    const printWindow = window.open("", "_blank");
    const htmlContent = `<!DOCTYPE html><html><head><meta charset="utf-8"><title>Danh sách LarkSuite</title><style>
      body{font-family:Arial,sans-serif;margin:20px}
      table{width:100%;border-collapse:collapse}
      th,td{border:1px solid #ccc;padding:6px;font-size:12px;text-align:left}
      th{background:#f2f2f2}
      .text-center{text-align:center}
      .status-active{color:green;font-weight:bold}
      .status-inactive{color:red;font-weight:bold}
    </style></head><body>
      <h3>DANH SÁCH LARKSUITE</h3>
      <p>Ngày in: ${new Date().toLocaleDateString("vi-VN")}</p>
      <table><thead><tr>
        <th>STT</th><th>ID</th><th>Tài khoản</th><th>Số TK</th><th>Sự kiện</th><th>Hook ID</th><th>Mô tả</th><th>Ngày thêm</th><th>Trạng thái</th>
      </tr></thead><tbody>
      ${list
        .map(
          (item, idx) => `
        <tr>
          <td class="text-center">${idx + 1}</td>
          <td class="text-center">${item.id}</td>
          <td>${item.accountName}</td>
          <td>${item.accountNumber}</td>
          <td class="text-center">${item.event}</td>
          <td>${item.hookId}</td>
          <td>${item.description}</td>
          <td>${item.addedAt}</td>
          <td class="text-center ${
            item.active ? "status-active" : "status-inactive"
          }">
            ${item.active ? "Hoạt động" : "Không hoạt động"}
          </td>
        </tr>`
        )
        .join("")}
      </tbody></table>
    </body></html>`;
    printWindow.document.write(htmlContent);
    printWindow.document.close();
    printWindow.focus();
    setTimeout(() => {
      printWindow.print();
      printWindow.close();
    }, 200);
  };

  // Handle pagination
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handlePrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  return (
    <>
      {/* MasterLayout */}
      <MasterLayout>
        {/* Breadcrumb */}
        <Breadcrumb title="Danh sách LarkSuite" />
        {/* Add and filters toolbar */}
        <div className="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center gap-2 mb-3">
          <div className="d-md-flex w-100 gap-2">
            {/* filters toolbar */}
            <select
              className="form-select"
              value={accountFilter}
              onChange={(e) => setAccountFilter(e.target.value)}
            >
              <option value="">Tất cả tài khoản</option>
              {Array.from(new Set(data.map((w) => w.accountName))).map(
                (name) => (
                  <option key={name} value={name}>
                    {name}
                  </option>
                )
              )}
            </select>
            <select
              className="form-select mt-3 mt-md-0"
              value={eventFilter}
              onChange={(e) => setEventFilter(e.target.value)}
            >
              <option value="">Tất cả sự kiện</option>
              {Array.from(new Set(data.map((w) => w.event))).map((ev) => (
                <option key={ev} value={ev}>
                  {getEventLabel(ev)}
                </option>
              ))}
            </select>
            <select
              className="form-select mt-3 mt-md-0"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="">Tất cả trạng thái</option>
              <option value="Hoạt động">Hoạt động</option>
              <option value="Không hoạt động">Không hoạt động</option>
            </select>
          </div>

          <div className="d-none d-md-flex gap-2 w-100 justify-content-md-end">
            <button className="btn btn-effect btn-sm" onClick={exportToExcel}>
              <Icon className="me-1" icon="mdi:file-excel" /> Excel
            </button>
            <button className="btn btn-effect btn-sm" onClick={printTable}>
              <Icon className="me-1" icon="mdi:file-pdf" /> Print
            </button>
          </div>
        </div>
        <div className="card mb-4">
          <div className="card-header d-flex justify-content-between align-items-center">
            <div className="d-none d-md-inline">
              <input
                type="search"
                className="form-control mt-md-0 mt-3"
                placeholder="Tìm kiếm..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div>
              <button className="btn-effect btn btn-sm" onClick={openAddModal}>
                <Icon className="mx-2" icon="solar:add-circle-outline" />
                Thêm LarkSuite
              </button>
            </div>
          </div>
          <div className="card-body p-3">
            <div className="table-responsive">
              <table className="table bordered-table mb-0">
                <thead>
                  <tr className="text-center">
                    <th className="id">ID</th>
                    <th style={{ width: "150px" }}>Tài khoản</th>
                    <th className="text-center">Sự kiện</th>
                    <th>Hook ID</th>
                    <th>Mô tả</th>
                    <th className="text-center">Hoạt động</th>
                    <th>Trạng thái</th>
                    <th>Hành động</th>
                  </tr>
                </thead>
                <tbody>
                  {larkLoading ? (
                    <tr>
                      <td colSpan={8} className="text-center p-4">
                        <div
                          className="spinner-border text-primary"
                          role="status"
                        >
                          <span className="visually-hidden">Loading...</span>
                        </div>
                        <div className="mt-2">Đang tải dữ liệu...</div>
                      </td>
                    </tr>
                  ) : larkError ? (
                    <tr>
                      <td colSpan={8} className="text-center p-4 text-danger">
                        Lỗi: {larkError}
                      </td>
                    </tr>
                  ) : list.length > 0 ? (
                    paginatedData.map((w) => (
                      <tr key={w.id}>
                        <td>{w.id}</td>
                        <td className="text-center" style={{ width: "150px" }}>
                          <img
                            src={w.accountLogo}
                            alt={w.accountName}
                            width={80}
                            className="me-2"
                          />
                          <br />
                          {w.accountNumber}
                        </td>
                        <td className="text-center">
                          <button className="alert alert-primary p-4 small">
                            {getEventLabel(w.event)}
                          </button>
                        </td>
                        <td>{w.hookId}</td>
                        <td>{w.description}</td>
                        <td className="text-center">
                          <label className="custom-switch">
                            <input
                              type="checkbox"
                              checked={w.active}
                              onChange={() => toggleActive(w.id)}
                            />
                            <span className="slider round"></span>
                          </label>
                        </td>
                        <td>
                          <span
                            className={`badge ${
                              w.active ? "bg-success" : "bg-danger"
                            } text-white`}
                          >
                            {w.active ? "Hoạt động" : "Tạm dừng"}
                          </span>
                        </td>
                        <td>
                          <div className="d-flex gap-2">
                            <button
                              className="btn btn-success btn-sm"
                              title="Sửa LarkSuite"
                              onClick={() => openEdit(w)}
                            >
                              <Icon icon="mdi:pencil" />
                            </button>
                            <button
                              className="btn btn-success btn-sm"
                              title="Xóa LarkSuite"
                              onClick={() => openDeleteModal(w)}
                            >
                              <Icon icon="mdi:delete" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={8} className="text-center p-4">
                        Không có dữ liệu để hiển thị
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
            {/* Pagination */}

            <div className="d-flex justify-content-end mt-3">
              <nav>
                <ul className="pagination pagination-sm mb-0">
                  <li
                    className={`page-item ${
                      currentPage === 1 ? "disabled" : ""
                    }`}
                    onClick={handlePrevPage}
                  >
                    <button className="page-link" disabled={currentPage === 1}>
                      &laquo;
                    </button>
                  </li>
                  {Array.from({ length: totalPages }, (_, i) => (
                    <li
                      key={i + 1}
                      className={`page-item ${
                        currentPage === i + 1 ? "active" : ""
                      }`}
                      onClick={() => handlePageChange(i + 1)}
                    >
                      <button className="page-link">{i + 1}</button>
                    </li>
                  ))}
                  <li
                    className={`page-item ${
                      currentPage === totalPages ? "disabled" : ""
                    }`}
                    onClick={handleNextPage}
                  >
                    <button
                      className="page-link"
                      disabled={currentPage === totalPages}
                    >
                      &raquo;
                    </button>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        </div>
      </MasterLayout>

      {/* Edit LarkSuite Modal */}
      {showEditModal && selected && (
        <>
          <div className="modal-backdrop fade show"></div>
          <div className="modal fade show" style={{ display: "block" }}>
            <div className="modal-dialog modal-dialog-centered">
              <div className="modal-content">
                <div className="modal-header bg-success text-white justify-content-between">
                  <p className="m-0">
                    LarkSuite tài khoản: {selected.accountNumber}
                  </p>
                  <button
                    type="button"
                    className="btn bg-white py-0"
                    onClick={() => setShowEditModal(false)}
                  >
                    x
                  </button>
                </div>
                <div className="modal-body">
                  <p className="fw-medium mb-2">
                    Lark Hook ID:{" "}
                    <span className="text-muted">
                      (Để trống nếu không thay đổi)
                    </span>
                  </p>
                  <input
                    type="text"
                    className="form-control mb-3"
                    placeholder="Nhập Lark Hook ID mới..."
                    value={editHookId}
                    onChange={(e) => setEditHookId(e.target.value)}
                  />

                  <p className="fw-medium mb-2">Sự kiện:</p>
                  <select
                    className="form-select mb-3"
                    value={editEvent}
                    onChange={(e) => setEditEvent(e.target.value)}
                  >
                    <option value="IN">Nhận</option>
                    <option value="OUT">Gửi</option>
                    <option value="ALL">Gửi và nhận</option>
                  </select>

                  <p className="fw-medium mb-2">
                    Mô tả:{" "}
                    <span className="text-muted">
                      (Để trống nếu không thay đổi)
                    </span>
                  </p>
                  <input
                    type="text"
                    className="form-control mb-3"
                    placeholder="Nhập mô tả mới..."
                    value={editDescription}
                    onChange={(e) => setEditDescription(e.target.value)}
                  />
                </div>
                <div className="modal-footer">
                  <button
                    className="btn btn-success me-2"
                    onClick={updateLarkSuite}
                  >
                    CẬP NHẬT
                  </button>
                  <button
                    className="btn btn-secondary"
                    onClick={() => setShowEditModal(false)}
                  >
                    ĐÓNG
                  </button>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
      {/* Delete Confirmation Modal */}
      {showDeleteModal && deleteTarget && (
        <>
          <div className="modal-backdrop fade show"></div>
          <div className="modal fade show" style={{ display: "block" }}>
            <div className="modal-dialog modal-dialog-centered">
              <div className="modal-content">
                <div className="modal-header">
                  <p className="modal-title">Xác nhận xóa LarkSuite</p>
                  <button
                    type="button"
                    className="btn-close"
                    onClick={cancelDelete}
                  ></button>
                </div>
                <div className="modal-body">
                  Bạn có chắc muốn xóa LarkSuite cho tài khoản{" "}
                  <strong>{deleteTarget.accountNumber}</strong>?
                </div>
                <div className="modal-footer">
                  <button className="btn btn-success" onClick={confirmDelete}>
                    Xóa
                  </button>
                  <button className="btn btn-success" onClick={cancelDelete}>
                    Hủy
                  </button>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
      {/* Add LarkSuite Modal */}
      {showAddModal && (
        <>
          <div className="modal-backdrop fade show"></div>
          <div className="modal fade show" style={{ display: "block" }}>
            <div className="modal-dialog modal-dialog-centered modal-lg">
              <div className="modal-content">
                <div className="modal-header bg-success text-white justify-content-between">
                  <p className="m-0">Thêm LarkSuite mới</p>
                  <button
                    type="button"
                    className="btn-close"
                    onClick={cancelAdd}
                  ></button>
                </div>
                <div className="modal-body">
                  <p className="fw-medium">Chọn tài khoản ngân hàng:</p>
                  <div className="position-relative">
                    <input
                      type="text"
                      className="form-control mb-3"
                      placeholder="Tìm kiếm theo STK hoặc tên ngân hàng... (Click để xem tất cả)"
                      value={bankSearchTerm}
                      onChange={handleBankSearchChange}
                      onFocus={() => setShowBankDropdown(true)}
                      onClick={() => {
                        if (!bankSearchTerm) {
                          setShowBankDropdown(true);
                        }
                      }}
                    />
                    {showBankDropdown && filteredBankAccounts.length > 0 && (
                      <div
                        className="dropdown-menu show position-absolute w-100"
                        style={{
                          zIndex: 1050,
                          maxHeight: "200px",
                          overflowY: "auto",
                          minWidth: "400px",
                        }}
                      >
                        {bankLoading ? (
                          <div className="dropdown-item-text">Đang tải...</div>
                        ) : (
                          <>
                            {/* Hiển thị tất cả nếu không có search term */}
                            {!bankSearchTerm && (
                              <div className="dropdown-item-text fw-bold border-bottom">
                                Tất cả tài khoản ({filteredBankAccounts.length})
                              </div>
                            )}
                            {filteredBankAccounts.map((bank) => (
                              <button
                                key={`${bank.id}-${bank.accountType}`}
                                type="button"
                                className="dropdown-item text-start"
                                onClick={() => selectBank(bank)}
                                style={{
                                  whiteSpace: "nowrap",
                                  overflow: "hidden",
                                  textOverflow: "ellipsis",
                                }}
                              >
                                {bank.accountType === "va" ? (
                                  <>
                                    <strong>{bank.vaNumber}</strong> -{" "}
                                    {bank.bankName}{" "}
                                    <span className="badge bg-primary">VA</span>
                                  </>
                                ) : (
                                  <>
                                    <strong>{bank.accountNumber}</strong> -{" "}
                                    {bank.bankName}
                                  </>
                                )}
                              </button>
                            ))}
                          </>
                        )}
                      </div>
                    )}
                    {showBankDropdown &&
                      !bankLoading &&
                      filteredBankAccounts.length === 0 &&
                      bankSearchTerm && (
                        <div
                          className="dropdown-menu show position-absolute w-100"
                          style={{ zIndex: 1050 }}
                        >
                          <div className="dropdown-item-text">
                            Không tìm thấy tài khoản phù hợp với "
                            {bankSearchTerm}"
                          </div>
                        </div>
                      )}
                  </div>
                  <p className="fw-medium">Sự kiện:</p>
                  <select
                    className="form-select mb-3"
                    value={addEvent}
                    onChange={(e) => setAddEvent(e.target.value)}
                  >
                    <option value="IN">Nhận</option>
                    <option value="OUT">Gửi</option>
                    <option value="ALL">Gửi và nhận</option>
                  </select>
                  <p className="fw-medium">Hook ID:</p>
                  <input
                    type="text"
                    className="form-control mb-3"
                    placeholder="Nhập Hook ID"
                    value={addHookId}
                    onChange={(e) => setAddHookId(e.target.value)}
                  />
                  <p className="fw-medium">Mô tả:</p>
                  <input
                    type="text"
                    className="form-control mb-3"
                    placeholder="Nhập mô tả"
                    value={addDescription}
                    onChange={(e) => setAddDescription(e.target.value)}
                  />
                </div>
                <div className="modal-footer">
                  <button
                    className="btn btn-success"
                    onClick={confirmAdd}
                    disabled={!addBankId}
                  >
                    Thêm
                  </button>
                  <button className="btn btn-success" onClick={cancelAdd}>
                    Đóng
                  </button>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </>
  );
};

export default LarkSuitePage;
