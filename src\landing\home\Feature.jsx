import React from "react";
import featureLine from "../../assets/landing/img/bg/feature_line.svg";
import featureImg1 from "../../assets/landing/img/normal/feature-img-7-1.png";
import featureImg2 from "../../assets/landing/img/normal/feature-img-7-2.png";

const checklist1 = ["Phản hồi tức thì", "Trải nghiệm đỉnh cao"];
const checklist2 = ["Dễ dàng kết nối", "Bảo mật và an toàn"];
const checklist3 = [
  "Tạo tài khoản Pay2S",
  "Liên kết tài khoản ngân hàng",
  "Kết nối & tự động hóa",
];

const Feature = () => (
  <div className="overflow-hidden space">
    <div className="container th-container4">
      <div className="row justify-content-center">
        <div className="col-lg-7">
          <div className="title-area text-center">
            <span className="sub-title">
              Thông báo đa nền tảng thời gian thực
            </span>
            <h2 className="sec-title">Tính năng nổi bật của Pay2S</h2>
          </div>
        </div>
      </div>
      <div className="feature-wrap7">
        <div className="feature-bg-line">
          <img src={featureLine} alt="img" />
        </div>
        <div className="row gy-80 justify-content-center justify-content-lg-between align-items-center">
          <div className="col-lg-6">
            <div className="feature-thumb7-1">
              <img src={featureImg1} alt="img" />
            </div>
          </div>
          <div className="col-xl-5 col-lg-6">
            <div className="feature-content me-xl-5">
              <h4 className="feature-content-title mb-25">
                Nhận thông báo tức thời qua Zalo, Telegram, Lark ...
              </h4>
              <p className="mb-25">
                Nhận biến động số dư tức thời, kết nối đa nền tảng, dễ dàng phân
                bố, chia sẽ biến động số dư, tiện lợi cho hoạt động bán hàng
                nhiều điểm, chia sẽ đơn hàng với sale
              </p>
              <div className="two-column mb-35">
                <div className="checklist">
                  <ul>
                    {checklist1.map((item, idx) => (
                      <li key={idx}>
                        <i className="fas fa-check"></i> {item}
                      </li>
                    ))}
                  </ul>
                </div>
                <div className="checklist">
                  <ul>
                    {checklist2.map((item, idx) => (
                      <li key={idx}>
                        <i className="fas fa-check"></i> {item}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
              <div className="btn-wrap d-flex flex-nowrap gap-2">
                <a
                  href="/client/signup"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="th-btn style-radius"
                >
                  Sử dụng API ngay
                </a>
                <a
                  href="https://docs.pay2s.vn/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="th-btn style6 style-radius"
                >
                  Tài liệu tích hợp
                </a>
              </div>
            </div>
          </div>
          <div className="col-lg-6 order-lg-4">
            <div className="feature-thumb7-1">
              <img src={featureImg2} alt="img" />
            </div>
          </div>
          <div className="col-xl-5 col-lg-6">
            <div className="feature-content left-content me-xl-5">
              <h4 className="feature-content-title mb-25">API ngân hàng</h4>
              <p className="mb-25">
                Cung cấp API ngân hàng, Open Banking với tốc độ tức thì. Trải
                nghiệm đem lại nhờ hạ tầng máy chủ mạnh mẽ, danh sách nhiều ngân
                hàng ký kết hợp tác trực tiếp. 3 bước đơn giản sẽ giúp bạn tự
                động hóa quy trình thanh toán siêu tiện lợi
              </p>
              <div className="two-column mb-35">
                <div className="checklist">
                  <ul>
                    {checklist3.map((item, idx) => (
                      <li key={idx}>
                        <i className="fas fa-check"></i> {item}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
              <div className="btn-wrap">
                <a href="/client/signup" className="th-btn style-radius">
                  Đăng ký tài khoản ngay
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
);

export default Feature;
