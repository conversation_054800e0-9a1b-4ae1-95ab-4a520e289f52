import React, { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import BlogPost from "./BlogPost";
import BlogCategory from "./BlogCategory";
import LoadingSkeleton from "./LoadingSkeleton";
import blogApi from "../callapi/BlogApiNew";

const BlogRouter = () => {
  const { slug } = useParams();
  const [loading, setLoading] = useState(true);
  const [contentType, setContentType] = useState(null); // 'post' or 'category'
  const [error, setError] = useState(null);

  useEffect(() => {
    const determineContentType = async () => {
      try {
        setLoading(true);
        setError(null);

        // First, try to find if it's a category
        const categories = await blogApi.getCategories();
        const foundCategory = categories.find((cat) => cat.slug === slug);

        if (foundCategory) {
          setContentType("category");
        } else {
          // If not a category, check if it's a valid post
          try {
            await blogApi.getPostBySlug(slug);
            setContentType("post");
          } catch (postError) {
            // If neither category nor post found, default to post (will show 404)
            setContentType("post");
          }
        }
      } catch (err) {
        console.error("Error determining content type:", err);
        // Default to post if there's an error
        setContentType("post");
      } finally {
        setLoading(false);
      }
    };

    determineContentType();
  }, [slug]);

  if (loading) {
    return null; // Loading bar sẽ hiển thị ở BlogTransition
  }

  if (error) {
    return (
      <main className="blog-main">
        <div className="container">
          <div className="error-message">
            <h2>Có lỗi xảy ra</h2>
            <p>{error}</p>
          </div>
        </div>
      </main>
    );
  }

  // Render the appropriate component based on content type
  if (contentType === "category") {
    return <BlogCategory />;
  } else {
    return <BlogPost />;
  }
};

export default BlogRouter;
