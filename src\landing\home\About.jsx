import React from "react";
import aboutBg from "../../assets/landing/img/bg/about_bg_7_1.png";
import aboutImg from "../../assets/landing/img/normal/about_7_1.jpg";

const features = [
  {
    icon: "fas fa-check-circle",
    title: "Đối <PERSON>",
    text: "<PERSON><PERSON> dàng kết nối nội dung chuyển khoản với đơn hàng hoặc người dùng cụ thể. Không cần kiểm tra thủ công, giảm sai sót và tiết kiệm thời gian.",
  },
  {
    icon: "fas fa-building",
    title: "Không cần đăng ký kinh doanh",
    text: "Tự động cập nhật biến động số dư khi có tiền vào tài khoản theo thời gian thực, đ<PERSON><PERSON> tá<PERSON> ch<PERSON>h thức của nhiều ngân hàng, phụ<PERSON> vụ kh<PERSON>ch hàng cá nhân, <PERSON>h<PERSON><PERSON> thẩm định, không giấy tờ",
  },
  {
    icon: "fas fa-briefcase",
    title: "Hỗ Trợ Thanh Toán Đa Kênh",
    text: "Tích hợp với nhiều ngân hàng lớn tại Việt Nam như Vietcombank, BIDV, MB, ACB… giúp quản lý dòng tiền tập trung, thuận tiện cho mọi kênh thanh toán.",
  },
];

const About = () => (
  <div
    className="overflow-hidden"
    style={{ backgroundImage: `url(${aboutBg})`, backgroundSize: "cover" }}
  >
    <div className="container th-container4">
      <div className="row justify-content-between align-items-center flex-row-reverse">
        <div className="col-xl-7">
          <div className="img-box9 pt-xl-0 space-top mb-xl-0 mb-n5">
            <div className="img1">
              <img src={aboutImg} alt="About" />
            </div>
          </div>
        </div>
        <div className="col-xl-5">
          <div className="space">
            <div className="title-area">
              <h2 className="sec-title">
                PAY2S - Tiện ích cho mọi mô hình kinh doanh
              </h2>
            </div>
            {features.map((f, idx) => (
              <div className="about-feature style2" key={idx}>
                <div className="about-feature_icon">
                  <i className={f.icon}></i>
                </div>
                <div className="media-body">
                  <h3 className="about-feature_title">{f.title}</h3>
                  <p className="about-feature_text">{f.text}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  </div>
);

export default About;
