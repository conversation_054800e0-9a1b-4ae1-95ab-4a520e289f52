{"name": "paydash-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@iconify/react": "^6.0.0", "@popperjs/core": "^2.11.8", "animate.css": "^4.1.1", "apexcharts": "^5.3.1", "axios": "^1.11.0", "bootstrap": "^5.3.7", "clsx": "^2.1.1", "framer-motion": "^12.23.11", "next": "15.4.4", "react": "19.1.0", "react-apexcharts": "^1.7.0", "react-dom": "19.1.0", "react-helmet-async": "^2.0.5", "react-slick": "^0.30.3", "react-toastify": "^11.0.5", "slick-carousel": "^1.8.1", "swiper": "^11.2.10", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.4", "tailwindcss": "^4", "typescript": "^5"}}