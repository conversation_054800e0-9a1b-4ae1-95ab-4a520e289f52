import { useEffect, useState } from 'react';

export const useSmoothScroll = () => {
  const [showScrollTop, setShowScrollTop] = useState(false);

  // Handle scroll to anchor
  const scrollToAnchor = (anchorId, offset = 80) => {
    const element = document.getElementById(anchorId);
    if (element) {
      const elementPosition = element.offsetTop - offset;
      window.scrollTo({
        top: elementPosition,
        behavior: 'smooth'
      });
    }
  };

  // Handle scroll to top
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  // Handle anchor links in navigation
  const handleAnchorClick = (e, anchorId) => {
    e.preventDefault();
    scrollToAnchor(anchorId);
    
    // Update URL without page reload
    if (anchorId) {
      window.history.pushState(null, null, `#${anchorId}`);
    }
  };

  // Show/hide scroll to top button
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.pageYOffset > 300);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Handle initial page load with hash
  useEffect(() => {
    if (window.location.hash) {
      const hash = window.location.hash.substring(1);
      setTimeout(() => {
        scrollToAnchor(hash);
      }, 100);
    }
  }, []);

  // Add click listeners to all anchor links
  useEffect(() => {
    const handleLinkClick = (e) => {
      const href = e.target.getAttribute('href');
      if (href && href.startsWith('#')) {
        const anchorId = href.substring(1);
        handleAnchorClick(e, anchorId);
      }
    };

    // Add listeners to all anchor links
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    anchorLinks.forEach(link => {
      link.addEventListener('click', handleLinkClick);
    });

    // Cleanup
    return () => {
      anchorLinks.forEach(link => {
        link.removeEventListener('click', handleLinkClick);
      });
    };
  }, []);

  return {
    scrollToAnchor,
    scrollToTop,
    handleAnchorClick,
    showScrollTop
  };
};

// Hook for intersection observer animations
export const useScrollAnimation = () => {
  useEffect(() => {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('visible');
        }
      });
    }, observerOptions);

    // Observe all elements with fade-in class
    const fadeElements = document.querySelectorAll('.fade-in');
    fadeElements.forEach(el => observer.observe(el));

    return () => {
      fadeElements.forEach(el => observer.unobserve(el));
    };
  }, []);
};
