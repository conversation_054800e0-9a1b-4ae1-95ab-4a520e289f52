// src/hooks/useFormSuggestions.js

import { useState, useEffect, useCallback } from 'react';

/**
 * Hook để quản lý suggestions cho form inputs
 * Lưu trữ dữ liệu đã submit và đề xuất khi user gõ
 */
const useFormSuggestions = (storageKey, minChars = 2) => {
  const [suggestions, setSuggestions] = useState({});
  const [activeSuggestions, setActiveSuggestions] = useState({});

  // Load suggestions từ localStorage khi component mount
  useEffect(() => {
    try {
      const stored = localStorage.getItem(`form_suggestions_${storageKey}`);
      if (stored) {
        setSuggestions(JSON.parse(stored));
      }
    } catch (error) {
      console.error('Error loading form suggestions:', error);
    }
  }, [storageKey]);

  // Save suggestions vào localStorage
  const saveSuggestions = useCallback((newSuggestions) => {
    try {
      localStorage.setItem(
        `form_suggestions_${storageKey}`, 
        JSON.stringify(newSuggestions)
      );
      setSuggestions(newSuggestions);
    } catch (error) {
      console.error('Error saving form suggestions:', error);
    }
  }, [storageKey]);

  // Thêm value mới vào suggestions cho field cụ thể
  const addSuggestion = useCallback((fieldName, value) => {
    if (!value || value.trim().length < minChars) return;

    const trimmedValue = value.trim();
    setSuggestions(prev => {
      const fieldSuggestions = prev[fieldName] || [];
      
      // Kiểm tra xem value đã tồn tại chưa (case insensitive)
      const exists = fieldSuggestions.some(
        item => item.toLowerCase() === trimmedValue.toLowerCase()
      );

      if (!exists) {
        const newFieldSuggestions = [trimmedValue, ...fieldSuggestions].slice(0, 10); // Giới hạn 10 suggestions
        const newSuggestions = {
          ...prev,
          [fieldName]: newFieldSuggestions
        };
        
        // Save to localStorage
        saveSuggestions(newSuggestions);
        return newSuggestions;
      }
      
      return prev;
    });
  }, [minChars, saveSuggestions]);

  // Lấy suggestions cho field và input value cụ thể
  const getSuggestions = useCallback((fieldName, inputValue) => {
    if (!inputValue || inputValue.length < minChars) {
      return [];
    }

    const fieldSuggestions = suggestions[fieldName] || [];
    const lowercaseInput = inputValue.toLowerCase();

    return fieldSuggestions.filter(suggestion =>
      suggestion.toLowerCase().includes(lowercaseInput) &&
      suggestion.toLowerCase() !== lowercaseInput
    ).slice(0, 5); // Giới hạn 5 suggestions hiển thị
  }, [suggestions, minChars]);

  // Set active suggestions cho field cụ thể
  const setFieldSuggestions = useCallback((fieldName, inputValue) => {
    const fieldSuggestions = getSuggestions(fieldName, inputValue);
    setActiveSuggestions(prev => ({
      ...prev,
      [fieldName]: fieldSuggestions
    }));
  }, [getSuggestions]);

  // Clear active suggestions cho field cụ thể
  const clearFieldSuggestions = useCallback((fieldName) => {
    setActiveSuggestions(prev => ({
      ...prev,
      [fieldName]: []
    }));
  }, []);

  // Clear tất cả active suggestions
  const clearAllSuggestions = useCallback(() => {
    setActiveSuggestions({});
  }, []);

  // Submit form data - lưu tất cả values vào suggestions
  const submitFormData = useCallback((formData) => {
    Object.entries(formData).forEach(([fieldName, value]) => {
      if (value && typeof value === 'string') {
        addSuggestion(fieldName, value);
      }
    });
  }, [addSuggestion]);

  // Xóa suggestion cụ thể
  const removeSuggestion = useCallback((fieldName, value) => {
    setSuggestions(prev => {
      const fieldSuggestions = prev[fieldName] || [];
      const newFieldSuggestions = fieldSuggestions.filter(item => item !== value);
      const newSuggestions = {
        ...prev,
        [fieldName]: newFieldSuggestions
      };
      
      saveSuggestions(newSuggestions);
      return newSuggestions;
    });
  }, [saveSuggestions]);

  // Clear tất cả suggestions cho field cụ thể
  const clearFieldAllSuggestions = useCallback((fieldName) => {
    setSuggestions(prev => {
      const newSuggestions = {
        ...prev,
        [fieldName]: []
      };
      
      saveSuggestions(newSuggestions);
      return newSuggestions;
    });
  }, [saveSuggestions]);

  return {
    // Data
    suggestions,
    activeSuggestions,
    
    // Methods
    addSuggestion,
    getSuggestions,
    setFieldSuggestions,
    clearFieldSuggestions,
    clearAllSuggestions,
    submitFormData,
    removeSuggestion,
    clearFieldAllSuggestions,
  };
};

export default useFormSuggestions;
