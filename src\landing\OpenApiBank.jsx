import React from "react";
import OpenApiHero from "./product/OpenApiHero";
import SEOHead from "../components/SEOHead";
import usePageLoading from "../hooks/usePageLoading";
import OpenApiFeatureCards from "./product/OpenApiFeatureCards";
import OpenApiAbout from "./product/OpenApiAbout";
import OpenApiComparisonTable from "./product/OpenApiComparisonTable";
import BankPartnersSection from "./product/BankPartnersSection";
import OpenApiCta from "./product/OpenApiCta";
import OpenApiFaq from "./product/OpenApiFaq";
import Header from "./Header";
import Footer from "./Footer";

const OpenApiBank = () => {
  // Complete loading khi component đã mount
  usePageLoading();

  return (
    <>
      <SEOHead pageKey="apiOpenBanking" />
      <Header />
      <OpenApiHero />
      <OpenApiFeatureCards />
      <OpenApiAbout />
      <OpenApiComparisonTable />
      <BankPartnersSection />
      <OpenApiCta />
      <OpenApiFaq />
      <Footer />
    </>
  );
};

export default OpenApiBank;
