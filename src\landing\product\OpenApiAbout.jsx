import React from "react";
import aboutImg from "../../assets/landing/img/normal/about_4_1.png";
import webhookImg from "../../assets/landing/img/normal/Webhook.png";
import intergrateImg from "../../assets/landing/img/normal/Intergrate.png";
import checkIcon from "../../assets/landing/img/icon/check_1.png";

const aboutChecklist = [
  "Kết nối trực tiếp 7 ngân hàng lớn (ACB, Techcombank, Vietcombank, MB, TPBank, BIDV, ViettinBank)",
  "Hỗ trợ đa nền tảng: Web, iOS, Android, POS",
  "Tài liệu rõ ràng + SDK hỗ trợ 10+ ngôn ngữ lập trình",
];

const OpenApiAbout = () => (
  <div className="container space-top">
    <div className="row align-items-center">
      <div className="col-lg-5 mb-30 mb-lg-0">
        <div className="img-box6">
          <div className="img1">
            <img src={aboutImg} alt="About" />
          </div>
          <div className="shape1">
            <img src={webhookImg} alt="About" />
          </div>
          <div className="shape2">
            <img src={intergrateImg} alt="About" />
          </div>
          <div className="color-animate"></div>
        </div>
      </div>
      <div className="col-lg-7 text-lg-start text-center">
        <div className="ps-xxl-5">
          <div className="title-area mb-35">
            <span className="sub-title">About Company</span>
            <h2 className="sec-title" id="anchor-sec-title">
              Pay2S giúp doanh nghiệp bạn giải quyết bài toán gì?
            </h2>
          </div>
          <p className="mt-n2 mb-25">
            Pay2S giúp bạn tích hợp trực tiếp hệ thống ngân hàng vào website/app
            chỉ với 1 API duy nhất, loại bỏ mọi rào cản thanh toán:
          </p>
          <div className="checklist style4 mb-40 list-center">
            <ul>
              {aboutChecklist.map((item, idx) => (
                <li key={idx}>
                  <img src={checkIcon} alt="icon" /> {item}
                </li>
              ))}
            </ul>
          </div>
          <a
            href="https://docs.pay2s.vn/introduction/pay2s-la-gi.html"
            target="_blank"
            className="th-btn"
          >
            Xem tài liệu tích hợp{" "}
            <i className="fa-regular fa-arrow-right ms-2"></i>
          </a>
        </div>
      </div>
    </div>
  </div>
);

export default OpenApiAbout;
