import React, { useState } from "react";
import { Link } from "react-router-dom";
import logoImg from "../assets/images/logo.png";
import authImg from "../assets/images/auth/auth-img.png";

const ForgotPass = () => {
  const [formData, setFormData] = useState({
    phone: "",
    password: "",
  });
  const [errorMsg, setErrorMsg] = useState("");
  const [loading, setLoading] = useState(false);

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setErrorMsg("");

    // In a real application, you would send the data to your backend for password reset.

    // Simulate a backend call:
    await new Promise((resolve) => setTimeout(resolve, 1000));

    setLoading(false);
    // You'd likely have a success message or redirect here after a successful submission.
  };

  return (
    <section className="auth bg-base d-flex flex-wrap">
      <div className="auth-left d-lg-block d-none">
        <div className="d-flex align-items-center flex-column h-100 justify-content-center">
          <img src={authImg} alt="Đăng ký Pay2S" />
        </div>
      </div>
      <div className="auth-right py-32 px-24 d-flex flex-column justify-content-center">
        <div className="max-w-464-px mx-auto w-100">
          <div>
            <Link to="/" className="mb-40 max-w-290-px d-inline-block">
              <img src={logoImg} alt="Pay2S Logo" />
            </Link>
            <h4 className="mb-12">LẤY LẠI MẬT KHẨU</h4>
            <p className="mb-32 text-secondary-light text-lg">
              Soạn tin nhắn SMS theo cú pháp bên dưới
              <br />
              và gửi đến số điện thoại
            </p>
            <div className="text-center mb-32">
              <div className="text-success text-xl fw-bold">0982 933 507</div>
            </div>
            <div className="mb-32 text-center">
              <span className="form-label text-lg fw-bold">
                MK <span className="text-muted">&lt;khoảng cách&gt;</span>{" "}
                <span className="text-success">mậtkhẩumới</span>
              </span>
            </div>
          </div>

          <div className="text-center">
            <Link
              to="/client/login"
              className="btn btn-success w-100 d-flex align-items-center justify-content-center gap-2"
            >
              <i className="fa fa-arrow-left"></i>
              <span>VỀ TRANG ĐĂNG NHẬP</span>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ForgotPass;
