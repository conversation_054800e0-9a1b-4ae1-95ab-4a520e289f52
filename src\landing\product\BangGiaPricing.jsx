import React, { useState, useEffect } from "react";
import titleShape2 from "../../../assets/landing/img/theme-img/title_shape_2.svg";
import techShape1 from "../../../assets/landing/img/shape/tech_shape_1.png";
import squareShape1 from "../../../assets/landing/img/shape/square_1.png";
import usePlanApi from "../../callapi/Plan";

const BangGiaPricing = () => {
  const [pricingPlans, setPricingPlans] = useState([]);
  const { data, loading, error, callApi } = usePlanApi();

  // Function to format price from API response
  const formatPrice = (price) => {
    if (!price) return "Liên hệ";
    return new Intl.NumberFormat("vi-VN").format(price) + "đ";
  };

  // Function to determine column class based on plan count
  const getColumnClass = (index, total) => {
    if (total <= 3) return "col-xl-4 col-md-6";
    if (total === 4)
      return index < 2 ? "col-xl-3 col-md-6" : "col-xl-3 col-md-6";
    if (total === 5)
      return index < 3 ? "col-xl-4 col-md-6" : "col-xl-6 col-md-6";
    return "col-xl-4 col-md-6";
  };

  // Function to parse features from API response
  const parseFeatures = (plan) => {
    const features = [];

    if (plan.bank_number) {
      if (plan.bank_number === -1) {
        features.push("Không giới hạn tài khoản ngân hàng");
      } else {
        features.push(`Tối đa ${plan.bank_number} tài khoản ngân hàng`);
      }
    }

    features.push("Không giới hạn giao dịch");
    features.push("Không giới hạn cổng thanh toán");
    features.push("Không giới hạn Web/App");
    features.push("Tốc độ cập nhật 2s/lần");

    return features;
  };

  // Fetch pricing plans from API using usePlanApi hook
  useEffect(() => {
    const fetchPricingPlans = async () => {
      try {
        const result = await callApi({
          action: "list_plan",
          // Thử thêm một số params có thể cần thiết
          limit: 100,
          status: "active",
        });

        if (result && result.status && Array.isArray(result.plans)) {
          const formattedPlans = result.plans.map((plan, index) => ({
            id: plan.id,
            title: plan.name || `Plan ${plan.id}`,
            description: plan.description || "Gói dịch vụ",
            price: formatPrice(plan.monthly),
            duration: "/tháng",
            features: parseFeatures(plan),
            colClass: getColumnClass(index, result.plans.length),
            originalData: plan,
          }));

          setPricingPlans(formattedPlans);
        } else {
          // Fallback to default plans if API fails
          setPricingPlans([
            {
              title: "Basic",
              description: "Gói cơ bản",
              price: "150.000đ",
              duration: "/tháng",
              features: [
                "Tối đa 1 tài khoản ngân hàng",
                "Không giới hạn giao dịch",
                "Không giới hạn cổng thanh toán",
                "Không giới hạn Web/App",
                "Tốc độ cập nhật 2s/lần",
              ],
              colClass: "col-xl-4 col-md-6",
            },
          ]);
        }
      } catch (err) {
        console.error("Error fetching pricing plans:", err);
        // Fallback data sẽ được set trong catch của usePlanApi
      }
    };

    fetchPricingPlans();
  }, [callApi]);

  // Loading state
  if (loading) {
    return (
      <section className="space">
        <div className="container">
          <div className="title-area text-center">
            <span className="sub-title">
              <div className="icon-masking me-2">
                <span className="mask-icon" data-mask-src={titleShape2}></span>
                <img src={titleShape2} alt="shape" />
              </div>
              Bảng giá API Pay2S
            </span>
            <h2 className="sec-title">
              Một bảng giá{" "}
              <span className="text-theme fw-normal">Cho tất cả</span>
            </h2>
          </div>
          <div className="row gy-4 justify-content-center">
            <div className="col-12 text-center">
              <div className="spinner-border text-primary" role="status">
                <span className="visually-hidden">Đang tải...</span>
              </div>
              <p className="mt-3">Đang tải bảng giá...</p>
            </div>
          </div>
        </div>
      </section>
    );
  }

  // Error state
  if (error) {
    return (
      <section className="space">
        <div className="container">
          <div className="title-area text-center">
            <span className="sub-title">
              <div className="icon-masking me-2">
                <span className="mask-icon" data-mask-src={titleShape2}></span>
                <img src={titleShape2} alt="shape" />
              </div>
              Bảng giá API Pay2S
            </span>
            <h2 className="sec-title">
              Một bảng giá{" "}
              <span className="text-theme fw-normal">Cho tất cả</span>
            </h2>
          </div>
          <div className="row gy-4 justify-content-center">
            <div className="col-12 text-center">
              <div className="alert alert-warning" role="alert">
                <i className="fas fa-exclamation-triangle me-2"></i>
                Không thể tải bảng giá. Vui lòng thử lại sau.
                <br />
                <small className="text-muted">Lỗi: {error}</small>
              </div>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="space">
      <div className="container">
        <div className="title-area text-center">
          <span className="sub-title">
            <div className="icon-masking me-2">
              <span className="mask-icon" data-mask-src={titleShape2}></span>
              <img src={titleShape2} alt="shape" />
            </div>
            Bảng giá API Pay2S
          </span>
          <h2 className="sec-title">
            Một bảng giá{" "}
            <span className="text-theme fw-normal">Cho tất cả</span>
          </h2>
        </div>
        <div className="row gy-4 justify-content-center">
          {pricingPlans.length > 0 ? (
            pricingPlans.map((plan, index) => (
              <div className={plan.colClass} key={plan.id || index}>
                <div className="price-card">
                  <div className="price-card_top">
                    <h3 className="price-card_title">{plan.title}</h3>
                    <p className="price-card_text">{plan.description}</p>
                    <h4 className="price-card_price">
                      {plan.price}{" "}
                      <span className="duration">{plan.duration}</span>
                    </h4>
                    <div className="particle">
                      <div className="price-particle" id="price-p1"></div>
                    </div>
                  </div>
                  <div className="price-card_content">
                    <div className="checklist">
                      <ul>
                        {plan.features.map((feature, featureIndex) => (
                          <li key={featureIndex}>
                            <i className="fas fa-circle-check"></i> {feature}
                          </li>
                        ))}
                      </ul>
                    </div>
                    <a href="/client/signup" className="th-btn">
                      ĐĂNG KÝ NGAY{" "}
                      <i className="fa-regular fa-arrow-right ms-2"></i>
                    </a>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="col-12 text-center">
              <p>Không có gói dịch vụ nào được tìm thấy.</p>
            </div>
          )}
        </div>
      </div>
      <div className="shape-mockup" data-top="0" data-right="0">
        <img src={techShape1} alt="shape" />
      </div>
      <div className="shape-mockup" data-top="0%" data-left="0%">
        <img src={squareShape1} alt="shape" />
      </div>
    </section>
  );
};

export default BangGiaPricing;
