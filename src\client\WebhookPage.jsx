import MasterLayout from "../masterLayout/MasterLayout";
import { Icon } from "@iconify/react";
import Breadcrumb from "../components/Breadcrumb";
import React, { useState, useMemo, useEffect } from "react";
import { bankLogos } from "../client/ImportImage";
import useWebhooksApi from "../callapi/Webhooks";
import axios from "axios";
import { API_BASE_URL } from "../callapi/apiConfig";
import * as XLSX from "xlsx";
import { useNavigate } from "react-router-dom";
import useBankApi from "../callapi/Bank";

const asset = {
  ACB: { bg: bankLogos.logoAcb },
  BIDV: { bg: bankLogos.logoBidv },
  MBBank: { bg: bankLogos.logoMbb },
  // MoMo: { bg: bankLogos.logoMomo },
  SEAB: { bg: bankLogos.logoSeab },
  TCB: { bg: bankLogos.logoTcb },
  // TPB: { bg: bankLogos.logoTpb },
  VCB: { bg: bankLogos.logoVcb },
  VTB: { bg: bankLogos.logoVtb },
};
const WebhookPage = () => {
  const navigate = useNavigate();
  const {
    data: bankRes,
    loading: bankLoading,
    error: bankError,
    callApi: callBankApi,
  } = useBankApi();
  const {
    data: apiRes,
    loading: hooksLoading,
    error: hooksError,
    callApi,
  } = useWebhooksApi();

  // Main data and modal state
  const [data, setData] = useState([]);
  const [accountFilter, setAccountFilter] = useState("");
  const [eventFilter, setEventFilter] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [selected, setSelected] = useState(null);
  const [showFullToken, setShowFullToken] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);

  // Add webhook modal state
  const [showAddModal, setShowAddModal] = useState(false);
  const [addBankId, setAddBankId] = useState("");
  const [addEvent, setAddEvent] = useState("IN");
  const [addEndpoint, setAddEndpoint] = useState("");
  const [bankSearchTerm, setBankSearchTerm] = useState("");
  const [showBankDropdown, setShowBankDropdown] = useState(false);
  // Extract bank account list from API response
  const bankAccounts = bankRes?.banks || bankRes?.data?.banks || [];

  // Flatten VA accounts with main accounts
  const allBankAccounts = useMemo(() => {
    const accounts = [];

    bankAccounts.forEach((account) => {
      // Check if this account has a different vaNumber (indicating it's a VA account)
      if (account.vaNumber && account.vaNumber !== account.accountNumber) {
        // Has VA - only add VA account, skip main account
        accounts.push({
          ...account,
          displayName: `${account.vaNumber} - ${account.bankName} (VA)`,
          accountType: "va",
          accountNumber: account.vaNumber, // Use VA number as account number
          parentAccount: account,
        });
      } else {
        // No VA - add main account
        accounts.push({
          ...account,
          displayName: `${account.accountNumber} - ${account.bankName}`,
          accountType: "main",
        });
      }

      // Also check for vaAccounts array (in case some accounts have this structure)
      if (account.vaAccounts && account.vaAccounts.length > 0) {
        account.vaAccounts.forEach((vaAccount) => {
          accounts.push({
            ...vaAccount,
            displayName: `${vaAccount.vaNumber} - ${vaAccount.bankName} (VA)`,
            accountType: "va",
            accountNumber: vaAccount.vaNumber, // Use VA number as account number
            parentAccount: account,
          });
        });
      }
    });

    return accounts;
  }, [bankAccounts]);

  // Filtered bank accounts based on search term
  const filteredBankAccounts = useMemo(() => {
    if (!bankSearchTerm.trim()) {
      return allBankAccounts;
    }

    const searchLower = bankSearchTerm.toLowerCase().trim();

    const filtered = allBankAccounts.filter((bank) => {
      // Tìm kiếm chính xác hơn
      const accountNumber = bank.accountNumber?.toString().toLowerCase() || "";
      const vaNumber = bank.vaNumber?.toString().toLowerCase() || "";
      const bankName = bank.bankName?.toLowerCase() || "";

      // Ưu tiên tìm kiếm theo:
      // 1. Bắt đầu bằng search term
      // 2. Chứa search term như một từ riêng biệt
      // 3. Chứa search term ở giữa
      return (
        // Số tài khoản chính bắt đầu bằng search term
        accountNumber.startsWith(searchLower) ||
        // VA number bắt đầu bằng search term
        vaNumber.startsWith(searchLower) ||
        // Tên ngân hàng chứa search term như từ riêng biệt
        bankName.includes(searchLower) ||
        // Fallback: chứa search term nhưng phải có ít nhất 3 ký tự
        (searchLower.length >= 3 &&
          (accountNumber.includes(searchLower) ||
            vaNumber.includes(searchLower)))
      );
    });

    return filtered;
  }, [allBankAccounts, bankSearchTerm]);

  // Delete webhook modal state
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteTarget, setDeleteTarget] = useState(null);
  // Edit webhook modal state
  const [showEditModal, setShowEditModal] = useState(false);
  const [editEvent, setEditEvent] = useState("");
  const [editEndpoint, setEditEndpoint] = useState("");

  // Pagination and advanced search state
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;
  const [searchField, setSearchField] = useState("all");

  // Helper to map event codes to display labels
  const getEventLabel = (code) => {
    switch (code) {
      case "IN":
        return "Nhận";
      case "OUT":
        return "Gửi";
      case "ALL":
        return "Gửi và nhận";
      default:
        return code;
    }
  };

  // Fetch bank accounts when opening add-modal
  useEffect(() => {
    if (showAddModal) {
      const userId = localStorage.getItem("user_id");
      callBankApi({ action: "bank_account", user_id: userId });
    }
  }, [showAddModal, callBankApi]);

  // Fetch webhook list on mount
  useEffect(() => {
    callApi({ action: "list" });
  }, [callApi]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showBankDropdown && !event.target.closest(".position-relative")) {
        setShowBankDropdown(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [showBankDropdown]);
  // Update data when API response arrives
  useEffect(() => {
    if (apiRes) {
      const items = apiRes.hooks || apiRes.data || [];
      const mapped = items.map((item) => {
        // Check if this item has VA number and it's different from main account
        let displayBankName = item.bankName;
        let displayAccountNumber = item.accountNumber;

        // If item has VA number and it's different from main account, use VA details
        if (item.vaNumber && item.vaNumber !== item.accountNumber) {
          displayBankName = `${item.bankName} (VA)`;
          displayAccountNumber = item.vaNumber;
        }

        const match = item.bankName?.match(/\(([^)]+)\)/);
        const code = match ? match[1] : item.bankName;
        return {
          id: item.id,
          accountLogo: asset[code]?.bg || "",
          accountName: displayBankName,
          accountNumber: displayAccountNumber,
          event: item.type,
          endpoint: item.webhook_url,
          addedAt: item.created_at,
          updatedAt: item.updated_at,
          active: item.status === "1",
          token: item.token,
        };
      });
      setData(mapped);
    }
  }, [apiRes]);

  // Filtered list
  const list = useMemo(() => {
    return data.filter((w) => {
      if (accountFilter && w.accountName !== accountFilter) return false;
      if (eventFilter && w.event !== eventFilter) return false;
      if (statusFilter) {
        const st = w.active ? "Hoạt động" : "Không hoạt động";
        if (st !== statusFilter) return false;
      }
      if (searchTerm) {
        const t = searchTerm.toLowerCase();
        if (searchField === "all") {
          // Search in all fields
          return (
            w.id.toString().toLowerCase().includes(t) ||
            w.accountName.toLowerCase().includes(t) ||
            w.accountNumber.toLowerCase().includes(t) ||
            w.event.toLowerCase().includes(t) ||
            w.endpoint.toLowerCase().includes(t) ||
            w.token.toLowerCase().includes(t)
          );
        } else if (searchField === "id") {
          return w.id.toString().toLowerCase().includes(t);
        } else if (searchField === "account") {
          return (
            w.accountName.toLowerCase().includes(t) ||
            w.accountNumber.toLowerCase().includes(t)
          );
        } else if (searchField === "event") {
          return w.event.toLowerCase().includes(t);
        } else if (searchField === "endpoint") {
          return w.endpoint.toLowerCase().includes(t);
        } else if (searchField === "token") {
          return w.token.toLowerCase().includes(t);
        }
      }
      return true;
    });
  }, [data, accountFilter, eventFilter, statusFilter, searchTerm, searchField]);

  // Paginated data
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return list.slice(startIndex, endIndex);
  }, [list, currentPage, itemsPerPage]);

  // Total pages
  const totalPages = Math.ceil(list.length / itemsPerPage);

  const toggleActive = async (id) => {
    const token = localStorage.getItem("token");
    const user_id = localStorage.getItem("user_id");
    try {
      await axios.post(
        `${API_BASE_URL}/hooks`,
        new URLSearchParams({ action: "update", user_id, webhook_id: id }),
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      // Update local state on success
      setData((prev) =>
        prev.map((w) => (w.id === id ? { ...w, active: !w.active } : w))
      );
    } catch (err) {
      // Handle error silently or show user notification
    }
  };

  const openToken = (w) => {
    setSelected(w);
    setShowFullToken(false);
    setShowModal(true);
  };

  const copyToken = () => {
    navigator.clipboard.writeText(selected.token || "");
    setCopySuccess(true);
    setTimeout(() => setCopySuccess(false), 2000);
  };

  // Edit webhook state and handlers
  const openEdit = (w) => {
    setSelected(w);
    setEditEvent(w.event);
    setEditEndpoint("");
    setShowEditModal(true);
  };

  const updateWebhook = async () => {
    const token = localStorage.getItem("token");
    const user_id = localStorage.getItem("user_id");
    const endpoint = editEndpoint.trim() || selected.endpoint;

    try {
      const response = await axios.post(
        `${API_BASE_URL}/hooks`,
        new URLSearchParams({
          action: "edit",
          user_id: user_id,
          webhook_id: selected.id,
          new_url: endpoint,
          new_type: editEvent,
        }),
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      setData((prev) =>
        prev.map((w) =>
          w.id === selected.id ? { ...w, event: editEvent, endpoint } : w
        )
      );
      setShowEditModal(false);
    } catch (err) {
      console.error(
        "Error updating webhook:",
        err.response?.status,
        err.response?.data || err.message
      );
    }
  };

  // Open delete confirmation modal
  const openDeleteModal = (w) => {
    setDeleteTarget(w);
    setShowDeleteModal(true);
  };
  // Confirm and perform deletion
  const confirmDelete = async () => {
    if (!deleteTarget) return;
    const result = await callApi({
      action: "delete",
      webhook_id: deleteTarget.id,
    });
    if (result) {
      callApi({ action: "list" });
    }
    setShowDeleteModal(false);
    setDeleteTarget(null);
  };
  const cancelDelete = () => {
    setShowDeleteModal(false);
    setDeleteTarget(null);
  };

  // Handlers for Add Webhook
  const openAddModal = () => {
    setAddBankId("");
    setAddEvent("IN");
    setAddEndpoint("");
    setBankSearchTerm("");
    setShowBankDropdown(false);
    setShowAddModal(true);
  };

  const selectBank = (bank) => {
    setAddBankId(bank.id);
    setBankSearchTerm(bank.displayName);
    setShowBankDropdown(false);
  };

  const handleBankSearchChange = (e) => {
    const value = e.target.value;
    setBankSearchTerm(value);
    setAddBankId("");
    setShowBankDropdown(true);
  };
  const confirmAdd = async () => {
    const result = await callApi({
      action: "add",
      user_bank_id: addBankId,
      type: addEvent,
      webhook_url: addEndpoint.trim(),
    });
    if (result) callApi({ action: "list" });
    setShowAddModal(false);
  };
  const cancelAdd = () => {
    setShowBankDropdown(false);
    setShowAddModal(false);
  };

  // Utility functions
  const clearAllFilters = () => {
    setAccountFilter("");
    setEventFilter("");
    setStatusFilter("");
    setSearchTerm("");
    setSearchField("all");
    setCurrentPage(1);
  };

  const exportToExcel = () => {
    const exportData = list.map((item, idx) => ({
      STT: idx + 1,
      ID: item.id,
      "Tài khoản": item.accountName,
      "Số TK": item.accountNumber,
      "Sự kiện": item.event,
      Endpoint: item.endpoint,
      "Ngày thêm": item.addedAt,
      "Ngày cập nhật": item.updatedAt,
      "Trạng thái": item.active ? "Hoạt động" : "Không hoạt động",
      Token: item.token,
    }));

    const ws = XLSX.utils.json_to_sheet(exportData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "Webhooks");
    ws["!cols"] = [
      { wch: 5 },
      { wch: 8 },
      { wch: 20 },
      { wch: 15 },
      { wch: 15 },
      { wch: 40 },
      { wch: 20 },
      { wch: 20 },
      { wch: 15 },
      { wch: 30 },
    ];
    XLSX.writeFile(
      wb,
      `webhooks_${new Date().toISOString().split("T")[0]}.xlsx`
    );
  };

  const printTable = () => {
    const printWindow = window.open("", "_blank");
    const htmlContent = `<!DOCTYPE html><html><head><meta charset="utf-8"><title>Danh sách Webhook</title><style>
      body{font-family:Arial,sans-serif;margin:20px}
      table{width:100%;border-collapse:collapse}
      th,td{border:1px solid #ccc;padding:6px;font-size:12px;text-align:left}
      th{background:#f2f2f2}
      .text-center{text-align:center}
      .status-active{color:green;font-weight:bold}
      .status-inactive{color:red;font-weight:bold}
    </style></head><body>
      <h3>DANH SÁCH WEBHOOK</h3>
      <p>Ngày in: ${new Date().toLocaleDateString("vi-VN")}</p>
      <table><thead><tr>
        <th>STT</th><th>ID</th><th>Tài khoản</th><th>Số TK</th><th>Sự kiện</th><th>Endpoint</th><th>Ngày thêm</th><th>Trạng thái</th>
      </tr></thead><tbody>
      ${list
        .map(
          (item, idx) => `
        <tr>
          <td class="text-center">${idx + 1}</td>
          <td class="text-center">${item.id}</td>
          <td>${item.accountName}</td>
          <td>${item.accountNumber}</td>
          <td class="text-center">${item.event}</td>
          <td>${item.endpoint}</td>
          <td>${item.addedAt}</td>
          <td class="text-center ${
            item.active ? "status-active" : "status-inactive"
          }">
            ${item.active ? "Hoạt động" : "Không hoạt động"}
          </td>
        </tr>`
        )
        .join("")}
      </tbody></table>
    </body></html>`;
    printWindow.document.write(htmlContent);
    printWindow.document.close();
    printWindow.focus();
    setTimeout(() => {
      printWindow.print();
      printWindow.close();
    }, 200);
  };

  // Handle pagination
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handlePrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  return (
    <>
      {/* MasterLayout */}
      <MasterLayout>
        {/* Breadcrumb */}
        <Breadcrumb title="Danh sách Webhook" />
        {/* Add and filters toolbar */}
        <div className="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center gap-2 mb-3">
          <div className="d-md-flex w-100 gap-2">
            {/* filters toolbar */}
            <select
              className="form-select"
              value={accountFilter}
              onChange={(e) => setAccountFilter(e.target.value)}
            >
              <option value="">Tất cả tài khoản</option>
              {Array.from(new Set(data.map((w) => w.accountName))).map(
                (name) => (
                  <option key={name} value={name}>
                    {name}
                  </option>
                )
              )}
            </select>
            <select
              className="form-select mt-3 mt-md-0"
              value={eventFilter}
              onChange={(e) => setEventFilter(e.target.value)}
            >
              <option value="">Tất cả sự kiện</option>
              {Array.from(new Set(data.map((w) => w.event))).map((ev) => (
                <option key={ev} value={ev}>
                  {getEventLabel(ev)}
                </option>
              ))}
            </select>
            <select
              className="form-select mt-3 mt-md-0"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="">Tất cả trạng thái</option>
              <option value="Hoạt động">Hoạt động</option>
              <option value="Không hoạt động">Không hoạt động</option>
            </select>
          </div>

          <div className="d-none d-md-flex gap-2 w-100 justify-content-md-end">
            <button className="btn btn-effect btn-sm" onClick={exportToExcel}>
              <Icon className="me-1" icon="mdi:file-excel" /> Excel
            </button>
            <button className="btn btn-effect btn-sm" onClick={printTable}>
              <Icon className="me-1" icon="mdi:file-pdf" /> Print
            </button>
          </div>
        </div>
        <div className="card mb-4">
          <div className="card-header d-flex justify-content-between align-items-center">
            <div className="d-none d-md-inline">
              <input
                type="search"
                className="form-control mt-md-0 mt-3"
                placeholder="Tìm kiếm..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div>
              <button className="btn-effect btn btn-sm" onClick={openAddModal}>
                <Icon className="mx-2" icon="solar:add-circle-outline" />
                Thêm webhook
              </button>
            </div>
          </div>
          <div className="card-body p-3">
            <div className="table-responsive">
              <table className="table bordered-table mb-0">
                <thead>
                  <tr className="text-center">
                    <th className="id">ID</th>
                    <th style={{ width: "150px" }}>Tài khoản</th>
                    <th className="text-center">Sự kiện</th>
                    <th>Endpoint</th>
                    <th>Thêm/Cập nhật</th>
                    <th className="text-center">Hoạt động</th>
                    <th>Token</th>
                    <th>Hành động</th>
                  </tr>
                </thead>
                <tbody>
                  {list.length > 0 ? (
                    paginatedData.map((w) => (
                      <tr key={w.id}>
                        <td>{w.id}</td>
                        <td className="text-center" style={{ width: "150px" }}>
                          <img
                            src={w.accountLogo}
                            alt={w.accountName}
                            width={80}
                            className="me-2"
                          />
                          <br />
                          {w.accountNumber}
                        </td>
                        <td className="text-center">
                          <button className="alert alert-primary p-4 small">
                            {getEventLabel(w.event)}
                          </button>
                        </td>
                        <td
                          style={{
                            maxWidth: "300px",
                            wordBreak: "break-all",
                          }}
                        >
                          {w.endpoint}
                        </td>
                        <td>
                          <small>{w.addedAt}</small>
                          <hr />
                          <small>{w.updatedAt}</small>
                        </td>
                        <td className="text-center">
                          <label className="custom-switch">
                            <input
                              type="checkbox"
                              checked={w.active}
                              onChange={() => toggleActive(w.id)}
                            />
                            <span className="slider round"></span>
                          </label>
                        </td>
                        <td>
                          <button
                            className="btn btn-success"
                            onClick={() => openToken(w)}
                          >
                            <Icon icon="mdi:key" role="button" />
                          </button>
                        </td>
                        <td>
                          <div className="d-flex gap-3">
                            <button
                              className="btn btn-success"
                              title="Sửa webhook"
                              style={{ cursor: "pointer" }}
                              onClick={() => openEdit(w)}
                            >
                              <Icon icon="mdi:pencil" />
                            </button>
                            <button
                              className="btn btn-success"
                              title="Xóa webhook"
                              style={{ cursor: "pointer" }}
                              onClick={() => openDeleteModal(w)}
                            >
                              <Icon icon="mdi:delete" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={8} className="text-center p-4">
                        Không có dữ liệu để hiển thị
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
            {/* Pagination */}

            <div className="d-flex justify-content-end mt-3">
              <nav>
                <ul className="pagination pagination-sm mb-0">
                  <li
                    className={`page-item ${
                      currentPage === 1 ? "disabled" : ""
                    }`}
                    onClick={handlePrevPage}
                  >
                    <button className="page-link" disabled={currentPage === 1}>
                      &laquo;
                    </button>
                  </li>
                  {Array.from({ length: totalPages }, (_, i) => (
                    <li
                      key={i + 1}
                      className={`page-item ${
                        currentPage === i + 1 ? "active" : ""
                      }`}
                      onClick={() => handlePageChange(i + 1)}
                    >
                      <button className="page-link">{i + 1}</button>
                    </li>
                  ))}
                  <li
                    className={`page-item ${
                      currentPage === totalPages ? "disabled" : ""
                    }`}
                    onClick={handleNextPage}
                  >
                    <button
                      className="page-link"
                      disabled={currentPage === totalPages}
                    >
                      &raquo;
                    </button>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        </div>
      </MasterLayout>
      {/* Token Modal */}
      {showModal && selected && (
        <>
          <div className="modal-backdrop fade show"></div>
          <div className="modal fade show" style={{ display: "block" }}>
            <div className="token-modal modal-dialog-centered">
              <div className="modal-content">
                <div className="modal-header bg-success text-white justify-content-between">
                  <p className="m-0">Token webhook: {selected.accountNumber}</p>
                  <button
                    type="button"
                    className="btn bg-white py-0"
                    onClick={() => setShowModal(false)}
                  >
                    x
                  </button>
                </div>
                <div className="modal-body d-flex align-items-center gap-2">
                  <input
                    type="text"
                    readOnly
                    className="form-control"
                    value={
                      showFullToken
                        ? selected.token
                        : `${selected.token.slice(0, 9)}${"*".repeat(
                            Math.max(0, selected.token.length - 9)
                          )}`
                    }
                  />
                  <Icon
                    icon={showFullToken ? "mdi:eye-off" : "mdi:eye"}
                    onClick={() => setShowFullToken(!showFullToken)}
                    role="button"
                  />
                  <button
                    className="btn btn-success btn-sm"
                    onClick={copyToken}
                  >
                    {copySuccess ? (
                      <Icon icon="mdi:check" className="text-white" />
                    ) : (
                      <Icon icon="mdi:content-copy" />
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
      {/* Edit Webhook Modal */}
      {showEditModal && selected && (
        <>
          <div className="modal-backdrop fade show"></div>
          <div className="modal fade show" style={{ display: "block" }}>
            <div className="modal-dialog modal-dialog-centered">
              <div className="modal-content">
                <div className="modal-header bg-success text-white justify-content-between">
                  <p className="m-0">
                    Webhook tài khoản: {selected.accountNumber}
                  </p>
                  <button
                    type="button"
                    className="btn bg-white py-0"
                    onClick={() => setShowEditModal(false)}
                  >
                    x
                  </button>
                </div>
                <div className="modal-body">
                  <p className="py-2 mb-0 fw-medium hook-account">
                    Cổng thanh toán:
                  </p>
                  <input
                    type="text"
                    readOnly
                    className="form-control mb-2"
                    value={selected.accountName}
                  />
                  <p className="py-2 mb-0 fw-medium hook-account">Sự kiện:</p>
                  <select
                    className="form-select mb-3"
                    id="hook-type-select"
                    name="hook-type-select"
                    value={editEvent}
                    onChange={(e) => setEditEvent(e.target.value)}
                  >
                    <option value="IN">Nhận</option>
                    <option value="OUT">Gửi</option>
                    <option value="ALL">Gửi và nhận</option>
                  </select>
                  <p className="py-2 mb-0 fw-medium hook-account">
                    Webhook endpoint:{" "}
                    <span className="text-muted">
                      (Để trống nếu không thay đổi)
                    </span>
                  </p>
                  <input
                    type="text"
                    className="form-control mb-2"
                    placeholder="Nhập webhook endpoint mới..."
                    value={editEndpoint}
                    onChange={(e) => setEditEndpoint(e.target.value)}
                  />
                </div>
                <div className="modal-footer">
                  <button
                    className="btn btn-success me-2"
                    onClick={updateWebhook}
                  >
                    Cập nhật
                  </button>
                  <button
                    className="btn btn-secondary"
                    onClick={() => setShowEditModal(false)}
                  >
                    Đóng
                  </button>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
      {/* Delete Confirmation Modal */}
      {showDeleteModal && deleteTarget && (
        <>
          <div className="modal-backdrop fade show"></div>
          <div className="modal fade show" style={{ display: "block" }}>
            <div className="modal-dialog modal-dialog-centered">
              <div className="modal-content">
                <div className="modal-header">
                  <p className="modal-title">Xác nhận xóa webhook</p>
                  <button
                    type="button"
                    className="btn-close"
                    onClick={cancelDelete}
                  ></button>
                </div>
                <div className="modal-body">
                  Bạn có chắc muốn xóa webhook cho tài khoản{" "}
                  <strong>{deleteTarget.accountNumber}</strong>?
                </div>
                <div className="modal-footer">
                  <button className="btn btn-danger" onClick={confirmDelete}>
                    Xóa
                  </button>
                  <button className="btn btn-secondary" onClick={cancelDelete}>
                    Hủy
                  </button>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
      {/* Add Webhook Modal */}
      {showAddModal && (
        <>
          <div className="modal-backdrop fade show"></div>
          <div className="modal fade show" style={{ display: "block" }}>
            <div className="modal-dialog modal-dialog-centered modal-lg">
              <div className="modal-content">
                <div className="modal-header bg-success text-white justify-content-between">
                  <p className="m-0">Thêm webhook mới</p>
                  <button
                    type="button"
                    className="btn-close"
                    onClick={cancelAdd}
                  ></button>
                </div>
                <div className="modal-body">
                  <p className="fw-medium">Chọn tài khoản ngân hàng:</p>
                  <div className="position-relative">
                    <input
                      type="text"
                      className="form-control mb-3"
                      placeholder="Tìm kiếm theo STK hoặc tên ngân hàng... (Click để xem tất cả)"
                      value={bankSearchTerm}
                      onChange={handleBankSearchChange}
                      onFocus={() => setShowBankDropdown(true)}
                      onClick={() => {
                        if (!bankSearchTerm) {
                          setShowBankDropdown(true);
                        }
                      }}
                    />
                    {showBankDropdown && filteredBankAccounts.length > 0 && (
                      <div
                        className="dropdown-menu show position-absolute w-100"
                        style={{
                          zIndex: 1050,
                          maxHeight: "200px",
                          overflowY: "auto",
                          minWidth: "400px",
                        }}
                      >
                        {bankLoading ? (
                          <div className="dropdown-item-text">Đang tải...</div>
                        ) : (
                          <>
                            {/* Hiển thị tất cả nếu không có search term */}
                            {!bankSearchTerm && (
                              <div className="dropdown-item-text fw-bold border-bottom">
                                Tất cả tài khoản ({filteredBankAccounts.length})
                              </div>
                            )}
                            {filteredBankAccounts.map((bank) => (
                              <button
                                key={`${bank.id}-${bank.accountType}`}
                                type="button"
                                className="dropdown-item text-start"
                                onClick={() => selectBank(bank)}
                                style={{
                                  whiteSpace: "nowrap",
                                  overflow: "hidden",
                                  textOverflow: "ellipsis",
                                }}
                              >
                                {bank.accountType === "va" ? (
                                  <>
                                    <strong>{bank.vaNumber}</strong> -{" "}
                                    {bank.bankName}{" "}
                                    <span className="badge bg-primary">VA</span>
                                  </>
                                ) : (
                                  <>
                                    <strong>{bank.accountNumber}</strong> -{" "}
                                    {bank.bankName}
                                  </>
                                )}
                              </button>
                            ))}
                          </>
                        )}
                      </div>
                    )}
                    {showBankDropdown &&
                      !bankLoading &&
                      filteredBankAccounts.length === 0 &&
                      bankSearchTerm && (
                        <div
                          className="dropdown-menu show position-absolute w-100"
                          style={{ zIndex: 1050 }}
                        >
                          <div className="dropdown-item-text">
                            Không tìm thấy tài khoản phù hợp với "
                            {bankSearchTerm}"
                          </div>
                        </div>
                      )}
                  </div>
                  <p className="fw-medium">Sự kiện:</p>
                  <select
                    className="form-select mb-3"
                    value={addEvent}
                    onChange={(e) => setAddEvent(e.target.value)}
                  >
                    <option value="IN">Nhận</option>
                    <option value="OUT">Gửi</option>
                    <option value="ALL">Gửi và nhận</option>
                  </select>
                  <p className="fw-medium">Endpoint:</p>
                  <input
                    type="text"
                    className="form-control mb-3"
                    placeholder="Nhập URL webhook"
                    value={addEndpoint}
                    onChange={(e) => setAddEndpoint(e.target.value)}
                  />
                </div>
                <div className="modal-footer">
                  <button
                    className="btn btn-success"
                    onClick={confirmAdd}
                    disabled={!addBankId}
                  >
                    Thêm
                  </button>
                  <button className="btn btn-secondary" onClick={cancelAdd}>
                    Đóng
                  </button>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </>
  );
};

export default WebhookPage;
