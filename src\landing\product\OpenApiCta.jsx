import React from "react";
import ctaImg from "../../assets/landing/img/normal/customer_support.jpg";
import ctaIcon from "../../assets/landing/img/icon/call_1.svg";
import titleShape2 from "../../assets/landing/img/theme-img/title_shape_2.svg";

const cta = {
  img: ctaImg,
  icon: ctaIcon,
  title: "TRIỂN KHAI",
  subtitle: "Hành động ngay",
  secTitle: (
    <>
      Triển khai giải pháp <span className="text-theme">Cho doanh nghiệp</span>{" "}
      của bạn ngay
    </>
  ),
  btn: {
    href: "/bang-gia",
    text: "Xem bảng giá",
    icon: "fa-solid fa-right-long",
  },
};

const OpenApiCta = () => (
  <section className="space-bottom">
    <div className="container">
      <div className="cta-box">
        <div className="row">
          <div className="col-lg-5">
            <div className="cta-box_img">
              <img src={cta.img} alt="Image" />
            </div>
          </div>
          <div className="col-lg-7">
            <div className="cta-box_content">
              <div className="cta-box_icon">
                <img src={cta.icon} alt="Icon" />
              </div>
              <div className="title-area mb-35">
                <div className="shadow-title">{cta.title}</div>
                <span className="sub-title">
                  <div className="icon-masking me-2">
                    <span
                      className="mask-icon"
                      data-mask-src={titleShape2}
                    ></span>
                    <img src={titleShape2} alt="shape" />
                  </div>
                  {cta.subtitle}
                </span>
                <h3 className="sec-title">{cta.secTitle}</h3>
              </div>
              <a href={cta.btn.href} className="th-btn">
                {cta.btn.text} <i className={cta.btn.icon}></i>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
);

export default OpenApiCta;
