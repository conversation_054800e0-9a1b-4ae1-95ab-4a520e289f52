import React from "react";
import heroBg from "../../assets/landing/img/hero/hero_bg_7_1.png";
import heroImg from "../../assets/landing/img/hero/hero_img_7_1.png";

const Hero = () => (
  <div className="th-hero-wrapper hero-7" id="hero">
    <div className="hero-inner">
      <div
        className="th-hero-bg"
        style={{
          backgroundImage: `url(${heroBg})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
        }}
      ></div>
      <div className="container th-container4">
        <div className="row justify-content-center">
          <div className="col-lg-8">
            <div className="hero-style7 text-center">
              {/* <span className="sub-title style1">We’re Leading Startup Agency</span> */}
              <h1 className="hero-title">
                <PERSON><PERSON><PERSON><PERSON>á<PERSON>
                <span className="text-theme">Tự <PERSON></span> <br />
                <PERSON><PERSON><PERSON><PERSON>
              </h1>
              <p className="hero-text">
                Giải pháp thanh toán tự động, Open Banking Pay2S là đối tác liên
                kết trực tiếp với ngân hàng, an toàn - bảo mật - ổn định
              </p>
              <div className="btn-group mt-35 justify-content-center">
                <a href="/client/signup" className="th-btn style-radius">
                  Đăng ký miễn phí ngay
                </a>
                <a href="/lien-he" className="th-btn style6 style-radius">
                  Liên hệ tư vấn
                </a>
              </div>
            </div>
          </div>
        </div>
        <div className="th-hero-thumb">
          <img src={heroImg} alt="img" />
        </div>
      </div>
    </div>
  </div>
);

export default Hero;
