import React from "react";
import heroImg from "../../assets/landing/img/hero/hero_img_1_1.png";
import shape1 from "../../assets/landing/img/hero/hero_shape_1_1.svg";
import shape2 from "../../assets/landing/img/hero/hero_shape_1_2.svg";
import shape3 from "../../assets/landing/img/hero/hero_shape_1_3.svg";

const HERO_TITLE = [
  "KẾT NỐI TRỰC TIẾP",
  <>
    7 NGÂN HÀNG LỚN<span className="text-theme fw-medium"> VỚI PAY2S</span>
  </>,
];

function OpenApiHero() {
  return (
    <section className="th-hero-wrapper hero-1" id="hero">
      <div className="hero-img tilt-active">
        <img src={heroImg} alt="Hero" loading="lazy" />
      </div>
      <div className="container">
        <div className="hero-style1">
          <span className="hero-subtitle">API Open Banking</span>
          {HERO_TITLE.map((title, idx) => (
            <h1 className="hero-title" key={idx}>
              {title}
            </h1>
          ))}
          <p className="hero-text">
            Với Pay2S, tích hợp trực tiếp API ngân hàng (ACB, Techcombank,
            Vietcombank, MB, TPBank, BIDV, ViettinBank), bạn có ngay:
          </p>
          <div className="btn-group">
            <a href="#anchor-sec-title" className="th-btn">
              Tìm hiểu thêm <i className="fa-regular fa-arrow-right ms-2" />
            </a>
          </div>
        </div>
      </div>
      <div className="hero-shape1">
        <img src={shape1} alt="shape" loading="lazy" />
      </div>
      <div className="hero-shape2">
        <img src={shape2} alt="shape" loading="lazy" />
      </div>
      <div className="hero-shape3">
        <img src={shape3} alt="shape" loading="lazy" />
      </div>
    </section>
  );
}

export default OpenApiHero;
