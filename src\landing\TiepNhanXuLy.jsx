import React from "react";
import SEOHead from "../components/SEOHead";
import Header from "./Header";
import Footer from "./Footer";
import Breadcrumb from "./Breadcrumb";

const TiepNhanXuLy = () => {
  return (
    <>
      <SEOHead pageKey="tiepNhanXuLy" />
      <Header />
      <Breadcrumb title="Quy trình tiếp nhận và xử lý khiếu nại" />

      {/* Content */}
      <section className="space-top space-extra-bottom">
        <div className="container">
          <div className="row">
            <div className="col-xxl-8 col-lg-8">
              <div className="page-single">
                <div className="kt-inside-inner-col">
                  <p>🛠️ Quy trình tiếp nhận và xử lý khiếu nại – Pay2S</p>

                  <p>
                    Tại <strong>Pay2S</strong>, chúng tôi luôn cam kết cung cấp
                    dịch vụ minh bạch, an toàn và hỗ trợ khách hàng nhan<PERSON> chóng,
                    chuyên nghiệp. Trong trường hợp phát sinh sự cố hoặc khách
                    hàng không hài lòng về dịch vụ, Pay2S áp dụng quy trình tiếp
                    nhận và xử lý khiếu nại như sau:
                  </p>

                  <hr className="wp-block-separator has-alpha-channel-opacity" />

                  <h3 className="wp-block-heading">
                    📌 1. Các kênh tiếp nhận khiếu nại chính thức
                  </h3>
                  <p>
                    Khách hàng có thể gửi khiếu nại thông qua một trong các kênh
                    sau:
                  </p>
                  <ul className="wp-block-list">
                    <li>
                      <strong>Email hỗ trợ:</strong>{" "}
                      <a href="mailto:<EMAIL>"><EMAIL></a>
                    </li>
                    <li>
                      <strong>Hotline:</strong> 1900 633 988
                    </li>
                    <li>
                      <strong>Trung tâm hỗ trợ khách hàng:</strong>{" "}
                      <a href="https://support.pay2s.vn">
                        https://support.pay2s.vn
                      </a>
                    </li>
                  </ul>

                  <h3 className="wp-block-heading">
                    ⏳ 3. Thời gian tiếp nhận và phản hồi
                  </h3>
                  <p>
                    Ngay sau khi tiếp nhận khiếu nại, hệ thống của Pay2S sẽ:
                  </p>
                  <ul className="wp-block-list">
                    <li>
                      <strong>Tự động gửi xác nhận tiếp nhận</strong> trong vòng{" "}
                      <strong>24 giờ làm việc</strong>
                    </li>
                    <li>
                      Cung cấp{" "}
                      <strong>mã theo dõi khiếu nại (ticket ID)</strong>
                    </li>
                    <li>Phân loại khiếu nại theo mức độ ưu tiên</li>
                  </ul>

                  <h3 className="wp-block-heading">🧭 Liên hệ hỗ trợ</h3>
                  <p>
                    <strong>Hệ thống hỗ trợ:</strong>{" "}
                    <a href="https://support.pay2s.vn">
                      https://support.pay2s.vn
                    </a>
                  </p>
                  <p>
                    <strong>Email hỗ trợ:</strong>{" "}
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                  </p>
                  <p>
                    <strong>Hotline:</strong> 1900 633 988
                  </p>
                  <p>
                    <strong>Fanpage Facebook:</strong>{" "}
                    <a href="https://fb.com/pay2s.vn">fb.com/pay2s.vn</a>
                  </p>
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="col-xxl-4 col-lg-4">
              <aside className="sidebar-area">
                <div className="widget widget_banner">
                  <h4 className="widget_title">Tài liệu tích hợp</h4>
                  <div className="download-widget-wrap">
                    <a href="https://docs.pay2s.vn" className="th-btn">
                      <i className="fa-light fa-file-pdf me-2"></i>Xem Docs
                    </a>
                  </div>
                </div>
                <div className="widget widget_banner">
                  <div className="widget-banner">
                    <span className="text">LIÊN HỆ NGAY</span>
                    <h2 className="title">Bạn có thắc mắc?</h2>
                    <a href="/lien-he" className="th-btn style3">
                      Liên hệ<i className="fas fa-arrow-right ms-2"></i>
                    </a>
                  </div>
                </div>
              </aside>
            </div>
          </div>
        </div>
      </section>
      <Footer />
    </>
  );
};

export default TiepNhanXuLy;
