import React from "react";
import titleShape2 from "../../assets/landing/img/theme-img/title_shape_2.svg";
import techShape1 from "../../assets/landing/img/shape/tech_shape_1.png";
import square1 from "../../assets/landing/img/shape/square_1.png";
import OpenApiBankSlider from "./OpenApiBankSlider";
import OpenApiCertificates from "./OpenApiCertificates";
const BankPartnersSection = () => (
  <section id="team-sec">
    <div className="container space">
      <div className="title-area text-center">
        <div className="shadow-title">API ngân hàng</div>
        <span className="sub-title">
          <div className="icon-masking me-2">
            <span className="mask-icon" data-mask-src={titleShape2}></span>
            <img src={titleShape2} alt="shape" />
          </div>
          Pay2S tự hào đồng hành
        </span>
        <h2 className="sec-title">
          <PERSON><PERSON><PERSON> tác cùng <span className="text-theme"><PERSON><PERSON><PERSON><PERSON></span>
        </h2>
      </div>
    </div>
    <OpenApiBankSlider />
    <OpenApiCertificates />
    <div className="shape-mockup" data-top="0" data-right="0">
      <img src={techShape1} alt="shape" />
    </div>
    <div className="shape-mockup" data-top="0%" data-left="0%">
      <img src={square1} alt="shape" />
    </div>
  </section>
);

export default BankPartnersSection;
