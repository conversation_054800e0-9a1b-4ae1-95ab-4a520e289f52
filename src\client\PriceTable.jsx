import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import MasterLayout from "../masterLayout/MasterLayout";
import Breadcrumb from "../components/Breadcrumb";
import { callPlan<PERSON>pi, callOrder<PERSON>pi, callUser<PERSON>pi } from "../callapi/OrderApi";

const mapBillingCycleToCode = (cycleString) => {
  const cycle = cycleString?.toLowerCase();
  if (cycle === "monthly") return "1m";
  if (cycle === "quarterly") return "3m";
  if (cycle === "semi-annually" || cycle === "semi") return "6m";
  if (cycle === "annually") return "12m";
  return null;
};

const UnpaidInvoicePopup = ({ onClose, onConfirm }) => (
  <div
    className="modal show d-block"
    tabIndex="-1"
    style={{ backgroundColor: "rgba(0,0,0,0.5)" }}
  >
    <div className="modal-dialog modal-dialog-centered">
      <div className="modal-content">
        <div className="modal-header">
          <h5 className="modal-title">Thông báo</h5>
          <button
            type="button"
            className="btn-close"
            onClick={onClose}
          ></button>
        </div>
        <div className="modal-body">
          <p>
            Bạn có một hóa đơn chưa hoàn tất. Vui lòng thanh toán hóa đơn có sẵn
            trước khi thực hiện giao dịch mới.
          </p>
        </div>
        <div className="modal-footer">
          <button type="button" className="btn btn-secondary" onClick={onClose}>
            Đóng
          </button>
          <button type="button" className="btn btn-primary" onClick={onConfirm}>
            Đến trang hóa đơn
          </button>
        </div>
      </div>
    </div>
  </div>
);

const PricingPage = () => {
  const [plans, setPlans] = useState([]);
  const [activePlanInfo, setActivePlanInfo] = useState(null);
  const [loading, setLoading] = useState(true);
  const [hasUnpaidInvoice, setHasUnpaidInvoice] = useState(false);
  const [showPopup, setShowPopup] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      const userId = localStorage.getItem("user_id");

      if (!userId) {
        try {
          const planResponse = await callPlanApi({});
          if (planResponse.data.status) setPlans(planResponse.data.plans);
        } catch (error) {
          console.error("Lỗi:", error);
        } finally {
          setLoading(false);
        }
        return;
      }

      try {
        const [planResponse, userResponse, invoiceResponse] = await Promise.all(
          [
            callPlanApi({ user_id: userId }),
            callUserApi({ action: "get_profile", user_id: userId }),
            callOrderApi({ action: "get_user_invoices", user_id: userId }),
          ]
        );

        const allPlans = planResponse.data.status
          ? planResponse.data.plans
          : [];
        setPlans(allPlans);

        const unpaidInvoice = invoiceResponse.data.invoices?.find(
          (inv) => inv.status === "unpaid"
        );
        if (unpaidInvoice) {
          setHasUnpaidInvoice(true);
          setShowPopup(true);
        }

        const userData = userResponse.data.status
          ? userResponse.data.message
          : null;

        // Tìm plan hiện tại từ API response (plan có current_plan field)
        const currentPlanFromApi = allPlans.find((p) => p.current_plan);

        if (currentPlanFromApi && userData && userData.billingcycle) {
          const activeBillingCycle = userData.billingcycle;
          const cycleCode = mapBillingCycleToCode(activeBillingCycle);

          setActivePlanInfo({
            planId: currentPlanFromApi.id,
            cycle: cycleCode,
            planAccountId: currentPlanFromApi.plan_account_id, // Lấy từ API response
          });
        }
        // Fallback logic nếu API không trả về current_plan field
        else if (
          userData &&
          userData.current_plan &&
          userData.billingcycle &&
          allPlans.length > 0
        ) {
          const activePlanName = userData.current_plan;
          const activeBillingCycle = userData.billingcycle;
          const currentPlanObject = allPlans.find(
            (p) => p.name.toLowerCase() === activePlanName.toLowerCase()
          );

          if (currentPlanObject) {
            const cycleCode = mapBillingCycleToCode(activeBillingCycle);
            setActivePlanInfo({
              planId: currentPlanObject.id,
              cycle: cycleCode,
              planAccountId: currentPlanObject.plan_account_id, // Thêm plan_account_id từ API
            });
          }
        }
      } catch (error) {
        console.error("Lỗi khi tải dữ liệu trang giá:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const formatPlanData = (plan) => ({
    name: plan.name.toUpperCase(),
    price: plan.monthly,
    accounts: plan.bank_number,
    benefits: [
      "Không giới hạn thay đổi ngân hàng",
      "Không giới hạn website, ứng dụng",
      "Không giới hạn giao dịch",
      "Cập nhật 2s/lần",
    ],
    discount: "Giảm 10% khi mua từ 12 tháng",
    options: [
      { label: `1 tháng = ${plan.monthly.toLocaleString()} đ`, value: "1m" },
      { label: `3 tháng = ${plan.quarterly.toLocaleString()} đ`, value: "3m" },
      { label: `6 tháng = ${plan.semi.toLocaleString()} đ`, value: "6m" },
      {
        label: `12 tháng = ${plan.annually.toLocaleString()} đ (-10%)`,
        value: "12m",
      },
    ],
  });

  const getAmountFromCycle = (plan, cycle) => {
    switch (cycle) {
      case "1m":
        return plan.monthly;
      case "3m":
        return plan.quarterly;
      case "6m":
        return plan.semi;
      case "12m":
        return plan.annually;
      default:
        return plan.monthly;
    }
  };

  const mapCycleToPeriod = (cycle) => {
    switch (cycle) {
      case "1m":
        return "monthly";
      case "3m":
        return "quarterly";
      case "6m":
        return "semi-annually";
      case "12m":
        return "annually";
      default:
        return "monthly";
    }
  };

  if (loading) {
    return (
      <MasterLayout>
        <Breadcrumb title="Bảng giá" />
        <div className="container">
          <p>Đang tải...</p>
        </div>
      </MasterLayout>
    );
  }

  return (
    <MasterLayout>
      {showPopup && (
        <UnpaidInvoicePopup
          onClose={() => setShowPopup(false)}
          onConfirm={() => navigate("/client/invoice")}
        />
      )}
      <Breadcrumb title="Bảng giá" />
      {activePlanInfo && !hasUnpaidInvoice && (
        <div className="container mb-3">
          <div className="alert alert-success">
            Chỉ có thể gia hạn gói cùng kỳ hạn đã đăng ký, hoặc nâng cấp lên gói
            cao hơn cùng kỳ hạn. Nếu muốn thay đổi kỳ hạn vui lòng liên hệ với
            chúng tôi.
          </div>
        </div>
      )}
      <div className="container">
        <div className="row g-4">
          {plans.map((plan) => {
            const displayData = formatPlanData(plan);
            let buttonText = "CHỌN GÓI";
            let buttonDisabled = false;
            let isCurrentPlan = false;

            // === LOGIC SỬA LỖI TẠI ĐÂY ===
            // 1. Luôn xác định trạng thái của plan (hiện tại, nâng cấp, hạ cấp) nếu có activePlanInfo
            if (activePlanInfo) {
              const isDowngrade = plan.id < activePlanInfo.planId;
              isCurrentPlan = plan.id === activePlanInfo.planId;

              if (isCurrentPlan) {
                buttonText = "ĐANG SỬ DỤNG";
                buttonDisabled = true;
              } else if (isDowngrade) {
                buttonText = "KHÔNG THỂ CHỌN";
                buttonDisabled = true;
              } else {
                buttonText = "NÂNG CẤP";
                buttonDisabled = false; // Mặc định cho phép nâng cấp
              }
            }

            // 2. Sau khi xác định xong, kiểm tra điều kiện ghi đè (hóa đơn chưa thanh toán)
            // if (hasUnpaidInvoice) {
            //   buttonDisabled = true; // Khóa tất cả các nút nếu có hóa đơn chưa thanh toán
            // }
            // === KẾT THÚC LOGIC SỬA LỖI ===

            // Bỏ khóa nút khi có hóa đơn chưa thanh toán để người dùng vẫn có thể thao tác
            // if (hasUnpaidInvoice) {
            //   buttonDisabled = true;
            // }

            return (
              <div key={plan.id} className="col-12 col-sm-6 col-lg-4">
                <div
                  className={`card h-100 shadow border-0 text-dark dark:bg-[#1e293b] dark:text-white ${
                    isCurrentPlan ? "border-2 border-success" : ""
                  }`}
                >
                  <div className="card-body d-flex flex-column">
                    <div className="card-header border-0 bg-transparent p-0 mb-3">
                      <div className="d-flex justify-content-between align-items-center">
                        <h5 className="card-title text-success text-uppercase fw-bold m-0">
                          {displayData.name}
                        </h5>
                        {isCurrentPlan && (
                          <div className="badge bg-success">
                            Bạn đang sử dụng
                          </div>
                        )}
                      </div>
                    </div>
                    <hr />
                    <h4 className="fw-bolder mt-3">
                      {displayData.price.toLocaleString()}{" "}
                      <small className="fs-6 fw-normal">/1 tháng</small>
                    </h4>
                    <ul className="list-unstyled small mb-3">
                      <li className="fw-semibold text-success">
                        Tối đa {displayData.accounts} tài khoản ngân hàng
                      </li>
                      {displayData.benefits.map((benefit, idx) => (
                        <li key={idx} className="d-flex align-items-start">
                          <span className="me-2 text-success">✔</span> {benefit}
                        </li>
                      ))}
                      <li className="text-warning fst-italic">
                        {displayData.discount}
                      </li>
                    </ul>
                    <label className="form-label">Chọn kỳ hạn</label>

                    {activePlanInfo ? (
                      <input
                        type="text"
                        className="form-control mb-3"
                        readOnly
                        disabled
                        value={
                          displayData.options.find(
                            (o) => o.value === activePlanInfo.cycle
                          )?.label || ""
                        }
                      />
                    ) : (
                      <select
                        className="form-select mb-3 text-dark dark:bg-[#0f172a] dark:text-white"
                        id={`billing-cycle-${plan.id}`}
                      >
                        {displayData.options.map((opt, i) => (
                          <option key={i} value={opt.value}>
                            {opt.label}
                          </option>
                        ))}
                      </select>
                    )}

                    <div className="mt-auto">
                      <button
                        className="btn btn-success w-100 fw-semibold"
                        disabled={buttonDisabled}
                        onClick={async () => {
                          if (buttonDisabled) return;
                          const selectedCycle =
                            activePlanInfo?.cycle ||
                            document.getElementById(`billing-cycle-${plan.id}`)
                              ?.value ||
                            "1m";
                          const amount = getAmountFromCycle(
                            plan,
                            selectedCycle
                          );

                          if (buttonText === "NÂNG CẤP") {
                            // Thực hiện upgrade tại đây
                            const userId = localStorage.getItem("user_id");
                            const payload = {
                              action: "upgrade_plan",
                              user_id: userId,
                              new_plan_id: plan.id.toString(),
                              billing_cycle: mapCycleToPeriod(selectedCycle),
                              old_plan_account_id:
                                activePlanInfo.planAccountId.toString(),
                            };
                            try {
                              const response = await callOrderApi(payload);
                              if (response.data.status) {
                                const payUrl =
                                  response.data.invoiceDetails?.payUrl ||
                                  response.data.payUrl;
                                window.location.href = payUrl;
                              } else {
                                alert(
                                  response.data.message || "Nâng cấp thất bại"
                                );
                              }
                            } catch (err) {
                              console.error("Lỗi khi gọi API nâng cấp:", err);
                              alert("Có lỗi xảy ra khi nâng cấp gói");
                            }
                          } else {
                            // Chọn gói mới bình thường
                            const selectedOption = displayData.options.find(
                              (opt) => opt.value === selectedCycle
                            );
                            navigate("/client/checkout", {
                              state: {
                                planId: plan.id,
                                planName: plan.name,
                                billingCycle: selectedCycle,
                                amount: amount,
                                selectedOption: selectedOption?.label,
                                isUpgrade: false,
                                oldPlanAccountId: null,
                              },
                            });
                          }
                        }}
                      >
                        {buttonText}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </MasterLayout>
  );
};

export default PricingPage;
