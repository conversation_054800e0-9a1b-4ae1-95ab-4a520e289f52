import React from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import testiBg from "../../assets/landing/img/bg/testi_bg_2.png";
import shape1 from "../../assets/landing/img/shape/line_1.png";
import shape2 from "../../assets/landing/img/shape/line_2.png";
import quoteIcon from "../../assets/landing/img/icon/quote_left_2.svg";
import titleIcon from "../../assets/landing/img/theme-img/title_shape_2.svg";
import client4 from "../../assets/landing/img/testimonial/client-4.jpg";
import client2 from "../../assets/landing/img/testimonial/client-2.jpg";
import client3 from "../../assets/landing/img/testimonial/client-3.jpg";

const testimonials = [
  {
    img: client4,
    text: "VPSMMO sử dụng thử gói Starter để tr<PERSON><PERSON> nghiệm thì thấy quá đỉnh, nâng cấp sang gói Pro để add nhiều tài khoản hơn cho đa dạng với khách hàng của mình.",
    author: "Minh Sang",
    desig: "VPSMMO",
  },
  {
    img: client2,
    text: "Mình sử dụng cho cá nhân và cả khách hàng mua plugin của mình, khách hàng của mình rất hài lòng, tuy giờ cao điểm nhưng thông báo vẫn trả về tức thời.",
    author: "Minh Hải",
    desig: "dominhhai.com",
  },
  {
    img: client3,
    text: "Không giới hạn giao dịch là điểm ăn tiền tuyệt đối của Pay2S rồi, thuegpu.vn có lượng khách đông nên tích hợp tự động rất là tiết kiệm thời gian và tiền bạc.",
    author: "Trọng Nguyễn",
    desig: "thuegpu.vn",
  },
];

const breakpoints = {
  0: { slidesPerView: 1 },
  576: { slidesPerView: 1 },
  768: { slidesPerView: 1 },
  992: { slidesPerView: 1 },
  1200: { slidesPerView: 2 },
};

const Testimonial = () => (
  <section
    className="bg-auto space"
    style={{ backgroundImage: `url(${testiBg})` }}
  >
    <div className="container">
      <div className="title-area text-center">
        <span className="sub-title">
          <span className="icon-masking me-2">
            <span className="mask-icon bg-mask">
              <img src={titleIcon} alt="shape" />
            </span>
          </span>
          CUSTOMER FEEDBACK
        </span>
        <h2 className="sec-title">
          Khách hàng đã sử dụng <br />
          <span className="text-theme fw-normal"> Hài lòng như thế nào?</span>
        </h2>
      </div>
      <div className="slider-area">
        <Swiper
          className="th-slider has-shadow"
          breakpoints={breakpoints}
          spaceBetween={24}
          loop={true}
        >
          {testimonials.map((item, idx) => (
            <SwiperSlide key={idx}>
              <div className="testi-box">
                <div className="testi-box_img">
                  <img src={item.img} alt="Avater" />
                  <div className="testi-box_quote">
                    <img src={quoteIcon} alt="quote" />
                  </div>
                </div>
                <div className="testi-box_content">
                  <p className="testi-box_text">{item.text}</p>
                  <div className="testi-box_review">
                    {[...Array(5)].map((_, i) => (
                      <i className="fa-solid fa-star-sharp" key={i}></i>
                    ))}
                  </div>
                  <h3 className="box-title">{item.author}</h3>
                  <p className="testi-box_desig">{item.desig}</p>
                </div>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </div>
    <div
      className="shape-mockup moving d-none d-xl-block"
      style={{ bottom: 0, left: "10%" }}
    >
      <img src={shape1} alt="shape" />
    </div>
    <div
      className="shape-mockup jump d-none d-xl-block"
      style={{ top: "20%", right: "2%" }}
    >
      <img src={shape2} alt="shape" />
    </div>
  </section>
);

export default Testimonial;
