/* Blog Transition Styles - Sử dụng màu success chủ đạo */

/* Loading Bar ở top trang */
.blog-loading-bar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  z-index: 9999;
  background: rgba(43, 124, 81, 0.1);
}

.loading-progress {
  height: 100%;
  background: linear-gradient(90deg, 
    var(--success-main) 0%, 
    var(--success-hover) 50%, 
    var(--success-main) 100%
  );
  background-size: 200% 100%;
  animation: loading-progress 1.2s ease-in-out infinite;
  box-shadow: 0 0 8px rgba(43, 124, 81, 0.4);
}

@keyframes loading-progress {
  0% {
    background-position: 200% 0;
    transform: scaleX(0);
    transform-origin: left;
  }
  50% {
    background-position: 0% 0;
    transform: scaleX(0.7);
  }
  100% {
    background-position: -200% 0;
    transform: scaleX(1);
    transform-origin: right;
  }
}

/* Main Content Area */
.blog-transition-main {
  position: relative;
  min-height: calc(100vh - 140px);
}

/* Content Loading Overlay */
.blog-content-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 100;
}

.loading-dots {
  display: flex;
  gap: 8px;
  align-items: center;
}

.loading-dots .dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--success-main);
  animation: dot-bounce 1.4s ease-in-out infinite both;
}

.loading-dots .dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dots .dot:nth-child(2) { animation-delay: -0.16s; }
.loading-dots .dot:nth-child(3) { animation-delay: 0s; }

@keyframes dot-bounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* Content Wrapper */
.blog-content-wrapper {
  width: 100%;
  min-height: inherit;
}

/* Content Loader Component */
.content-loader-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 40px 20px;
}

.content-loader {
  text-align: center;
}

.loader-spinner {
  position: relative;
  width: 60px;
  height: 60px;
  margin: 0 auto 20px;
}

.spinner-ring {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-top-color: var(--success-main);
  border-radius: 50%;
  animation: spinner-rotate 1.2s linear infinite;
}

.spinner-ring:nth-child(2) {
  width: 80%;
  height: 80%;
  top: 10%;
  left: 10%;
  border-top-color: var(--success-hover);
  animation-delay: -0.4s;
  animation-duration: 1.8s;
}

.spinner-ring:nth-child(3) {
  width: 60%;
  height: 60%;
  top: 20%;
  left: 20%;
  border-top-color: var(--success-600);
  animation-delay: -0.8s;
  animation-duration: 2.4s;
}

@keyframes spinner-rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loader-text {
  color: var(--success-main);
  font-size: 14px;
  font-weight: 500;
  margin: 0;
}

/* Blog Skeleton Styles */
.blog-skeleton {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  padding: 20px;
}

.skeleton-card {
  background: #fff;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.skeleton-image {
  width: 100%;
  height: 200px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-shimmer 1.5s infinite;
}

.skeleton-content {
  padding: 20px;
}

.skeleton-line {
  height: 16px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-shimmer 1.5s infinite;
  border-radius: 4px;
  margin-bottom: 12px;
}

.skeleton-title {
  height: 20px;
  width: 85%;
}

.skeleton-text {
  width: 100%;
}

.skeleton-text.short {
  width: 60%;
}

.skeleton-meta {
  display: flex;
  gap: 15px;
  margin-top: 15px;
}

.skeleton-date,
.skeleton-category {
  height: 12px;
  width: 80px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-shimmer 1.5s infinite;
  border-radius: 4px;
}

@keyframes skeleton-shimmer {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Post Skeleton */
.post-skeleton {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.skeleton-breadcrumb {
  margin-bottom: 20px;
}

.skeleton-post-header {
  margin-bottom: 30px;
}

.skeleton-post-title {
  height: 32px;
  width: 90%;
  margin-bottom: 15px;
}

.skeleton-post-meta {
  display: flex;
  gap: 20px;
}

.skeleton-author {
  width: 100px;
}

.skeleton-featured-image {
  width: 100%;
  height: 300px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-shimmer 1.5s infinite;
  border-radius: 8px;
  margin-bottom: 30px;
}

.skeleton-post-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.skeleton-paragraph {
  height: 16px;
}

.skeleton-paragraph:nth-child(3n) {
  width: 75%;
}

.skeleton-paragraph:nth-child(5n) {
  width: 85%;
}

/* Responsive */
@media (max-width: 768px) {
  .blog-skeleton {
    grid-template-columns: 1fr;
    gap: 20px;
    padding: 15px;
  }
  
  .skeleton-image {
    height: 180px;
  }
  
  .skeleton-content {
    padding: 15px;
  }
  
  .loader-spinner {
    width: 50px;
    height: 50px;
  }
  
  .post-skeleton {
    padding: 15px;
  }
  
  .skeleton-featured-image {
    height: 200px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .skeleton-line,
  .skeleton-image,
  .skeleton-featured-image,
  .skeleton-date,
  .skeleton-category {
    background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
    background-size: 200% 100%;
  }
  
  .skeleton-card {
    background: #1a1a1a;
  }
  
  .loader-text {
    color: var(--success-hover);
  }
}
