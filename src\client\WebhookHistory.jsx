import MasterLayout from "../masterLayout/MasterLayout";
import Breadcrumb from "../components/Breadcrumb";
import React, { useState, useEffect } from "react";
import { Icon } from "@iconify/react";
import useWebhooksApi from "../callapi/Webhooks";
import DatePicker from "react-datepicker";
import * as XLSX from "xlsx";

import "react-datepicker/dist/react-datepicker.css";

// Component Modal thông báo
const NotificationModal = ({ show, message, type, onClose }) => {
  if (!show) return null;

  const headerClass = {
    success: "text-success",
    error: "text-danger",
    warning: "text-dark",
  }[type];

  const headerTitle = {
    success: "Thành công",
    error: "Lỗi",
    warning: "Cảnh báo",
  }[type];

  return (
    <>
      <div className="modal-backdrop fade show"></div>
      <div
        className="modal fade show"
        style={{ display: "block" }}
        tabIndex="-1"
      >
        <div className="modal-dialog modal-dialog-centered">
          <div className="modal-content">
            <div className={`modal-header ${headerClass}`}>
              <p className="modal-title">{headerTitle}</p>
              <button
                type="button"
                className="btn-close btn-close-white"
                onClick={onClose}
              ></button>
            </div>
            <div className="modal-body">
              <p>{message}</p>
            </div>
            <div className="modal-footer">
              <button className="btn btn-primary" onClick={onClose}>
                Đóng
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

const WebhookHistory = () => {
  // State cho filters và search
  const [searchTerm, setSearchTerm] = useState("");
  const [webhookFilter, setWebhookFilter] = useState("");
  const [dateFilter, setDateFilter] = useState(null); // Lọc theo ngày, dùng null cho DatePicker
  const [eventFilter, setEventFilter] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // State cho data
  const [webhookData, setWebhookData] = useState([]);
  const [resendingId, setResendingId] = useState(null); // State để theo dõi webhook đang được gửi lại

  // State cho modal
  const [showModal, setShowModal] = useState(false);
  const [modalTitle, setModalTitle] = useState("");
  const [modalContent, setModalContent] = useState("");
  const [modalType, setModalType] = useState(""); // "request" hoặc "response"
  const [notification, setNotification] = useState({
    show: false,
    message: "",
    type: "",
  });

  // Hook để gọi API
  const {
    data: apiData,
    loading,
    error,
    callApi: getWebhookHistory,
  } = useWebhooksApi();

  // Hook riêng cho action resend
  const {
    data: resendData,
    error: resendError,
    callApi: resendWebhook,
  } = useWebhooksApi();

  // Gọi API khi component mount
  useEffect(() => {
    const userId = localStorage.getItem("user_id");
    if (userId) {
      getWebhookHistory({ action: "history" });
    }
  }, [getWebhookHistory]);

  // Xử lý response từ API
  useEffect(() => {
    if (apiData && apiData.data && Array.isArray(apiData.data)) {
      setWebhookData(apiData.data);
    }
  }, [apiData]);

  // Xử lý response từ API resend
  useEffect(() => {
    if (resendData) {
      if (resendData.status) {
        setNotification({
          show: true,
          message: "Gửi lại webhook thành công!",
          type: "success",
        });
        // Tải lại lịch sử để cập nhật
        getWebhookHistory({ action: "history" });
      } else {
        setNotification({
          show: true,
          message: `Gửi lại thất bại: ${
            resendData.message || "Lỗi không xác định"
          }`,
          type: "error",
        });
      }
    }
    if (resendError) {
      setNotification({
        show: true,
        message: `Lỗi khi gửi lại webhook: ${resendError}`,
        type: "error",
      });
    }
    // Dù thành công hay thất bại, cũng reset trạng thái loading
    if (resendData || resendError) {
      setResendingId(null);
    }
  }, [resendData, resendError, getWebhookHistory]);

  // Filter và search logic
  const filteredData = webhookData.filter((item) => {
    const lowerCaseSearchTerm = searchTerm.toLowerCase();
    const matchesSearch =
      !searchTerm ||
      item.id?.toString().toLowerCase().includes(lowerCaseSearchTerm) ||
      item.webhook_id?.toString().toLowerCase().includes(lowerCaseSearchTerm) ||
      item.history_id?.toString().toLowerCase().includes(lowerCaseSearchTerm) ||
      item.endpoint?.toLowerCase().includes(lowerCaseSearchTerm) ||
      item.description?.toLowerCase().includes(lowerCaseSearchTerm);

    const matchesWebhook =
      !webhookFilter || item.webhook_id?.toString() === webhookFilter;
    const matchesDate =
      !dateFilter || // Nếu không có filter ngày thì bỏ qua
      (item.call_time && // Nếu có filter, so sánh ngày
        new Date(item.call_time).toDateString() === dateFilter.toDateString());

    const matchesEvent = !eventFilter || item.type === eventFilter;

    return matchesSearch && matchesWebhook && matchesDate && matchesEvent;
  });

  // Pagination
  const totalPages = Math.ceil(filteredData.length / itemsPerPage);
  const startIdx = (currentPage - 1) * itemsPerPage;
  const paginatedData = filteredData.slice(startIdx, startIdx + itemsPerPage);

  // Get unique values for filters
  const uniqueWebhookIds = [
    ...new Set(webhookData.map((item) => item.webhook_id)),
  ].filter(Boolean);
  const uniqueEvents = [
    ...new Set(webhookData.map((item) => item.type)),
  ].filter(Boolean);

  // Format currency
  const formatCurrency = (amount) => {
    if (!amount) return "0 ₫";
    return new Intl.NumberFormat("vi-VN").format(amount) + " ₫";
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return "-";
    // Chỉ hiển thị ngày/tháng/năm (dd/MM/yyyy) cho nhất quán
    return new Date(dateString).toLocaleDateString("vi-VN", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };

  const getStatusBadge = (statusCode) => {
    if (statusCode >= 200 && statusCode < 300) {
      return "bg-success";
    } else if (statusCode >= 400) {
      return "bg-danger";
    } else {
      return "bg-warning";
    }
  };

  // Mapping cho sự kiện
  const getEventText = (type) => {
    const eventMap = {
      send: "Gửi",
      receive: "Nhận",
      both: "Gửi và nhận",
      IN: "Nhận",
      OUT: "Gửi",
      ALL: "Gửi và Nhận",
    };
    return eventMap[type] || type || "Nhận";
  };

  // Hàm xử lý hiển thị modal
  const handleShowRequest = (item) => {
    setModalTitle(`Request - ID: ${item.id}`);
    setModalType("request");

    // Format payload nếu là string JSON
    let formattedPayload;
    try {
      if (typeof item.payload === "string") {
        formattedPayload = JSON.stringify(JSON.parse(item.payload), null, 2);
      } else {
        formattedPayload = JSON.stringify(item.payload || {}, null, 2);
      }
    } catch (e) {
      formattedPayload = item.payload || "Không có dữ liệu request";
    }

    setModalContent(formattedPayload);
    setShowModal(true);
  };

  const handleShowResponse = (item) => {
    setModalTitle(`Response - ID: ${item.id}`);
    setModalType("response");

    // Format response nếu là string JSON
    let formattedResponse;
    try {
      if (typeof item.response === "string") {
        formattedResponse = JSON.stringify(JSON.parse(item.response), null, 2);
      } else {
        formattedResponse = JSON.stringify(item.response || {}, null, 2);
      }
    } catch (e) {
      formattedResponse = item.response || "Không có dữ liệu response";
    }

    setModalContent(formattedResponse);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setModalTitle("");
    setModalContent("");
    setModalType("");
  };

  // Hàm export Excel
  const handleExportExcel = () => {
    if (filteredData.length === 0) {
      setNotification({
        show: true,
        message: "Không có dữ liệu để xuất.",
        type: "warning",
      });
      return;
    }

    // Chuyển đổi dữ liệu thành format Excel
    const excelData = filteredData.map((item, index) => ({
      STT: index + 1,
      ID: item.id,
      "Hook ID": item.webhook_id || "-",
      "Trans ID": item.history_id || "-",
      Endpoint: item.endpoint || "-",
      "Status Code": item.status_code || "-",
      "Call Time": formatDate(item.call_time),
      "Sự kiện": getEventText(item.type),
      "Mô tả": item.description || "-",
      "Số tiền": item.amount
        ? new Intl.NumberFormat("vi-VN").format(item.amount) + " ₫"
        : "0 ₫",
    }));

    // Tạo workbook và worksheet
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.json_to_sheet(excelData);

    // Thêm worksheet vào workbook
    XLSX.utils.book_append_sheet(wb, ws, "Webhook History");

    // Tạo tên file với ngày giờ hiện tại
    const today = new Date();
    const day = String(today.getDate()).padStart(2, "0");
    const month = String(today.getMonth() + 1).padStart(2, "0");
    const year = today.getFullYear();
    const fileName = `Webhook_History_${day}-${month}-${year}.xlsx`;

    // Download file
    XLSX.writeFile(wb, fileName);
  };

  // Hàm print
  const handlePrint = () => {
    const printContent = `
      <html>
        <head>
          <title>Lịch sử Webhook</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; font-weight: bold; }
            .text-center { text-align: center; }
            .header { text-align: center; margin-bottom: 20px; }
            .badge-success { background-color: #28a745; color: white; padding: 2px 6px; border-radius: 3px; }
            .badge-danger { background-color: #dc3545; color: white; padding: 2px 6px; border-radius: 3px; }
            .badge-warning { background-color: #ffc107; color: black; padding: 2px 6px; border-radius: 3px; }
          </style>
        </head>
        <body>
          <div class="header">
            <h2>Lịch sử Webhook</h2>
            <p>Ngày xuất: ${new Date().toLocaleString("vi-VN")}</p>
            <p>Tổng số webhook: ${filteredData.length}</p>
          </div>
          <table>
            <thead>
              <tr>
                <th>STT</th>
                <th>ID</th>
                <th>Hook ID</th>
                <th>Trans ID</th>
                <th>Endpoint</th>
                <th>Status Code</th>
                <th>Call Time</th>
                <th>Sự kiện</th>
                <th>Mô tả</th>
                <th>Số tiền</th>
              </tr>
            </thead>
            <tbody>
              ${filteredData
                .map(
                  (item, index) => `
                <tr>
                  <td class="text-center">${index + 1}</td>
                  <td>${item.id}</td>
                  <td>${item.webhook_id || "-"}</td>
                  <td>${item.history_id || "-"}</td>
                  <td>${item.endpoint || "-"}</td>
                  <td class="text-center">
                    <span class="badge-${getStatusBadge(
                      item.status_code
                    ).replace("bg-", "")}">${item.status_code || "-"}</span>
                  </td>
                  <td>${formatDate(item.call_time)}</td>
                  <td class="text-center">${getEventText(item.type)}</td>
                  <td>${item.description || "-"}</td>
                  <td class="text-center">${formatCurrency(item.amount)}</td>
                </tr>
              `
                )
                .join("")}
            </tbody>
          </table>
        </body>
      </html>
    `;

    const printWindow = window.open("", "_blank");
    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
    printWindow.close();
  };

  // Hàm xử lý gửi lại webhook
  const handleResend = (item) => {
    const userId = localStorage.getItem("user_id");
    const transactionId = item.id;

    if (!userId || !transactionId) {
      setNotification({
        show: true,
        message: "Thiếu thông tin User ID hoặc Transaction ID để gửi lại.",
        type: "error",
      });
      return;
    }

    setResendingId(item.id); // Bắt đầu loading cho dòng này
    resendWebhook({
      action: "resend",
      user_id: userId,
      transaction_id: transactionId,
    });
  };

  return (
    <>
      {/* MasterLayout */}
      <MasterLayout>
        {/* Breadcrumb */}
        <Breadcrumb title="Lịch sử Webhook" />

        <div className="container-fluid mt-4">
          {/* Filter Card */}
          <div className="card">
            <div className="card-body">
              <div className="row g-3 align-items-center">
                <div className="col-md-2">
                  <select
                    className="form-select"
                    value={webhookFilter}
                    onChange={(e) => setWebhookFilter(e.target.value)}
                  >
                    <option value="">Tất cả Webhook ID</option>
                    {uniqueWebhookIds.map((id) => (
                      <option key={id} value={id}>
                        {id}
                      </option>
                    ))}
                  </select>
                </div>
                <div className="col-md-2">
                  <DatePicker
                    selected={dateFilter}
                    onChange={(date) => setDateFilter(date)}
                    className="form-control"
                    placeholderText="Lọc theo ngày gọi"
                    dateFormat="dd/MM/yyyy"
                    isClearable
                    autoComplete="off"
                  />
                </div>
                <div className="col-md-2">
                  <select
                    className="form-select"
                    value={eventFilter}
                    onChange={(e) => setEventFilter(e.target.value)}
                  >
                    <option value="">Tất cả Sự kiện</option>
                    {uniqueEvents.map((event) => (
                      <option key={event} value={event}>
                        {getEventText(event)}
                      </option>
                    ))}
                  </select>
                </div>
                <div className="col-md-3">
                  <input
                    type="search"
                    className="form-control"
                    placeholder="Tìm kiếm ID, Trans ID, endpoint, mô tả..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <div className="col-md-3">
                  <button
                    className="btn btn-primary w-100"
                    onClick={() => {
                      setSearchTerm("");
                      setWebhookFilter("");
                      setDateFilter(null);
                      setEventFilter("");
                      setCurrentPage(1);
                    }}
                  >
                    Chọn lại
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Table Section */}
          <div className="card mt-3 printable-area">
            <div className="card-body">
              <div className="d-flex justify-content-between align-items-center mb-3">
                <div className="d-flex align-items-center gap-2"></div>
                <div className="d-flex gap-2">
                  <button
                    className="btn btn-success btn-sm"
                    onClick={handleExportExcel}
                  >
                    <Icon className="me-1" icon="mdi:file-excel" /> Excel
                  </button>
                  <button
                    className="btn btn-success btn-sm"
                    onClick={handlePrint}
                  >
                    <Icon className="me-1" icon="mdi:printer" /> Print
                  </button>
                </div>
              </div>

              {loading ? (
                <div className="text-center p-5">
                  <div className="spinner-border text-primary" role="status">
                    <span className="visually-hidden">Đang tải...</span>
                  </div>
                </div>
              ) : error ? (
                <div className="alert alert-danger">{error}</div>
              ) : (
                <div className="table-responsive">
                  <table className="table basic-border-table mb-0">
                    <thead className="table-light">
                      <tr>
                        <th style={{ width: "80px" }}>ID</th>
                        <th style={{ width: "70px" }}>Hook ID</th>
                        <th style={{ width: "70px" }}>Trans ID</th>
                        <th style={{ width: "260px" }}>Endpoint</th>
                        <th style={{ width: "100px" }} className="text-center">
                          Status Code
                        </th>
                        <th style={{ width: "80px" }} className="text-center">
                          Request
                        </th>
                        <th style={{ width: "80px" }} className="text-center">
                          Response
                        </th>
                        <th style={{ width: "70px" }}>Call time</th>
                        <th style={{ width: "100px" }} className="text-center">
                          Sự kiện
                        </th>
                        <th style={{ width: "230px" }}>Mô tả</th>
                        <th style={{ width: "120px" }} className="text-center">
                          Số tiền
                        </th>
                        <th style={{ width: "100px" }} className="text-center">
                          Gửi lại
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {paginatedData.length > 0 ? (
                        paginatedData.map((item) => (
                          <tr key={item.id}>
                            <td>{item.id}</td>
                            <td>{item.webhook_id || "-"}</td>
                            <td>{item.history_id || "-"}</td>
                            <td
                              className="text-break"
                              style={{ maxWidth: "200px" }}
                            >
                              {item.endpoint || "-"}
                            </td>
                            <td className="text-center">
                              <span
                                className={`badge ${getStatusBadge(
                                  item.status_code
                                )}`}
                              >
                                {item.status_code || "-"}
                              </span>
                            </td>
                            <td className="text-center">
                              <button
                                className="btn btn-success"
                                title="Xem Request"
                                onClick={() => handleShowRequest(item)}
                              >
                                <Icon icon="mdi:eye" />
                              </button>
                            </td>
                            <td className="text-center">
                              <button
                                className="btn btn-success"
                                title="Xem Response"
                                onClick={() => handleShowResponse(item)}
                              >
                                <Icon icon="mdi:eye" />
                              </button>
                            </td>
                            <td>{formatDate(item.call_time)}</td>
                            <td className="text-center">
                              <span className="badge text-bg-success">
                                {getEventText(item.type)}
                              </span>
                            </td>
                            <td
                              className="text-break"
                              style={{ maxWidth: "150px" }}
                            >
                              {item.description || "-"}
                            </td>
                            <td className="text-center">
                              {formatCurrency(item.amount)}
                            </td>
                            <td className="text-center">
                              <button
                                className="btn btn-sm btn-success"
                                title="Gửi lại"
                                onClick={() => handleResend(item)}
                                disabled={resendingId === item.id}
                              >
                                {resendingId === item.id ? (
                                  <span className="spinner-border spinner-border-sm"></span>
                                ) : (
                                  <Icon icon="mdi:send" />
                                )}
                              </button>
                            </td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td colSpan={12} className="text-center p-4">
                            Không có webhook nào.
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              )}

              {/* Pagination */}
              {!loading &&
                !error &&
                Math.ceil(filteredData.length / itemsPerPage) > 1 && (
                  <div className="d-flex justify-content-between align-items-center mt-3">
                    <span className="text-muted">
                      Hiển thị {paginatedData.length} trên tổng số{" "}
                      {filteredData.length} webhook
                    </span>
                    <nav aria-label="pagination">
                      <ul className="pagination mb-0">
                        <li
                          className={`page-item ${
                            currentPage === 1 ? "disabled" : ""
                          }`}
                        >
                          <button
                            className="page-link"
                            onClick={() =>
                              setCurrentPage((prev) => Math.max(prev - 1, 1))
                            }
                          >
                            &laquo;
                          </button>
                        </li>
                        {(() => {
                          const totalPages = Math.ceil(
                            filteredData.length / itemsPerPage
                          );
                          const pageNumbers = [];
                          const maxPagesToShow = 5;

                          if (totalPages <= maxPagesToShow + 2) {
                            for (let i = 1; i <= totalPages; i++)
                              pageNumbers.push(i);
                          } else {
                            pageNumbers.push(1);
                            if (currentPage > 3) pageNumbers.push("...");
                            let start = Math.max(2, currentPage - 1);
                            let end = Math.min(totalPages - 1, currentPage + 1);
                            if (currentPage <= 2) end = 3;
                            if (currentPage >= totalPages - 1)
                              start = totalPages - 2;
                            for (let i = start; i <= end; i++)
                              pageNumbers.push(i);
                            if (currentPage < totalPages - 2)
                              pageNumbers.push("...");
                            pageNumbers.push(totalPages);
                          }

                          return pageNumbers.map((num, index) =>
                            num === "..." ? (
                              <li
                                key={`ellipsis-${index}`}
                                className="page-item disabled"
                              >
                                <span className="page-link">...</span>
                              </li>
                            ) : (
                              <li
                                key={num}
                                className={`page-item ${
                                  currentPage === num ? "active" : ""
                                }`}
                              >
                                <button
                                  className="page-link"
                                  onClick={() => setCurrentPage(num)}
                                >
                                  {num}
                                </button>
                              </li>
                            )
                          );
                        })()}
                        <li
                          className={`page-item ${
                            currentPage ===
                            Math.ceil(filteredData.length / itemsPerPage)
                              ? "disabled"
                              : ""
                          }`}
                        >
                          <button
                            className="page-link"
                            onClick={() =>
                              setCurrentPage((prev) =>
                                Math.min(
                                  prev + 1,
                                  Math.ceil(filteredData.length / itemsPerPage)
                                )
                              )
                            }
                          >
                            &raquo;
                          </button>
                        </li>
                      </ul>
                    </nav>
                  </div>
                )}
            </div>
          </div>
        </div>

        {/* Modal hiển thị Request/Response */}
        {showModal && (
          <div
            className="modal fade show"
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              backgroundColor: "rgba(0,0,0,0.5)",
              position: "fixed",
              top: 0,
              left: 0,
              width: "100%",
              height: "100vh",
              zIndex: 1050,
              margin: 0,
              padding: "1rem",
            }}
            tabIndex="-1"
            onClick={handleCloseModal}
          >
            <div
              className="modal-dialog modal-lg"
              onClick={(e) => e.stopPropagation()}
              style={{
                margin: 0,
                maxHeight: "90vh",
              }}
            >
              <div className="modal-content">
                <div className="modal-header py-2">
                  <p className="modal-title d-flex align-items-center gap-2 mb-0 p-3">
                    <Icon
                      icon={
                        modalType === "request" ? "mdi:upload" : "mdi:download"
                      }
                      className={
                        modalType === "request" ? "text-info" : "text-success"
                      }
                      style={{ fontSize: "1rem" }}
                    />
                    {modalTitle}
                  </p>
                  <button
                    type="button"
                    className="btn-close btn-close-sm"
                    onClick={handleCloseModal}
                    aria-label="Close"
                  ></button>
                </div>
                <div className="modal-body">
                  <div className="mb-2">
                    <small className="text-muted">
                      {modalType === "request"
                        ? "Payload được gửi đi:"
                        : "Response nhận được:"}
                    </small>
                  </div>
                  <pre
                    className="bg-light p-3 rounded border"
                    style={{
                      maxHeight: "400px",
                      overflow: "auto",
                      fontSize: "0.875rem",
                      whiteSpace: "pre-wrap",
                      wordBreak: "break-word",
                    }}
                  >
                    <code>{modalContent}</code>
                  </pre>
                </div>
                <div className="modal-footer">
                  <button
                    type="button"
                    className="btn btn-success btn-sm"
                    onClick={handleCloseModal}
                  >
                    Đóng
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
        <NotificationModal
          show={notification.show}
          message={notification.message}
          type={notification.type}
          onClose={() =>
            setNotification({ show: false, message: "", type: "" })
          }
        />
      </MasterLayout>
    </>
  );
};

export default WebhookHistory;
