import React, { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import LoadingLink from "../components/LoadingLink";
import LoadingSkeleton from "./LoadingSkeleton";
import blogApi from "../callapi/BlogApiNew";
import logo from "../assets/landing/img/logo.png";
const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const location = useLocation();

  // Static menu items
  const staticMenuItems = [
    { name: "Trang chủ", path: "/", type: "internal" },
    { name: "Blog", path: "/blog", type: "internal" },
  ];

  // Check token for login/dashboard button
  const token =
    typeof window !== "undefined" ? localStorage.getItem("token") : null;

  const rightMenuItems = [
    { name: "<PERSON><PERSON>ng gi<PERSON>", path: "/bang-gia", type: "internal" },
    { name: "<PERSON><PERSON><PERSON> hệ", path: "/lien-he", type: "internal" },
    token
      ? { name: "<PERSON><PERSON><PERSON> Dashboard", path: "/client/dashboard", type: "button" }
      : { name: "Đăng nhập", path: "/client/login", type: "button" },
  ];

  // Fetch categories from WordPress API with caching
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);
        const categoriesData = await blogApi.getCategories();
        // Filter out uncategorized and limit to main categories
        const filteredCategories = categoriesData
          .filter((cat) => cat.name !== "Uncategorized" && cat.count > 0)
          .slice(0, 5) // Limit to 5 categories
          .map((cat) => ({
            name: cat.name,
            path: `/blog/${cat.slug}`,
            type: "category",
            id: cat.id,
            count: cat.count,
          }));
        setCategories(filteredCategories);
      } catch (error) {
        console.error("Error fetching categories:", error);
        setCategories([]);
      } finally {
        setLoading(false);
      }
    };

    // Add small delay to allow preload to complete
    const timer = setTimeout(fetchCategories, 50);
    return () => clearTimeout(timer);
  }, []);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  // Check if current path is active
  const isActiveLink = (path) => {
    if (path === "/blog" && location.pathname.startsWith("/blog")) {
      return true;
    }
    return location.pathname === path;
  };

  // Prefetch data on hover for better UX
  const handleCategoryHover = (categoryId) => {
    if (categoryId) {
      blogApi.prefetchCategory(categoryId);
    }
  };

  // Combine all menu items
  const allMenuItems = [...staticMenuItems, ...categories];

  return (
    <header className="sticky-top bg-white shadow-sm">
      <nav className="navbar navbar-expand-lg navbar-light">
        <div className="container">
          <LoadingLink
            to="/"
            className="navbar-brand d-flex align-items-center"
          >
            <img
              src={logo}
              alt="Pay2S Logo"
              width="120px"
              height="auto"
              className="me-2"
              onError={(e) => {
                e.target.style.display = "none";
                e.target.nextSibling.style.display = "inline";
              }}
            />
            <span className="fw-bold text-success d-none">Pay2S</span>
          </LoadingLink>

          <button
            className="navbar-toggler"
            type="button"
            onClick={toggleMenu}
            aria-controls="navbarNav"
            aria-expanded={isMenuOpen}
            aria-label="Toggle navigation"
          >
            <span className="navbar-toggler-icon"></span>
          </button>

          <div
            className={`collapse navbar-collapse ${isMenuOpen ? "show" : ""}`}
            id="navbarNav"
          >
            <ul className="navbar-nav me-auto">
              {/* Main menu items */}
              {allMenuItems.map((item, index) => (
                <li key={index} className="nav-item">
                  <LoadingLink
                    to={item.path}
                    className={`nav-link ${
                      isActiveLink(item.path) ? "active" : ""
                    }`}
                    onClick={() => setIsMenuOpen(false)}
                    onMouseEnter={() => handleCategoryHover(item.id)}
                  >
                    {item.name}
                  </LoadingLink>
                </li>
              ))}

              {/* Loading indicator for categories */}
              {loading && (
                <li className="nav-item">
                  <LoadingSkeleton type="categories" count={3} />
                </li>
              )}
            </ul>

            <ul className="navbar-nav">
              {/* Right menu items */}
              {rightMenuItems.map((item, index) => (
                <li key={`right-${index}`} className="nav-item">
                  <LoadingLink
                    to={item.path}
                    className={`nav-link ${
                      item.type === "button"
                        ? "btn btn-success text-white px-3"
                        : ""
                    } ${isActiveLink(item.path) ? "active" : ""}`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {item.name}
                  </LoadingLink>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </nav>
    </header>
  );
};

export default Header;
