// Blog API - Direct call to WordPress REST API using Axios with Caching
import axios from "axios";

const BLOG_API_BASE = "https://blog.pay2s.vn/wp-json/wp/v2";

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: BLOG_API_BASE,
  timeout: 5000, // Reduced to 5 seconds
  headers: {
    Accept: "application/json",
    "Content-Type": "application/json",
  },
});

// Cache configuration
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
const cache = new Map();

// Cache helper functions
const getCacheKey = (endpoint, params) => {
  const sortedParams = Object.keys(params || {})
    .sort()
    .reduce((result, key) => {
      result[key] = params[key];
      return result;
    }, {});
  return `${endpoint}?${JSON.stringify(sortedParams)}`;
};

const isValidCache = (cacheEntry) => {
  return cacheEntry && Date.now() - cacheEntry.timestamp < CACHE_DURATION;
};

const setCache = (key, data) => {
  cache.set(key, {
    data,
    timestamp: Date.now(),
  });
};

const getCache = (key) => {
  const cacheEntry = cache.get(key);
  return isValidCache(cacheEntry) ? cacheEntry.data : null;
};

class BlogApi {
  constructor() {
    this.client = apiClient;
    this.preloadData();
  }

  // Clear cache
  clearCache() {
    cache.clear();
  }

  // Get cache statistics
  getCacheStats() {
    const stats = {
      size: cache.size,
      keys: Array.from(cache.keys()),
      hitRate: 0, // Could be implemented with counters
    };
    return stats;
  }

  // Preload essential data
  async preloadData() {
    try {
      // Preload categories in background
      setTimeout(() => {
        this.getCategories().catch(() => {
          // Silent fail for preload
        });
      }, 100);

      // Preload first page of posts in background
      setTimeout(() => {
        this.getPosts(1, 10).catch(() => {
          // Silent fail for preload
        });
      }, 200);
    } catch (error) {
      // Silent fail for preload
    }
  }

  // Prefetch data for better UX (call on hover)
  async prefetchCategory(categoryId) {
    try {
      this.getPostsByCategory(categoryId, null, 10).catch(() => {
        // Silent fail for prefetch
      });
    } catch (error) {
      // Silent fail for prefetch
    }
  }

  // Prefetch posts page
  async prefetchPage(page) {
    try {
      this.getPosts(page, 10).catch(() => {
        // Silent fail for prefetch
      });
    } catch (error) {
      // Silent fail for prefetch
    }
  }

  // Make API request with axios and caching
  async makeRequest(endpoint, params = {}) {
    const cacheKey = getCacheKey(endpoint, params);

    // Check cache first
    const cachedData = getCache(cacheKey);
    if (cachedData) {
      return cachedData;
    }

    try {
      const response = await this.client.get(endpoint, {
        params: params,
      });

      const result = {
        data: response.data,
        headers: response.headers,
        status: response.status,
      };

      // Cache the result
      setCache(cacheKey, result);

      return result;
    } catch (error) {
      console.error("API request error:", error);
      throw error;
    }
  }

  // Get posts with pagination
  async getPosts(page = 1, perPage = 10) {
    const params = {
      page,
      per_page: perPage,
      _embed: true,
      orderby: "date",
      order: "desc",
    };

    try {
      const { data: posts, headers } = await this.makeRequest("/posts", params);

      // Get total pages from headers
      const totalPages = parseInt(headers["x-wp-totalpages"]) || 1;
      const total = parseInt(headers["x-wp-total"]) || posts.length;

      return {
        posts,
        totalPages,
        total,
        currentPage: page,
      };
    } catch (error) {
      console.error("Error fetching posts:", error);
      throw new Error("Không thể tải bài viết. Vui lòng thử lại sau.");
    }
  }

  // Get single post by slug
  async getPostBySlug(slug) {
    const params = {
      slug,
      _embed: true,
    };

    try {
      const { data: posts } = await this.makeRequest("/posts", params);

      if (!posts || posts.length === 0) {
        throw new Error("Post not found");
      }

      return posts[0];
    } catch (error) {
      console.error("Error fetching post:", error);
      throw new Error("Không tìm thấy bài viết.");
    }
  }

  // Get posts by category
  async getPostsByCategory(categoryId, excludeId = null, limit = 3) {
    const params = {
      categories: categoryId,
      per_page: limit,
      _embed: true,
      orderby: "date",
      order: "desc",
    };

    if (excludeId) {
      params.exclude = excludeId;
    }

    try {
      const { data: posts } = await this.makeRequest("/posts", params);
      return posts || [];
    } catch (error) {
      console.error("Error fetching related posts:", error);
      return [];
    }
  }

  // Get posts by category with pagination
  async getPostsByCategoryPaginated(categoryId, page = 1, perPage = 10) {
    const params = {
      categories: categoryId,
      page,
      per_page: perPage,
      _embed: true,
      orderby: "date",
      order: "desc",
    };

    try {
      const { data: posts, headers } = await this.makeRequest("/posts", params);

      const totalPages = parseInt(headers["x-wp-totalpages"]) || 1;
      const total = parseInt(headers["x-wp-total"]) || posts.length;

      return {
        posts,
        totalPages,
        total,
        currentPage: page,
      };
    } catch (error) {
      console.error("Error fetching posts by category:", error);
      throw new Error("Không thể tải bài viết theo danh mục.");
    }
  }

  // Get categories
  async getCategories() {
    try {
      const { data: categories } = await this.makeRequest("/categories", {
        per_page: 100,
      });
      return categories || [];
    } catch (error) {
      console.error("Error fetching categories:", error);
      return [];
    }
  }

  // Search posts
  async searchPosts(query, page = 1, perPage = 10) {
    const params = {
      search: query,
      page,
      per_page: perPage,
      _embed: true,
      orderby: "relevance",
      order: "desc",
    };

    try {
      const { data: posts, headers } = await this.makeRequest("/posts", params);

      const totalPages = parseInt(headers["x-wp-totalpages"]) || 1;
      const total = parseInt(headers["x-wp-total"]) || posts.length;

      return {
        posts,
        totalPages,
        total,
        currentPage: page,
        query,
      };
    } catch (error) {
      console.error("Error searching posts:", error);
      throw new Error("Không thể tìm kiếm bài viết. Vui lòng thử lại sau.");
    }
  }

  // Get posts by author
  async getPostsByAuthor(authorId, page = 1, perPage = 10) {
    const params = {
      author: authorId,
      page,
      per_page: perPage,
      _embed: true,
      orderby: "date",
      order: "desc",
    };

    try {
      const { data: posts, headers } = await this.makeRequest("/posts", params);

      const totalPages = parseInt(headers["x-wp-totalpages"]) || 1;
      const total = parseInt(headers["x-wp-total"]) || posts.length;

      return {
        posts,
        totalPages,
        total,
        currentPage: page,
      };
    } catch (error) {
      console.error("Error fetching posts by author:", error);
      throw new Error("Không thể tải bài viết theo tác giả.");
    }
  }

  // Get posts by tag
  async getPostsByTag(tagId, page = 1, perPage = 10) {
    const params = {
      tags: tagId,
      page,
      per_page: perPage,
      _embed: true,
      orderby: "date",
      order: "desc",
    };

    try {
      const { data: posts, headers } = await this.makeRequest("/posts", params);

      const totalPages = parseInt(headers["x-wp-totalpages"]) || 1;
      const total = parseInt(headers["x-wp-total"]) || posts.length;

      return {
        posts,
        totalPages,
        total,
        currentPage: page,
      };
    } catch (error) {
      console.error("Error fetching posts by tag:", error);
      throw new Error("Không thể tải bài viết theo tag.");
    }
  }

  // Get tags
  async getTags() {
    try {
      const { data: tags } = await this.makeRequest("/tags", {
        per_page: 100,
      });
      return tags || [];
    } catch (error) {
      console.error("Error fetching tags:", error);
      return [];
    }
  }

  // Get authors/users
  async getAuthors() {
    try {
      const { data: authors } = await this.makeRequest("/users", {
        per_page: 100,
      });
      return authors || [];
    } catch (error) {
      console.error("Error fetching authors:", error);
      return [];
    }
  }

  // Get single category by slug
  async getCategoryBySlug(slug) {
    const params = {
      slug,
    };

    try {
      const { data: categories } = await this.makeRequest(
        "/categories",
        params
      );

      if (!categories || categories.length === 0) {
        throw new Error("Category not found");
      }

      return categories[0];
    } catch (error) {
      console.error("Error fetching category:", error);
      throw new Error("Không tìm thấy danh mục.");
    }
  }

  // Get single tag by slug
  async getTagBySlug(slug) {
    const params = {
      slug,
    };

    try {
      const { data: tags } = await this.makeRequest("/tags", params);

      if (!tags || tags.length === 0) {
        throw new Error("Tag not found");
      }

      return tags[0];
    } catch (error) {
      console.error("Error fetching tag:", error);
      throw new Error("Không tìm thấy tag.");
    }
  }

  // Get single author by slug
  async getAuthorBySlug(slug) {
    const params = {
      slug,
    };

    try {
      const { data: authors } = await this.makeRequest("/users", params);

      if (!authors || authors.length === 0) {
        throw new Error("Author not found");
      }

      return authors[0];
    } catch (error) {
      console.error("Error fetching author:", error);
      throw new Error("Không tìm thấy tác giả.");
    }
  }
}

// Create singleton instance
const blogApi = new BlogApi();

export default blogApi;
