import React, { useState, useEffect, useRef } from "react";
import LoadingLink from "../components/LoadingLink";
import logo from "../assets/landing/img/logo.png";
import "../assets/css/landing.css";
import "../assets/css/fontawesome.min.css";
const menu = [
  { label: "Trang chủ", href: "/" },
  {
    label: "Sản Phẩm",
    href: "",
    children: [
      { label: "API Open Banking", href: "/open-api-banking" },
      { label: "Cổng thanh toán tự động", href: "/cong-thanh-toan" },
      { label: "Chia sẻ biến động số dư", href: "/chia-se-bien-dong-so-du" },
    ],
  },
  { label: "Bảng giá", href: "/bang-gia" },
  { label: "Tin tức", href: "/blog" },
  { label: "Liên hệ", href: "/lien-he" },
  {
    label: "<PERSON><PERSON><PERSON> nguyên",
    href: "",
    children: [
      { label: "Trung tâm trợ giúp", href: "https://docs.pay2s.vn/" },
      { label: "API Status", href: "https://pay2s.vn/status" },
    ],
  },
];

const Header = () => {
  const [mobileOpen, setMobileOpen] = useState(false);
  const [isSticky, setIsSticky] = useState(false);
  const [openSubMenus, setOpenSubMenus] = useState([]); // lưu trạng thái submenu mobile
  const menuAreaRef = useRef(null);

  useEffect(() => {
    const handleScroll = () => {
      setIsSticky(window.scrollY > 50);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  useEffect(() => {
    if (!mobileOpen) return;
    const handleClickOutside = (event) => {
      if (menuAreaRef.current && !menuAreaRef.current.contains(event.target)) {
        setMobileOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [mobileOpen]);

  const handleToggle = () => setMobileOpen((v) => !v);

  // Toggle submenu cho mobile
  const handleSubMenuToggle = (idx) => {
    setOpenSubMenus((prev) =>
      prev.includes(idx) ? prev.filter((i) => i !== idx) : [...prev, idx]
    );
  };

  // Handle smooth scroll for anchor links
  const handleAnchorClick = (e, href) => {
    if (href && href.startsWith("#")) {
      e.preventDefault();
      const targetId = href.substring(1);
      const targetElement = document.getElementById(targetId);
      if (targetElement) {
        targetElement.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
        // Update URL without page reload
        window.history.pushState(null, null, href);
      }
      // Close mobile menu if open
      if (mobileOpen) {
        setMobileOpen(false);
      }
    }
  };

  const renderMenu = (isMobile = false) => (
    <ul>
      {menu.map((item, idx) =>
        item.children ? (
          <li
            className={`menu-item-has-children${
              isMobile && openSubMenus.includes(idx) ? " th-active" : ""
            }`}
            key={item.label}
          >
            <a
              href={item.href}
              onClick={
                isMobile
                  ? (e) => {
                      e.preventDefault();
                      handleSubMenuToggle(idx);
                    }
                  : undefined
              }
              style={{ position: "relative" }}
            >
              {item.label}
              {/* Dấu cộng/trừ bên phải */}
              {isMobile && (
                <span className="th-mean-expand">
                  {openSubMenus.includes(idx) ? "−" : "+"}
                </span>
              )}
            </a>
            <ul
              className={`sub-menu${
                isMobile && openSubMenus.includes(idx) ? " th-open" : ""
              }`}
              style={
                isMobile
                  ? { display: openSubMenus.includes(idx) ? "block" : "none" }
                  : {}
              }
            >
              {item.children.map((sub, subIdx) => (
                <li key={sub.label}>
                  <LoadingLink
                    to={sub.href}
                    onClick={(e) => handleAnchorClick(e, sub.href)}
                  >
                    {sub.label}
                  </LoadingLink>
                </li>
              ))}
            </ul>
          </li>
        ) : (
          <li className="menu-item" key={item.label}>
            <LoadingLink
              to={item.href}
              onClick={(e) => handleAnchorClick(e, item.href)}
            >
              {item.label}
            </LoadingLink>
          </li>
        )
      )}
    </ul>
  );

  // Kiểm tra token trong localStorage
  const token =
    typeof window !== "undefined" ? localStorage.getItem("token") : null;

  return (
    <>
      {/* Mobile Menu */}
      <div
        className={`th-menu-wrapper${mobileOpen ? " th-body-visible" : ""}`}
        style={{ zIndex: 99999 }}
      >
        <div className="th-menu-area text-center" ref={menuAreaRef}>
          <button className="th-menu-toggle" onClick={handleToggle}>
            <i className="fal fa-times"></i>
          </button>
          <div className="mobile-logo">
            <LoadingLink to="/">
              <img src={logo} alt="pay2s" className="pay2s-logo-mobile" />
            </LoadingLink>
          </div>
          <div className="th-mobile-menu">{renderMenu(true)}</div>
          <div className="mobile-header-button">
            {token ? (
              <LoadingLink
                to="/client/dashboard"
                className="th-btn style-radius"
                onClick={() => setMobileOpen(false)}
              >
                Vào Dashboard
              </LoadingLink>
            ) : (
              <LoadingLink
                to="/client/login"
                className="th-btn style-radius"
                onClick={() => setMobileOpen(false)}
              >
                Đăng nhập
              </LoadingLink>
            )}
          </div>
        </div>
      </div>
      {/* Header Area */}
      <header className="th-header header-layout6">
        <div className={`sticky-wrapper${isSticky ? " sticky" : ""}`}>
          <div className="menu-area">
            <div className="container th-container4">
              <div className="row align-items-center justify-content-between">
                <div className="col-auto">
                  <div className="header-logo">
                    <LoadingLink className="icon-masking" to="/">
                      <img src={logo} alt="pay2s" className="pay2s-logo" />
                    </LoadingLink>
                  </div>
                </div>
                <div className="col-auto">
                  <nav className="main-menu style2 d-none d-lg-inline-block">
                    {renderMenu(false)}
                  </nav>
                  <button
                    type="button"
                    className="th-menu-toggle d-block d-lg-none"
                    onClick={handleToggle}
                  >
                    <i className="far fa-bars"></i>
                  </button>
                </div>
                <div className="col-auto d-xl-block d-none">
                  <div className="header-button">
                    {token ? (
                      <LoadingLink
                        to="/client/dashboard"
                        className="th-btn style-radius"
                      >
                        Vào Dashboard
                      </LoadingLink>
                    ) : (
                      <LoadingLink
                        to="/client/login"
                        className="th-btn style-radius"
                      >
                        Đăng nhập
                      </LoadingLink>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>
    </>
  );
};

export default Header;
