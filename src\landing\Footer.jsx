import React from "react";

import logo from "../assets/landing/img/logo.png";
import bank1 from "../assets/landing/img/bank/ft_1.png";
import bank2 from "../assets/landing/img/bank/ft_2.png";
import bank3 from "../assets/landing/img/bank/ft_3.png";
import bank5 from "../assets/landing/img/bank/ft_5.png";
import bank6 from "../assets/landing/img/bank/ft_6.png";
import bank7 from "../assets/landing/img/bank/ft_7.png";
import bank4 from "../assets/landing/img/bank/ft_4.png";

const bankImages = [bank1, bank2, bank3, bank5, bank6, bank7, bank4];

const productMenu = [
  { href: "/open-api-banking", label: "Open Banking" },
  { href: "/cong-thanh-toan", label: "API Thanh toán tự động" },
  { href: "/chia-se-bien-dong-so-du", label: "Chia sẽ biến động số dư" },
  { href: "/cong-thanh-toan#sec-title", label: "Cổng thanh toán WHMCS" },
  { href: "/cong-thanh-toan#sec-title", label: "Cổng thanh toán Hostbill" },
];
const companyMenu = [
  { href: "/bang-gia", label: "Bảng giá" },
  { href: "https://pay2s.vn/tin-tuc", label: "Tin tức" },
  { href: "https://docs.pay2s.vn/", label: "Tài liệu" },
];
const infoMenu = [
  { href: "/open-api-banking#team-sec", label: "Công bố hợp tác" },
  { href: "/chinh-sach-bao-mat", label: "Chính sách bảo mật" },
  { href: "/thoa-thuan", label: "Thỏa thuận sử dụng dịch vụ" },
  { href: "/tiep-nhan-xu-ly", label: "Tiếp nhận & Xử lý khiếu nại" },
];
import bg from "../assets/landing/img/bg/footer_bg_6.jpg"; // If you want to use background image

const Footer = ({ hasNegativeMargin }) => {
  // Nếu có prop hasNegativeMargin thì mới set paddingTop
  const footerStyle = {
    backgroundImage: `url(${bg})`,
    backgroundSize: "cover",
    ...(hasNegativeMargin ? { paddingTop: "195px" } : {}),
  };
  return (
    <footer className="footer-wrapper footer-layout6" style={footerStyle}>
      <div className="container th-container4">
        <div className="widget-area">
          <div className="row justify-content-between">
            <div className="col-md-6 col-xl-4">
              <div className="widget footer-widget">
                <div className="header-logo">
                  <a className="icon-masking" href="/">
                    <img src={logo} alt="pay2s" className="pay2s-logo" />
                  </a>
                </div>
                <h3 className="widget_title mt-2"></h3>
                <div className="info-widget">
                  <strong>CÔNG TY CỔ PHẦN FUTE</strong>
                  <div className="footer-text small">
                    <i className="fa-solid fa-house"></i> 15/40/30 Đường số 59,
                    Phường 14, Q.Gò Vấp, TP. HCM <br />
                    <i className="fa-solid fa-phone"></i> 028 627 05478
                    <br />
                    <i className="fa-solid fa-envelope"></i> <EMAIL>
                    <br />
                    <i className="fa-brands fa-square-facebook"></i>{" "}
                    facebook.com/pay2s
                    <br />
                    <i className="fa-solid fa-folder-open"></i> MST: **********
                    <br />
                  </div>
                </div>
              </div>
            </div>
            <div className="col-md-6 col-xl-auto">
              <div className="widget footer-widget">
                <h3 className="widget_title">Ngân hàng đối tác</h3>
                <div className="banking-coop">
                  {/* First row: 3 banks */}
                  <div className="row mt-3 mb-3">
                    {bankImages.slice(0, 3).map((img, idx) => (
                      <div className="col-4" key={idx}>
                        <img src={img} alt={`Bank ${idx + 1}`} />
                      </div>
                    ))}
                  </div>
                  {/* Second row: 3 banks */}
                  <div className="row mt-3 mb-3">
                    {bankImages.slice(3, 6).map((img, idx) => (
                      <div className="col-4" key={idx}>
                        <img src={img} alt={`Bank ${idx + 4}`} />
                      </div>
                    ))}
                  </div>
                  {/* Third row: center bank */}
                  {/* <div className="row mt-3 mb-3">
                    <div className="col-4"></div>
                    <div className="col-4">
                      <img src={bankImages[6]} alt="Bank 7" />
                    </div>
                    <div className="col-4"></div>
                  </div> */}
                </div>
              </div>
            </div>
            <div className="col-md-6 col-xl-auto">
              <div className="widget widget_nav_menu footer-widget">
                <h3 className="widget_title">Sản phẩm</h3>
                <div className="menu-all-pages-container">
                  <ul className="menu">
                    {productMenu.map((item, idx) => (
                      <li key={idx}>
                        <a href={item.href}>{item.label}</a>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
            <div className="col-md-6 col-xl-auto">
              <div className="widget widget_nav_menu footer-widget">
                <h3 className="widget_title">Công ty</h3>
                <div className="menu-all-pages-container">
                  <ul className="menu">
                    {companyMenu.map((item, idx) => (
                      <li key={idx}>
                        <a href={item.href}>{item.label}</a>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
            <div className="col-md-6 col-xl-auto">
              <div className="widget widget_nav_menu footer-widget">
                <h3 className="widget_title">Thông tin</h3>
                <div className="menu-all-pages-container">
                  <ul className="menu">
                    {infoMenu.map((item, idx) => (
                      <li key={idx}>
                        <a href={item.href}>{item.label}</a>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="container th-container4">
        <div className="copyright-wrap">
          <div className="row justify-content-between align-items-center">
            <div className="col-lg-6">
              <p className="copyright-text">
                Copyright <i className="fal fa-copyright"> </i>{" "}
                <a href="https://pay2s.vn"> FUTE JSC </a> là chủ sở hữu và có
                toàn quyền tác giả phần mềm Pay2S .
              </p>
            </div>
            <div className="col-lg-6 text-lg-end text-center">
              <div className="th-social style4">
                <a href="https://www.facebook.com/">
                  <i className="fab fa-facebook-f"></i>
                </a>
                <a href="https://www.twitter.com/">
                  <i className="fab fa-twitter"></i>
                </a>
                <a href="https://www.linkedin.com/">
                  <i className="fab fa-linkedin-in"></i>
                </a>
                <a href="https://www.whatsapp.com/">
                  <i className="fab fa-whatsapp"></i>
                </a>
                <a href="https://www.youtube.com/">
                  <i className="fab fa-youtube"></i>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
