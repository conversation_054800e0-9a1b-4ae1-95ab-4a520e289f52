import React from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import mbLogo from "../../assets/landing/img/bank/MB.svg";
import acbLogo from "../../assets/landing/img/bank/ACB.svg";
import techcombankLogo from "../../assets/landing/img/bank/Techcombank.svg";
import vietcombankLogo from "../../assets/landing/img/bank/Vietcombank.svg";
import vietinbankLogo from "../../assets/landing/img/bank/vietinbank.svg";
import bidvLogo from "../../assets/landing/img/bank/BIDV.svg";
import tpbankLogo from "../../assets/landing/img/bank/TP.svg";
import momoLogo from "../../assets/landing/img/bank/Momo.svg";

const bankLogos = [
  { src: mbLogo, alt: "MB" },
  { src: acb<PERSON><PERSON>, alt: "ACB" },
  { src: techcombankLogo, alt: "Techcombank" },
  { src: vietcombankLogo, alt: "Vietcombank" },
  { src: vietinbankLogo, alt: "Vietinbank" },
  { src: bidvLogo, alt: "BIDV" },
  { src: tpbankLogo, alt: "TPBank" },
  { src: momoLogo, alt: "Momo" },
];

const OpenApiBankSlider = () => (
  <div
    className="brand-sec1"
    data-pos-for="#process-sec"
    data-sec-pos="top-half"
  >
    <div className="container py-5">
      <div className="slider-area text-center">
        <Swiper
          modules={[Navigation]}
          navigation={{
            nextEl: ".slider-next",
            prevEl: ".slider-prev",
          }}
          spaceBetween={20}
          breakpoints={{
            0: { slidesPerView: 2 },
            576: { slidesPerView: 2 },
            768: { slidesPerView: 3 },
            992: { slidesPerView: 3 },
            1200: { slidesPerView: 4 },
            1400: { slidesPerView: 5 },
          }}
          className="th-slider"
        >
          {bankLogos.map((logo, idx) => (
            <SwiperSlide key={idx}>
              <div className="brand-box py-20">
                <img src={logo.src} alt={logo.alt + " Logo"} />
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
        <button
          className="slider-arrow style3 slider-prev"
          tabIndex={0}
          aria-label="Previous"
        >
          <i className="far fa-arrow-left"></i>
        </button>
        <button
          className="slider-arrow style3 slider-next"
          tabIndex={0}
          aria-label="Next"
        >
          <i className="far fa-arrow-right"></i>
        </button>
      </div>
    </div>
  </div>
);

export default OpenApiBankSlider;
