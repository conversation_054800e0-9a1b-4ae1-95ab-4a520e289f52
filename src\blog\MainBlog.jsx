import React, { useState, useEffect } from "react";
import { Helmet } from "react-helmet-async";
import LoadingLink from "../components/LoadingLink";
import usePageLoading from "../hooks/usePageLoading";
import LoadingSkeleton from "./LoadingSkeleton";
import blogCache from "../utils/blogCache";
import blogApi from "../callapi/BlogApiNew";
import "./blog.css";

const MainBlog = () => {
  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [skeletonReady, setSkeletonReady] = useState(false);

  // Complete loading khi data đã load xong
  usePageLoading(loading);

  // Fetch initial posts
  const fetchPosts = async () => {
    try {
      setError(null);

      // Check cache first
      const cacheKey = blogCache.generateKey("posts", { page: 1, limit: 6 });
      const cachedData = blogCache.get(cacheKey);

      if (cachedData) {
        // Use cached data immediately but keep loading for a moment
        setPosts(cachedData.posts);
        setTotalPages(cachedData.totalPages);
        setCurrentPage(1);
        setHasMore(cachedData.totalPages > 1);

        // Small delay to show loading bar briefly even with cache
        setTimeout(() => {
          setLoading(false);
        }, 300);
        return;
      }

      // No cache, show loading
      setLoading(true);

      const result = await blogApi.getPosts(1, 6);

      // Cache the result
      blogCache.set(cacheKey, result);

      setPosts(result.posts);
      setTotalPages(result.totalPages);
      setCurrentPage(1);
      setHasMore(result.totalPages > 1);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Load more posts
  const loadMorePosts = async () => {
    if (loadingMore || !hasMore) return;

    try {
      setLoadingMore(true);
      setError(null);

      const nextPage = currentPage + 1;
      const result = await blogApi.getPosts(nextPage, 6);

      setPosts((prevPosts) => [...prevPosts, ...result.posts]);
      setCurrentPage(nextPage);
      setHasMore(nextPage < result.totalPages);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoadingMore(false);
    }
  };

  useEffect(() => {
    fetchPosts();
  }, []);

  // Format date
  const formatDate = (dateString) => {
    const options = {
      year: "numeric",
      month: "long",
      day: "numeric",
      timeZone: "Asia/Ho_Chi_Minh",
    };
    return new Date(dateString).toLocaleDateString("vi-VN", options);
  };

  // Extract excerpt from content
  const getExcerpt = (content, maxLength = 150) => {
    const text = content.replace(/<[^>]*>/g, "");
    return text.length > maxLength
      ? text.substring(0, maxLength) + "..."
      : text;
  };

  // Get featured image
  const getFeaturedImage = (post) => {
    if (
      post._embedded &&
      post._embedded["wp:featuredmedia"] &&
      post._embedded["wp:featuredmedia"][0]
    ) {
      return post._embedded["wp:featuredmedia"][0].source_url;
    }
    return "/src/assets/images/blog/blog1.png"; // Default image
  };

  if (loading) {
    // Set skeleton ready after a brief delay to ensure loading bar shows
    if (!skeletonReady) {
      setTimeout(() => {
        setSkeletonReady(true);
      }, 500); // Loading bar chạy 500ms trước khi skeleton xuất hiện
    }

    return (
      <>
        <Helmet>
          <title>Blog - Pay2S</title>
          <meta
            name="description"
            content="Khám phá các bài viết mới nhất về công nghệ thanh toán và Open Banking"
          />
        </Helmet>

        <div className="container py-5">
          <div className="row">
            <div className="col-12">
              <h1 className="text-center mb-5 text-success">Blog Pay2S</h1>
              <p className="text-center text-muted mb-5">
                Khám phá các bài viết mới nhất về công nghệ thanh toán và Open
                Banking
              </p>
            </div>
          </div>

          {/* Skeleton loaders - chỉ hiển thị sau khi loading bar đã chạy */}
          {skeletonReady && (
            <div className="row">
              {[...Array(6)].map((_, index) => (
                <div key={index} className="col-lg-4 col-md-6 mb-4">
                  <LoadingSkeleton type="post" />
                </div>
              ))}
            </div>
          )}
        </div>
      </>
    );
  }

  if (error) {
    return (
      <main className="blog-main">
        <div className="container">
          <div className="error-message">
            <h2>Có lỗi xảy ra</h2>
            <p>{error}</p>
            <button onClick={() => fetchPosts()} className="retry-btn">
              Thử lại
            </button>
          </div>
        </div>
      </main>
    );
  }

  return (
    <>
      <Helmet>
        <title>Blog Pay2S - Tin tức và kiến thức thanh toán điện tử</title>
        <meta
          name="description"
          content="Cập nhật tin tức mới nhất về thanh toán điện tử, fintech và các giải pháp ngân hàng số từ Pay2S"
        />
        <meta
          name="keywords"
          content="pay2s, blog, thanh toán điện tử, fintech, ngân hàng số, payment gateway"
        />
        <meta
          property="og:title"
          content="Blog Pay2S - Tin tức và kiến thức thanh toán điện tử"
        />
        <meta
          property="og:description"
          content="Cập nhật tin tức mới nhất về thanh toán điện tử, fintech và các giải pháp ngân hàng số từ Pay2S"
        />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://pay2s.vn/blog" />
        <link rel="canonical" href="https://pay2s.vn/blog" />
      </Helmet>

      <main className="blog-main">
        <div className="container">
          <div className="blog-header">
            <h1>Blog Pay2S</h1>
          </div>

          <div className="blog-grid">
            {posts.map((post) => (
              <article key={post.id} className="blog-card">
                <div className="blog-card-image">
                  <img
                    src={getFeaturedImage(post)}
                    alt={post.title.rendered}
                    onError={(e) => {
                      e.target.src = "/src/assets/images/blog/blog1.png";
                    }}
                  />
                </div>

                <div className="blog-card-content">
                  <div className="blog-meta">
                    <span className="blog-date">{formatDate(post.date)}</span>
                    {post._embedded &&
                      post._embedded["wp:term"] &&
                      post._embedded["wp:term"][0] && (
                        <span className="blog-category">
                          {post._embedded["wp:term"][0][0]?.name}
                        </span>
                      )}
                  </div>

                  <h2 className="blog-title">
                    <LoadingLink
                      to={`/blog/${post.slug}`}
                      dangerouslySetInnerHTML={{ __html: post.title.rendered }}
                    />
                  </h2>

                  <div className="blog-excerpt">
                    {getExcerpt(post.excerpt.rendered)}
                  </div>

                  <LoadingLink
                    to={`/blog/${post.slug}`}
                    className="read-more-btn"
                  >
                    Đọc thêm →
                  </LoadingLink>
                </div>
              </article>
            ))}
          </div>

          {/* Load More Button */}
          {hasMore && (
            <div className="load-more-section">
              <button
                onClick={loadMorePosts}
                disabled={loadingMore}
                className="load-more-btn"
              >
                {loadingMore ? (
                  <>
                    <i className="fas fa-spinner fa-spin me-2"></i>
                    Đang tải...
                  </>
                ) : (
                  <>
                    Xem thêm bài viết
                    <i className="fas fa-chevron-down ms-2"></i>
                  </>
                )}
              </button>

              {loadingMore && (
                <div className="loading-more-posts">
                  <LoadingSkeleton type="posts" count={6} />
                </div>
              )}
            </div>
          )}
        </div>
      </main>
    </>
  );
};

export default MainBlog;
