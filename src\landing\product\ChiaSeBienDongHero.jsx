import React from "react";
import heroImg from "../../assets/landing/img/hero/hero_img_1_1.png";
import heroShape1 from "../../assets/landing/img/hero/hero_shape_1_1.svg";
import heroShape2 from "../../assets/landing/img/hero/hero_shape_1_2.svg";
import heroShape3 from "../../assets/landing/img/hero/hero_shape_1_3.svg";

const ChiaSeBienDongHero = () => {
  return (
    <>
      <h1 style={{ display: "none" }}>
        Giải pháp thông báo thông báo chia sẻ biến động số dư
      </h1>
      <div className="th-hero-wrapper hero-1" id="hero">
        <div className="hero-img tilt-active">
          <img src={heroImg} alt="Hero Image" />
        </div>
        <div className="container">
          <div className="hero-style1">
            <span className="hero-subtitle">Webhook linh hoạt</span>
            <h1 className="hero-title">ĐỐI SOÁT TÀI CHÍNH</h1>
            <h1 className="hero-title">
              <span className="text-theme fw-medium"> LINH HOẠT</span>
            </h1>
            <p className="hero-text">
              Bạn đang mất hàng giờ theo dõi số dư, đối chiếu thanh toán thủ
              công? Nhân viên không biết khách đã chuyển tiền hay chưa?
              <br />
              Doanh nghiệp của bạn cần tự động hóa quy trình thu ngân, trích
              lương, báo cáo tài chính?
            </p>
            <div className="btn-group d-flex flex-nowrap gap-2">
              <a href="#anchor-title" className="th-btn">
                Tìm hiểu thêm <i className="fa-regular fa-arrow-right ms-2"></i>
              </a>
              <a href="/client/signup" className="th-btn">
                Tài liệu tích hợp{" "}
                <i className="fa-regular fa-arrow-right ms-2"></i>
              </a>
            </div>
          </div>
        </div>
        <div className="hero-shape1">
          <img src={heroShape1} alt="shape" />
        </div>
        <div className="hero-shape2">
          <img src={heroShape2} alt="shape" />
        </div>
        <div className="hero-shape3">
          <img src={heroShape3} alt="shape" />
        </div>
      </div>
    </>
  );
};

export default ChiaSeBienDongHero;
