import axios from "axios";
import { API_BASE_URL } from "./apiConfig";

const getAuthToken = () => localStorage.getItem("token");

/**
 * Hàm gọi API chung cho endpoint "/order".
 * @param {object} data - Đ<PERSON>i tượng chứa action và các tham số cần thiết.
 * Ví dụ: { action: 'get_user_invoices', user_id: '123' }
 * @returns {Promise<any>} Promise chứa dữ liệu phản hồi từ API.
 */
export const callOrderApi = (data) => {
  const token = getAuthToken();
  return axios.post(
    `${API_BASE_URL}/order`,
    new URLSearchParams(data), // Truyền thẳng đối tượng data vào
    {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/x-www-form-urlencoded",
      },
    }
  );
};

export const callPlanApi = (data) => {
  const token = getAuthToken();
  return axios.post(
    `${API_BASE_URL}/plan`,
    new URLSearchParams(data), // Truyền thẳng đối tượng data vào
    {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/x-www-form-urlencoded",
      },
    }
  );
};

/**
 * Tạo link thanh toán cho một hóa đơn (giữ nguyên vì là endpoint khác).
 * @param {string} invoiceNumber Mã hóa đơn (invoicenum).
 * @param {number|string} invoiceId ID của hóa đơn.
 * @returns {Promise<any>} Promise chứa dữ liệu phản hồi từ API, bao gồm payUrl.
 */
export const createPaymentGateway = (invoiceNumber, invoiceId) => {
  const token = getAuthToken();
  return axios.post(
    `${API_BASE_URL}/gateway/create`,
    new URLSearchParams({
      action: "pay_invoice",
      invoice_number: invoiceNumber,
      invoice_id: invoiceId,
    }),
    {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/x-www-form-urlencoded",
      },
    }
  );
};

/**
 * Hàm gọi API chung cho endpoint "/user".
 * @param {object} data - Đối tượng chứa action và các tham số cần thiết.
 * @returns {Promise<any>} Promise chứa dữ liệu phản hồi từ API.
 */
export const callUserApi = (data) => {
  const token = getAuthToken();
  return axios.post(`${API_BASE_URL}/user`, new URLSearchParams(data), {
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/x-www-form-urlencoded",
    },
  });
};
