import React, { useEffect, useState } from "react";
import { Icon } from "@iconify/react/dist/iconify.js";
import useBankApi from "../../callapi/Bank.jsx";
import useGetInfoBIDVApi from "../../callapi/GetInfoBIDV.jsx";

const AddVAModal = ({ isOpen, onClose, accountNumber, bankShortName }) => {
  const [merchantId, setMerchantId] = useState("");
  const [autoFilledData, setAutoFilledData] = useState(null);
  const [showOtpInput, setShowOtpInput] = useState(false);
  const [otp, setOtp] = useState("");
  const [notification, setNotification] = useState({
    error: null,
    success: null,
  });
  const [lastProcessedResponse, setLastProcessedResponse] = useState(null);

  // Hooks
  const {
    data: bidvData,
    loading: bidvLoading,
    callApi: getBidvInfo,
  } = useGetInfoBIDVApi();
  const {
    data: apiResponse,
    loading: isLoading,
    error: apiError,
    callApi,
  } = useBankApi();

  // Auto-fill thông tin khi user nhập merchantId (debounced)
  useEffect(() => {
    if (!merchantId.trim() || merchantId.trim().length < 1) {
      setAutoFilledData(null);
      return;
    }

    const timeoutId = setTimeout(async () => {
      try {
        const userId = localStorage.getItem("user_id");

        setNotification({ error: null, success: "Đang tải thông tin..." });

        // Gọi API GetInfoBIDV với accountNumber chính để lấy thông tin
        const response = await getBidvInfo({
          accNo: accountNumber, // Sử dụng tài khoản chính
          user_id: userId,
          bankShortName: bankShortName,
        });

        if (response && response.success && response.data) {
          setAutoFilledData(response.data);
          setNotification({
            error: null,
            // success: "Đã tự động điền thông tin thành công",
          });
        } else {
          setAutoFilledData(null);
          setNotification({
            error: `Không thể lấy thông tin từ tài khoản chính ${accountNumber}`,
            success: null,
          });
        }
      } catch (err) {
        setAutoFilledData(null);
        setNotification({
          error: "Lỗi khi tự động điền thông tin: " + err.message,
          success: null,
        });
      }
    }, 1000); // Debounce 1 giây

    return () => clearTimeout(timeoutId);
  }, [merchantId, getBidvInfo, bankShortName, accountNumber]);

  // Xử lý response từ API thêm VA
  useEffect(() => {
    if (apiResponse && apiResponse !== lastProcessedResponse) {
      if (apiError) {
        setNotification({ error: apiError, success: null });
        return;
      }

      if (apiResponse && apiResponse.status === true) {
        setLastProcessedResponse(apiResponse);

        // Trường hợp cần OTP
        if (apiResponse.OTP === 1 && !showOtpInput) {
          setShowOtpInput(true);
          setNotification({
            success: "Gửi OTP thành công. Vui lòng nhập mã OTP để hoàn tất.",
            error: null,
          });
          return;
        }

        // Trường hợp thành công
        if (apiResponse.OTP !== 1 || (apiResponse.OTP === 1 && showOtpInput)) {
          const finalMessage = apiResponse.message || "Thêm VA thành công!";
          setNotification({
            success: `${finalMessage} Đang đóng modal...`,
            error: null,
          });

          setTimeout(() => {
            onClose();
            resetForm();
            window.location.reload();
          }, 2000);
        }
      } else if (apiResponse && apiResponse.status === false) {
        setNotification({
          error: apiResponse.message || "Có lỗi xảy ra khi thêm VA",
          success: null,
        });
        setLastProcessedResponse(apiResponse);
      }
    }
  }, [apiResponse, apiError, showOtpInput, lastProcessedResponse, onClose]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setNotification({ error: null, success: null });

    if (!merchantId.trim()) {
      setNotification({
        error: "Vui lòng nhập Merchant ID",
        success: null,
      });
      return;
    }

    if (!autoFilledData) {
      setNotification({
        error: "Không thể lấy thông tin tài khoản. Vui lòng thử lại.",
        success: null,
      });
      return;
    }

    const userId = localStorage.getItem("user_id");
    if (!userId) {
      setNotification({
        error: "Lỗi xác thực: Không tìm thấy User ID. Vui lòng đăng nhập lại.",
        success: null,
      });
      return;
    }

    let apiBody;
    if (showOtpInput) {
      // Giai đoạn 2: Xác nhận OTP
      apiBody = {
        accountNumber,
        accEmail: autoFilledData.accEmail,
        merchantId,
        shortName: bankShortName,
        type: "openapi",
        action: "confirm_otp",
        user_id: userId,
        otp,
      };
    } else {
      // Giai đoạn 1: Thêm VA với thông tin auto-fill
      apiBody = {
        accName: autoFilledData.accName,
        accountNumber,
        accEmail: autoFilledData.accEmail,
        merchantId,
        shortName: bankShortName,
        action: "add",
        user_id: userId,
        cccd: autoFilledData.cccd,
        accMobile: autoFilledData.accMobile,
        type: "openapi",
      };
    }

    await callApi(apiBody);
  };

  const resetForm = () => {
    setMerchantId("");
    setAutoFilledData(null);
    setShowOtpInput(false);
    setOtp("");
    setNotification({ error: null, success: null });
    setLastProcessedResponse(null);
  };

  // Thêm useEffect để xử lý success và trigger refresh parent
  useEffect(() => {
    if (
      apiResponse &&
      apiResponse.status === true &&
      apiResponse !== lastProcessedResponse
    ) {
      if (apiResponse.OTP === 1 && !showOtpInput) {
        setShowOtpInput(true);
        setNotification({
          success: "Gửi OTP thành công. Vui lòng nhập mã OTP để hoàn tất.",
          error: null,
        });
      } else {
        setNotification({
          success: "Thêm VA thành công! Đang đóng...",
          error: null,
        });

        setTimeout(() => {
          onClose();
          resetForm();
          // Trigger refresh parent component bằng cách reload trang
          window.location.reload();
        }, 1500);
      }
      setLastProcessedResponse(apiResponse);
    }
  }, [apiResponse, showOtpInput, lastProcessedResponse, onClose]);

  if (!isOpen) return null;

  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(0, 0, 0, 0.6)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        zIndex: 1050,
      }}
      onClick={onClose}
    >
      <div
        style={{
          background: "white",
          borderRadius: "8px",
          width: "90%",
          maxWidth: "500px",
          maxHeight: "90vh",
          overflow: "auto",
          boxShadow: "0 5px 15px rgba(0,0,0,.5)",
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <div style={{ padding: "1.5rem", borderBottom: "1px solid #dee2e6" }}>
          <div className="d-flex justify-content-between align-items-center">
            <h5 className="fw-bold mb-0">
              <Icon icon="ph:plus-circle" className="me-2 text-success" />
              Thêm tài khoản ảo (VA)
            </h5>
            <button
              type="button"
              className="btn-close"
              onClick={() => {
                onClose();
                resetForm();
              }}
              disabled={isLoading}
            ></button>
          </div>
        </div>

        <form onSubmit={handleSubmit} style={{ padding: "1.5rem" }}>
          {/* Thông báo */}
          {notification.error && (
            <div className="alert alert-danger">{notification.error}</div>
          )}
          {notification.success && (
            <div className="alert alert-success">{notification.success}</div>
          )}

          {/* Loading thông tin tài khoản */}
          {bidvLoading && (
            <div className="alert alert-info">
              <Icon icon="ph:spinner" className="me-2" />
              Đang tải thông tin tài khoản...
            </div>
          )}

          {/* Thông báo hướng dẫn */}
          {merchantId.trim().length > 18 && (
            <div className="alert alert-warning">
              <Icon icon="ph:warning" className="me-2" />
              Merchant ID không được vượt quá 18 ký tự.
            </div>
          )}

          {/* Merchant ID input */}
          <div className="mb-3">
            <label className="form-label">Tạo số tài khoản ảo *</label>
            <div className="input-group">
              <span className="input-group-text">963869</span>
              <input
                className="form-control"
                type="text"
                value={merchantId}
                onChange={(e) => {
                  const value = e.target.value.toUpperCase();
                  // Giới hạn tối đa 18 ký tự
                  if (value.length <= 18) {
                    setMerchantId(value);
                  }
                }}
                required
                disabled={isLoading}
                placeholder="Nhập 1-18 ký tự (ví dụ: VYNT)"
              />
            </div>
            <div className="form-text">
              Tài khoản ảo sẽ là: <strong>963869{merchantId}</strong>
              <br />
              <small className="text-muted">
                Độ dài: {merchantId.length}/18 ký tự
              </small>
            </div>
          </div>

          {/* OTP Input */}
          {showOtpInput && (
            <div className="mb-3">
              <label className="form-label fw-bold text-danger">Mã OTP *</label>
              <input
                className="form-control"
                type="text"
                value={otp}
                onChange={(e) => setOtp(e.target.value)}
                required
                placeholder="Nhập mã OTP đã được gửi đến bạn"
                autoFocus
              />
            </div>
          )}

          {/* Buttons */}
          <div className="d-flex justify-content-end gap-2">
            <button
              type="button"
              className="btn btn-light border"
              onClick={() => {
                onClose();
                resetForm();
              }}
              disabled={isLoading}
            >
              Hủy
            </button>
            <button
              type="submit"
              className="btn btn-success"
              disabled={
                isLoading ||
                (!autoFilledData && !bidvLoading) ||
                merchantId.trim().length < 1 ||
                merchantId.trim().length > 18
              }
            >
              {isLoading ? (
                <>
                  <span
                    className="spinner-border spinner-border-sm me-2"
                    role="status"
                  ></span>
                  Đang xử lý...
                </>
              ) : showOtpInput ? (
                <>
                  <Icon icon="ph:check" className="me-1" />
                  Xác nhận OTP
                </>
              ) : (
                <>
                  <Icon icon="ph:plus" className="me-1" />
                  Thêm VA
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddVAModal;
