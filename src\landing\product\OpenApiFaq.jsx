import React from "react";
import apiFaqImg from "../../assets/landing/img/normal/api-faq.svg";
import pViconImg from "../../assets/landing/img/normal/p-vicon.png";
const faqList = [
  {
    question: "Pay2S sẽ giúp bạn những gì?",
    answer: (
      <>
        <p>
          <strong>1. Thanh Toán QR Code Tự Động</strong>
        </p>
        <p>
          Tạo QR thanh toán ngay trong app của bạn, không phụ thuộc vào ví điện
          tử. Tỷ lệ thành công 99.9%, xử lý dưới 3 giây.
        </p>
        <p>
          <strong>2. Webhooks Đối Soát Real-time</strong>
        </p>
        <p>
          Nhận thông báo tự động khi khách hàng chuyển tiền. Đồng bộ trạng thái
          thanh toán vào hệ thống nội bộ.
        </p>
        <p>
          <strong>3. <PERSON><PERSON><PERSON><PERSON> Tự Động</strong>
        </p>
        <p>
          G<PERSON>i lệnh chi lương hàng loạt qua API. Hỗ trợ webhook xác nhận thành
          công từ ngân hàng.
        </p>
      </>
    ),
    expanded: false,
  },
  {
    question: "API Pay2S có an toàn không?",
    answer: (
      <>
        <p>Giải pháp Open Banking dành riêng cho doanh nghiệp Việt</p>
        <p>
          Pay2S là đối tác chính thức được cấp API trực tiếp các ngân hàng lớn
        </p>
        <p>
          ✅ Kết nối trực tiếp 7 ngân hàng lớn (ACB, Techcombank, Vietcombank,
          MB, TPBank, BIDV, ViettinBank)
        </p>
        <p>✅ Hỗ trợ đa nền tảng: Web, iOS, Android, POS</p>
        <p>✅ Tài liệu rõ ràng + SDK hỗ trợ 10+ ngôn ngữ lập trình</p>
      </>
    ),
    expanded: true,
  },
  {
    question: "API ngân hàng và ví điện tử Pay2S có giới hạn giao dịch không?",
    answer: (
      <p className="faq-text">
        API ngân hàng và ví điện tử Pay2S không giới hạn số lần giao dịch và
        cũng không giới hạn cổng thanh toán, khách hàng chỉ cần đăng ký gói phù
        hợp với số lượng tài khoản cần thêm vào là có thể sử dụng.
      </p>
    ),
    expanded: false,
  },
];

const OpenApiFaq = () => (
  <div className="overflow-hidden space" id="faq-sec">
    <div className="container">
      <div className="row align-items-center justify-content-center">
        <div className="col-xl-6 col-lg-9">
          <div className="title-area text-center text-xl-start">
            <span className="sub-title">PAY2S FAQ'S</span>
            <h2 className="sec-title">Bạn có thể thắc mắc</h2>
          </div>
          <div className="accordion-area accordion" id="faqAccordion">
            {faqList.map((faq, idx) => (
              <div
                className={`accordion-card style3${
                  faq.expanded ? " active" : ""
                }`}
                key={idx}
              >
                <div
                  className="accordion-header"
                  id={`collapse-item-${idx + 1}`}
                >
                  <button
                    className={`accordion-button${
                      faq.expanded ? "" : " collapsed"
                    }`}
                    type="button"
                    data-bs-toggle="collapse"
                    data-bs-target={`#collapse-${idx + 1}`}
                    aria-expanded={faq.expanded}
                    aria-controls={`collapse-${idx + 1}`}
                  >
                    {faq.question}
                  </button>
                </div>
                <div
                  id={`collapse-${idx + 1}`}
                  className={`accordion-collapse collapse${
                    faq.expanded ? " show" : ""
                  }`}
                  aria-labelledby={`collapse-item-${idx + 1}`}
                  data-bs-parent="#faqAccordion"
                >
                  <div className="accordion-body">{faq.answer}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
        <div className="col-xl-6 mt-35 mt-xl-0">
          <div className="img-box8">
            <div className="img1">
              <img src={apiFaqImg} alt="Faq" />
            </div>
            <div className="shape2">
              <img src={pViconImg} alt="About" />
            </div>
            <div className="color-animate"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
);

export default OpenApiFaq;
