import React from "react";
import processLine from "../../assets/landing/img/bg/process_line_3s.svg";
import processBox1 from "../../assets/landing/img/icon/process_box_3_1.svg";
import processBox2 from "../../assets/landing/img/icon/process_box_3_2.svg";
import processBox3 from "../../assets/landing/img/icon/process_box_3_3.svg";

const steps = [
  {
    img: processBox1,
    number: 1,
    title: "Nhận thanh toán & THÔNG BÁO tức thời",
    text: "Pay2S bot ngay lập tức gửi thông tin thanh toán thành công đến cho Nhà bán hàng qua Group chat trên các nền tảng: Telegram, Zalo, SMS, Lark, …",
  },
  {
    img: processBox2,
    number: 2,
    title: "Tự động hóa Thanh toán chuyển khoản",
    text: "Pay2S kết nối trực tiếp đến Ngân hàng để cung cấp mã QR giao dịch theo yêu cầu và cung cấp thông báo tức thời qua API trạng thái thanh toán của giao dịch.",
  },
  {
    img: processBox3,
    number: 3,
    title: "Theo dõi và Quản lý dòng tiền",
    text: "Thống kê dòng tiền theo yêu cầu, đối soát dòng tiền chuyển khoản từ các POS, booth bán hàng. Giải pháp tự động hóa tiền lương (C&B)",
  },
];

const Process = () => (
  <section className="process-area-3 overflow-hidden space">
    <div className="container th-container4">
      <div className="title-area text-xl-start text-center">
        <span className="sub-title">
          Khách hàng của bạn có thể thanh toán một cách nhanh nhất.
        </span>
        <h2 className="sec-title">
          Nâng tầm trải nghiệm <br />
          thanh toán với Pay2S
        </h2>
        <p className="sec-text mt-25">
          Thực hiện quá nhiều bước trong quy trình thanh toán là nguyên nhân
          khiến khách hàng từ bỏ đơn hàng. 3 tính năng từ Pay2S giúp đơn giản
          hóa trải nghiệm mua hàng.
        </p>
        <a href="/client/signup" className="th-btn style-radius">
          Đăng ký ngay
        </a>
      </div>
      <div className="process-card-area3">
        <div className="process-line position-top">
          <img src={processLine} className="svg-3s" alt="line" />
        </div>
        <div className="row gy-40 justify-content-xl-between justify-content-center">
          {steps.map((step, idx) => (
            <div className="col-md-6 col-xl-auto process-card-wrap" key={idx}>
              <div className="process-card style3">
                <div className="process-card_icon">
                  <img src={step.img} alt="icon" />
                </div>
                <div className="process-card_number">{step.number}</div>
                <h2 className="box-title">{step.title}</h2>
                <p className="process-card_text">{step.text}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  </section>
);

export default Process;
