import React from "react";
import techShape from "../../assets/landing/img/shape/tech_shape_1.png";
import square from "../../assets/landing/img/shape/square_1.png";

const LienHeContact = () => {
  const contactInfo = [
    {
      icon: "fas fa-phone",
      title: "Hotline",
      description: "Hỗ trợ trực tiếp qua điện thoại",
      link: "tel:02862705478",
      linkText: "028 627 05478"
    },
    {
      icon: "fas fa-envelope",
      title: "Email",
      description: "<PERSON><PERSON><PERSON> hệ email",
      link: "mailto:<EMAIL>",
      linkText: "<EMAIL>"
    },
    {
      icon: "fa-solid fa-file",
      title: "Hỗ trợ & Báo lỗi",
      description: "Xem hướng dẫn và báo lỗi",
      link: "https://docs.pay2s.vn/introduction/ho-tro-bao-loi.html",
      linkText: "Docs.pay2s.vn"
    }
  ];

  return (
    <section className="space">
      <div className="container">
        <div className="row gy-4 justify-content-center">
          <div className="space">
            <div className="container">
              <div className="row gy-4">
                {contactInfo.map((contact, index) => (
                  <div className="col-xl-4 col-md-6" key={index}>
                    <div className="contact-info">
                      <div className="contact-box bg-white">
                        <div className="contact-icon mb-3">
                          <i className={contact.icon}></i>
                        </div>
                        <h5 className="fw-bold">{contact.title}</h5>
                        <p className="text-muted">{contact.description}</p>
                        <a href={contact.link} className="contact-link">
                          {contact.linkText}
                        </a>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="shape-mockup" data-top="0" data-right="0">
        <img src={techShape} alt="shape" />
      </div>
      <div className="shape-mockup" data-top="0%" data-left="0%">
        <img src={square} alt="shape" />
      </div>
    </section>
  );
};

export default LienHeContact;