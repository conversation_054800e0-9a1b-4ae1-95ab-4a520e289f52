import React from "react";
import "./skeleton.css";

const LoadingSkeleton = ({ type = "posts", count = 6 }) => {
  if (type === "post") {
    return (
      <div className="blog-card skeleton-card">
        <div className="skeleton-image"></div>
        <div className="blog-card-content">
          <div className="skeleton-meta">
            <div className="skeleton-date"></div>
            <div className="skeleton-category"></div>
          </div>
          <div className="skeleton-title"></div>
          <div className="skeleton-title-short"></div>
          <div className="skeleton-excerpt">
            <div className="skeleton-line"></div>
            <div className="skeleton-line"></div>
            <div className="skeleton-line-short"></div>
          </div>
          <div className="skeleton-button"></div>
        </div>
      </div>
    );
  }

  if (type === "post-detail") {
    return (
      <div className="skeleton-post-detail">
        <div className="skeleton-breadcrumb"></div>
        <div className="skeleton-title-large"></div>
        <div className="skeleton-meta-large">
          <div className="skeleton-date"></div>
          <div className="skeleton-category"></div>
        </div>
        <div className="skeleton-image-large"></div>
        <div className="skeleton-content">
          {[...Array(8)].map((_, index) => (
            <div key={index} className="skeleton-line"></div>
          ))}
          <div className="skeleton-line-short"></div>
        </div>
        <div className="skeleton-toc">
          <div className="skeleton-toc-title"></div>
          {[...Array(4)].map((_, index) => (
            <div key={index} className="skeleton-toc-item"></div>
          ))}
        </div>
      </div>
    );
  }

  if (type === "posts") {
    return (
      <div className="blog-grid">
        {Array.from({ length: count }, (_, index) => (
          <div key={index} className="blog-card skeleton-card">
            <div className="skeleton-image"></div>
            <div className="blog-card-content">
              <div className="skeleton-meta">
                <div className="skeleton-date"></div>
                <div className="skeleton-category"></div>
              </div>
              <div className="skeleton-title"></div>
              <div className="skeleton-title-short"></div>
              <div className="skeleton-excerpt">
                <div className="skeleton-line"></div>
                <div className="skeleton-line"></div>
                <div className="skeleton-line-short"></div>
              </div>
              <div className="skeleton-button"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (type === "categories") {
    return (
      <div className="skeleton-categories">
        {Array.from({ length: count }, (_, index) => (
          <div key={index} className="skeleton-category-item"></div>
        ))}
      </div>
    );
  }

  return null;
};

export default LoadingSkeleton;
