// src/client/addbankform/AcbPersonalOpenApiForm.jsx

import React, { useState, useEffect, useCallback, useRef } from "react";
import { useNavigate } from "react-router-dom";
import useBankApi from "../../callapi/Bank.jsx";
import { navigateToAccountList } from "../../utils/bankUtils.js";

const AcbPersonalOpenApiForm = React.memo(({ bankName }) => {
  // --- State cho dữ liệu form ---
  const [accName, setAccName] = useState("");
  const [accMobile, setAccMobile] = useState("");
  const [accountNumber, setAccountNumber] = useState("");
  const [agree, setAgree] = useState(false);
  // State popup điều khoản
  const [showTermsModal, setShowTermsModal] = useState(false);
  const [showOtpInput, setShowOtpInput] = useState(false);
  const [otp, setOtp] = useState("");
  // --- State quản lý thông báo (gộp chung) ---
  const [notification, setNotification] = useState({
    error: null,
    success: null,
  });

  // --- Hooks cho API và điều hướng ---
  const {
    data: apiResponse,
    loading: isLoading,
    error: apiError,
    callApi,
  } = useBankApi();
  const navigate = useNavigate();

  // FIX: Dùng useRef để theo dõi response đã được xử lý, tránh vòng lặp render
  const processedResponseRef = useRef(null);

  /**
   * Hàm xử lý chính khi người dùng nhấn nút submit.
   */
  const handleSubmit = async (event) => {
    event.preventDefault();
    event.stopPropagation();
    setNotification({ error: null, success: null });
    const userId = localStorage.getItem("user_id");

    if (!userId) {
      setNotification({
        error: "Lỗi xác thực: Không tìm thấy User ID. Vui lòng đăng nhập lại.",
        success: null,
      });
      return;
    }

    // Nếu đang ở bước nhập OTP thì gửi xác nhận OTP như cũ
    if (showOtpInput) {
      const apiBody = {
        accountNumber,
        shortName: bankName,
        type: "openapi",
        action: "confirm_otp",
        user_id: userId,
        otp,
      };
      await callApi(apiBody);
      return;
    }

    // Kiểm tra xem modal đã đang hiển thị chưa để tránh duplicate
    if (!showTermsModal) {
      setShowTermsModal(true);
    }
    return;
  };

  // Effect để xử lý keyboard events cho modal
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === "Escape" && showTermsModal) {
        setShowTermsModal(false);
      }
    };

    if (showTermsModal) {
      document.addEventListener("keydown", handleKeyDown);
      // Prevent body scroll when modal is open
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
      document.body.style.overflow = "unset";
    };
  }, [showTermsModal]);

  useEffect(() => {
    // FIX: Chỉ chạy logic khi có response mới và response đó chưa được xử lý
    if (apiResponse && apiResponse !== processedResponseRef.current) {
      processedResponseRef.current = apiResponse; // Đánh dấu là đã xử lý

      if (apiResponse.status === true) {
        if ((apiResponse.OTP === 1 || apiResponse.otp === 1) && !showOtpInput) {
          setShowOtpInput(true);
          setNotification({
            success: "Gửi OTP Thành công. Vui lòng nhập mã OTP để hoàn tất.",
            error: null,
          });
        } else {
          const finalMessage = apiResponse.message || "Thao tác thành công!";
          setNotification({
            success: `${finalMessage} Đang chuyển hướng...`,
            error: null,
          });
          navigateToAccountList(navigate, bankName);
        }
      } else if (apiResponse.status === false) {
        setNotification({
          error: apiResponse.message || "Có lỗi xảy ra!",
          success: null,
        });
      }
    }

    // Xử lý lỗi riêng biệt
    if (apiError) {
      setNotification({ error: apiError, success: null });
    }
  }, [apiResponse, apiError, navigate, bankName, showOtpInput]); // FIX: Thêm showOtpInput vào dependency array

  return (
    <>
      {/* Popup điều khoản sử dụng */}
      {showTermsModal && (
        <div
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            width: "100vw",
            height: "100vh",
            background: "rgba(0,0,0,0.3)",
            zIndex: 9999,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
          onClick={(e) => {
            // Đóng modal khi click vào backdrop
            if (e.target === e.currentTarget) {
              setShowTermsModal(false);
            }
          }}
        >
          <div
            style={{
              background: "#fff",
              borderRadius: 8,
              maxWidth: 620,
              width: "90%",
              padding: 24,
              boxShadow: "0 2px 16px rgba(0,0,0,0.15)",
              textAlign: "left",
            }}
            onClick={(e) => {
              // Ngăn event bubbling khi click vào modal content
              e.stopPropagation();
            }}
          >
            <p
              style={{ fontWeight: 700, marginBottom: 12, textAlign: "center" }}
            >
              Điều khoản sử dụng
            </p>
            <div style={{ fontSize: 15, marginBottom: 10 }}>
              Quý khách hàng đang sử dụng Dịch vụ nhận biến động số dư tại{" "}
              <b>Pay2S </b>
              được cung cấp bởi <b>Công ty cổ phần FUTE</b>.
              <div
                className="mt-3"
                style={{
                  textAlign: "justify",
                }}
              >
                {" "}
                Theo <b>Nghị định 13 </b>, để đảm bảo quy tắc bảo mật thông tin
                Khách hàng, <b>Pay2S </b>cần Quý khách hàng xác nhận đồng ý các
                điều khoản khi đăng ký sử dụng dịch vụ nhận thông báo giao dịch
                bởi bên <b>ACB</b>, với các điều khoản, bao gồm:
              </div>
              <ol
                className="my-3"
                style={{
                  textAlign: "justify",
                  listStyleType: "disc",
                  paddingLeft: 18,
                }}
              >
                <li>
                  Đồng ý cho phép Pay2S chia sẻ thông tin cho ACB để đăng ký sử
                  dụng dịch vụ từ ACB.
                </li>
                <li>
                  Đồng ý cho Pay2S nhận thông tin trực tiếp theo dịch vụ khách
                  hàng đã đăng ký với ACB thông qua nền tảng của Pay2S.
                </li>
                <li>
                  Bằng việc xác nhận ở đây, nếu về sau có điều chỉnh về điều
                  khoản điều kiện sử dụng dịch vụ thì thông báo giao dịch sẽ trả
                  về tài khoản định danh.
                </li>
              </ol>
              <span style={{ color: "red", fontWeight: 500 }}>
                * Bằng việc nhấp vào nút "Có, tôi đồng ý" bên dưới, Quý khách
                hàng sẽ đồng ý chấp thuận các điều khoản trên.
              </span>
            </div>
            <div
              className="mt-5"
              style={{ display: "flex", justifyContent: "center", gap: 12 }}
            >
              <button
                className="btn btn-success"
                onClick={async (e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setShowTermsModal(false);
                  setAgree(true);
                  // Sau khi đồng ý, gửi API lấy OTP
                  const userId = localStorage.getItem("user_id");
                  if (!userId) {
                    setNotification({
                      error:
                        "Lỗi xác thực: Không tìm thấy User ID. Vui lòng đăng nhập lại.",
                      success: null,
                    });
                    return;
                  }
                  const apiBody = {
                    accName,
                    accMobile,
                    accountNumber,
                    shortName: bankName,
                    action: "add",
                    user_id: userId,
                    type: "openapi",
                  };
                  await callApi(apiBody);
                }}
              >
                Có, tôi đồng ý
              </button>
              <button
                className="btn btn-secondary"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setShowTermsModal(false);
                }}
              >
                Không đồng ý
              </button>
            </div>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit}>
        {/* Khu vực hiển thị thông báo đã được gộp lại */}
        {notification.error && (
          <div className="alert alert-danger">{notification.error}</div>
        )}
        {notification.success && (
          <div className="alert alert-success">{notification.success}</div>
        )}

        {/* Các trường input */}
        <div className="mt-20">
          <label className="form-label">Họ và tên *</label>
          <input
            className="form-control"
            type="text"
            value={accName}
            onChange={(e) => setAccName(e.target.value.toUpperCase())}
            style={{ textTransform: "uppercase" }}
            required
            disabled={isLoading}
          />
        </div>

        <div className="mt-20">
          <label className="form-label">Số điện thoại *</label>
          <input
            className="form-control"
            type="number"
            value={accMobile}
            onChange={(e) => setAccMobile(e.target.value)}
            required
            disabled={isLoading}
          />
        </div>

        <div className="mt-20">
          <label className="form-label">Số tài khoản {bankName} *</label>
          <input
            className="form-control"
            type="number"
            value={accountNumber}
            onChange={(e) => setAccountNumber(e.target.value)}
            required
            disabled={isLoading}
          />
        </div>

        {/* Trường nhập OTP */}
        {showOtpInput && (
          <div className="mt-20">
            <label className="form-label fw-bold text-danger">Mã OTP *</label>
            <input
              className="form-control"
              type="text"
              value={otp}
              onChange={(e) => setOtp(e.target.value)}
              required
              placeholder="Nhập mã OTP đã được gửi đến bạn"
              autoFocus
            />
          </div>
        )}

        {/* Checkbox điều khoản */}
        {!showOtpInput && (
          <div className="form-check mt-20">
            <input
              className="form-check-input"
              type="checkbox"
              id="agreePay2s"
              checked={agree}
              onChange={(e) => setAgree(e.target.checked)}
              required
            />
            <label className="form-check-label small" htmlFor="agreePay2s">
              Bằng cách cung cấp thông tin cho Pay2S. Bạn đã đồng ý với{" "}
              <a
                href="https://pay2s.vn/chinh-sach-bao-mat"
                className="text-primary-600 fw-semibold"
                target="_blank"
                rel="noopener noreferrer"
              >
                Chính sách bảo mật *
              </a>{" "}
              của Pay2S và cho phép Pay2S truy xuất thông tin tài chính từ ngân
              hàng của bạn và Đồng ý nhận thông báo tiền về từ ngân hàng đến hệ
              thống Pay2S.
            </label>
          </div>
        )}

        {/* Nút Submit */}
        <div className="mt-20">
          <button
            type="submit"
            className="btn btn-success"
            disabled={isLoading}
            id="acb-personal-form-submit"
          >
            {isLoading
              ? "ĐANG XỬ LÝ..."
              : showOtpInput
              ? "XÁC NHẬN OTP"
              : "THÊM TÀI KHOẢN"}
          </button>
        </div>
      </form>
    </>
  );
});

export default AcbPersonalOpenApiForm;
