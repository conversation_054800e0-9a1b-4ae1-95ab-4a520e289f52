import axios from "axios";

const AUTH_URL = "https://api.pay2s.vn/api/v1/auth";

export async function login({ username, password }) {
  return axios.post(
    AUTH_URL,
    new URLSearchParams({ action: "login", username, password }),
    { headers: { "Content-Type": "application/x-www-form-urlencoded" } }
  );
}

export async function confirmOTP({ username, password, otp }) {
  return axios.post(
    AUTH_URL,
    new URLSearchParams({ action: "confirmOTP", username, password, otp }),
    { headers: { "Content-Type": "application/x-www-form-urlencoded" } }
  );
}

export function logout() {
  localStorage.removeItem("token");
  localStorage.removeItem("user_id");
}

export async function register(formData) {
  const payload = new URLSearchParams();
  payload.append("action", "register");
  Object.entries(formData).forEach(([key, value]) => {
    payload.append(key, value);
  });
  return axios.post(AUTH_URL, payload, {
    headers: { "Content-Type": "application/x-www-form-urlencoded" },
  });
}
