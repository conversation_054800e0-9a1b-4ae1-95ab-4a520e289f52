import React from "react";
import SEOHead from "../components/SEOHead";
import Header from "./Header";
import Footer from "./Footer";
import Breadcrumb from "./Breadcrumb";

const ChinhSachBaoMat = () => {
  return (
    <>
      <SEOHead pageKey="chinhSachBaoMat" />
      <Header />
      <Breadcrumb title="Chính sách bảo mật" />

      {/* Content */}
      <section className="space-top space-extra-bottom">
        <div className="container">
          <div className="row">
            <div className="col-xxl-8 col-lg-8">
              <div className="page-single">
                <div className="kt-inside-inner-col">
                  <p>
                    Chính sách bảo mật của Pay2S được xây dựng dựa trên thông lệ
                    quốc tế và Nghị định 13/2023/NĐ-CP của Chính phủ Việt Nam về
                    bảo vệ dữ liệu cá nhân. Khi sử dụng dịch vụ của chúng tôi,
                    bạn tin tưởng cung cấp thông tin của bạn cho chúng tôi.
                    Chúng tôi hiểu rằng đây là một trách nhiệm lớn và chúng tôi
                    nỗ lực bảo vệ thông tin của bạn cũng như để bạn nắm quyền
                    kiểm soát.
                  </p>

                  <p>
                    Pay2S được xây dựng với sứ mệnh mở khóa những tiềm năng của
                    ngân hàng mở. Bằng việc cung cấp một ứng dụng cho phép bạn
                    quản lý tập trung tất cả tài khoản dữ liệu tài chính của
                    mình và cấp quyền truy cập dữ liệu cho các nhà phát triển
                    phần mềm, Chúng tôi giúp cho việc xây dựng những ứng dụng
                    tài chính mở trở nên dễ dàng, an toàn và bảo mật.
                  </p>

                  <h2>Dữ liệu chúng tôi thu thập</h2>
                  <p>
                    Chúng tôi thu thập thông tin để cung cấp dịch vụ tốt hơn cho
                    tất cả người dùng của mình.
                  </p>

                  <h2>Cách chúng tôi sử dụng dữ liệu của bạn</h2>
                  <p>
                    Chúng tôi sử dụng thông tin thu thập được từ tất cả các dịch
                    vụ của mình để cung cấp, duy trì, bảo vệ và cải thiện các
                    dịch vụ đó.
                  </p>

                  <h2>Bảo vệ dữ liệu</h2>
                  <p>
                    Các chính sách và thực hành bảo mật của Pay2S được thiết kế
                    để bảo vệ tính bảo mật và tính toàn vẹn của dữ liệu của bạn
                    (chẳng hạn như Số nhận dạng công dân và dữ liệu nhận dạng
                    khác), cũng như bất kỳ dữ liệu nào khác mà chúng tôi thu
                    thập về bạn.
                  </p>

                  <h2>Liên hệ với Pay2S</h2>
                  <p>
                    Nếu bạn có bất kỳ câu hỏi hoặc khiếu nại nào về Chính sách
                    này hoặc về việc thực hiện bảo mật của chúng tôi nói chung,
                    bạn có thể liên hệ với chúng tôi tại{" "}
                    <a href="mailto:<EMAIL>"><EMAIL></a>.
                  </p>
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="col-xxl-4 col-lg-4">
              <aside className="sidebar-area">
                <div className="widget widget_banner">
                  <h4 className="widget_title">Tài liệu tích hợp</h4>
                  <div className="download-widget-wrap">
                    <a href="https://docs.pay2s.vn" className="th-btn">
                      <i className="fa-light fa-file-pdf me-2"></i>Xem Docs
                    </a>
                  </div>
                </div>
                <div className="widget widget_banner">
                  <div className="widget-banner">
                    <span className="text">LIÊN HỆ NGAY</span>
                    <h2 className="title">Bạn có thắc mắc?</h2>
                    <a href="/lien-he" className="th-btn style3">
                      Liên hệ<i className="fas fa-arrow-right ms-2"></i>
                    </a>
                  </div>
                </div>
              </aside>
            </div>
          </div>
        </div>
      </section>
      <Footer />
    </>
  );
};

export default ChinhSachBaoMat;
