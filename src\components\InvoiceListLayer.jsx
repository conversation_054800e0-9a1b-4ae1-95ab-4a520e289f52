import React, { useEffect, useState, useCallback } from "react";
import * as XLSX from "xlsx";
import { Icon } from "@iconify/react/dist/iconify.js";
import { Link } from "react-router-dom";
import { callOrderApi, createPaymentGateway } from "../callapi/OrderApi";

const InvoiceListLayer = () => {
  const [invoices, setInvoices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [sortAsc, setSortAsc] = useState(true);

  // Bản đồ trạng thái tiếng Anh -> tiếng Việt
  const statusMap = {
    paid: "Đã thanh toán",
    unpaid: "Chưa thanh toán",
    pending: "Đang xử lý",
    cancelled: "Đã hủy",
  };

  // Bản đồ loại hóa đơn
  const typeMap = {
    upgrade: "Nâng cấp",
    renew: "Gia hạn",
    new: "Mới",
    refund: "Hoàn tiền",
  };

  // Hàm lấy dữ liệu từ API, được bọc trong useCallback để tối ưu
  const fetchData = useCallback(async () => {
    setLoading(true);
    const userId = localStorage.getItem("user_id");
    try {
      // Sử dụng hàm callOrderApi chung và truyền vào action, payload
      const res = await callOrderApi({
        action: "get_user_invoices",
        user_id: userId,
      });

      if (res.data.status) {
        setInvoices(res.data.invoices);
      } else {
        setError("Không tải được dữ liệu");
      }
    } catch (err) {
      console.error(err);
      setError("Lỗi kết nối");
    } finally {
      setLoading(false);
    }
  }, []);

  // Lấy dữ liệu lần đầu khi component được tải
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Tự động lấy lại dữ liệu khi người dùng focus lại vào tab
  useEffect(() => {
    window.addEventListener("focus", fetchData);

    // Dọn dẹp listener khi component bị hủy
    return () => {
      window.removeEventListener("focus", fetchData);
    };
  }, [fetchData]);

  // Lọc, tìm kiếm và sắp xếp danh sách hóa đơn
  const filtered = invoices
    .filter((inv) => {
      const labelStatus = statusMap[inv.status] || inv.status;
      const code = inv.invoicenum.toLowerCase();
      const date = inv.date; // API trả về trường 'date'
      const term = searchTerm.toLowerCase();
      const matchSearch = code.includes(term) || date.includes(term);
      const matchStatus = !statusFilter || labelStatus === statusFilter;
      return matchSearch && matchStatus;
    })
    .sort((a, b) => {
      const labelA = statusMap[a.status] || a.status;
      const labelB = statusMap[b.status] || b.status;
      return sortAsc
        ? labelA.localeCompare(labelB)
        : labelB.localeCompare(labelA);
    });

  // Chức năng xuất file Excel
  const exportExcel = () => {
    const ws = XLSX.utils.json_to_sheet(
      filtered.map((inv) => ({
        "Số HĐ": inv.id,
        "Mã HĐ": inv.invoicenum,
        "Loại HĐ": typeMap[inv.type] || inv.type,
        "Ngày tạo": inv.date,
        "Thuế suất": inv.taxrate,
        "Trạng thái": statusMap[inv.status] || inv.status,
        "Tổng tiền": inv.total,
      }))
    );
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "Hóa đơn");
    XLSX.writeFile(wb, "DanhSachHoaDon.xlsx");
  };

  // Xử lý khi nhấn nút thanh toán
  const handlePay = async (inv) => {
    try {
      const res = await createPaymentGateway(inv.invoicenum, inv.id);
      if (res.data.status) {
        const url = res.data.message?.payUrl;
        if (url) {
          window.open(url, "_blank");
        } else {
          alert("Không tìm thấy đường dẫn thanh toán.");
        }
      } else {
        alert(
          "Không thể tạo yêu cầu thanh toán: " +
            (res.data.message || "Lỗi không xác định")
        );
      }
    } catch (err) {
      console.error(err);
      alert("Lỗi khi tạo yêu cầu thanh toán.");
    }
  };

  return (
    <div className="card mt-4">
      <div className="card-header d-flex justify-content-between align-items-center dark:bg-[#1e293b] dark:text-white">
        <div className="d-flex gap-3">
          <select
            className="form-select form-select-sm"
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          >
            <option value="">Tất cả trạng thái</option>
            {Object.values(statusMap).map((s) => (
              <option key={s} value={s}>
                {s}
              </option>
            ))}
          </select>
          <div className="position-relative">
            <input
              type="text"
              className="form-control form-control-sm"
              placeholder="Tìm kiếm mã HĐ hoặc ngày tạo"
              style={{ width: "300px" }}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <Icon
              icon="ion:search-outline"
              className="position-absolute"
              style={{ top: "8px", right: "8px" }}
            />
          </div>
        </div>
        <button className="btn btn-sm btn-success" onClick={exportExcel}>
          <Icon icon="akar-icons:download" /> Excel
        </button>
      </div>
      <div className="card-body mt-3">
        {loading ? (
          <p className="p-3">Đang tải...</p>
        ) : error ? (
          <div className="alert alert-danger m-3">{error}</div>
        ) : (
          <div className="table-responsive">
            <table className="table basic-border-table mb-0">
              <thead>
                <tr>
                  <th>
                    <div className="form-check">
                      <input type="checkbox" className="form-check-input" />
                    </div>
                  </th>
                  <th>Số HĐ</th>
                  <th>Mã HĐ</th>
                  <th>Loại HĐ</th>
                  <th>Ngày tạo</th>
                  <th>Tổng tiền</th>
                  <th
                    onClick={() => setSortAsc(!sortAsc)}
                    style={{ cursor: "pointer" }}
                  >
                    Trạng thái{" "}
                    <Icon
                      icon={
                        sortAsc ? "mdi:sort-ascending" : "mdi:sort-descending"
                      }
                    />
                  </th>
                  <th>Hành động</th>
                </tr>
              </thead>
              <tbody>
                {filtered.length > 0 ? (
                  filtered.map((inv) => {
                    const labelStatus = statusMap[inv.status] || inv.status;
                    return (
                      <tr key={inv.id}>
                        <td>
                          <div className="form-check">
                            <input
                              type="checkbox"
                              className="form-check-input"
                            />
                          </div>
                        </td>
                        <td>{inv.id}</td>
                        <td>
                          <Link to="#" className="text-success">
                            {inv.invoicenum}
                          </Link>
                        </td>
                        <td>{typeMap[inv.type] || inv.type}</td>
                        <td>{inv.date}</td>
                        <td>
                          {new Intl.NumberFormat("vi-VN", {
                            style: "currency",
                            currency: "VND",
                          }).format(inv.total)}
                        </td>
                        <td>
                          {inv.status === "unpaid" ? (
                            <span className="bg-warning-focus text-warning-main px-24 py-4 rounded-pill fw-medium text-sm">
                              {labelStatus}
                            </span>
                          ) : inv.status === "paid" ? (
                            <span className="bg-success-focus text-success-main px-24 py-4 rounded-pill fw-medium text-sm">
                              {labelStatus}
                            </span>
                          ) : (
                            <span className="badge bg-secondary">
                              {labelStatus}
                            </span>
                          )}
                        </td>
                        <td>
                          {inv.status === "unpaid" && (
                            <button
                              className="btn btn-sm btn-primary"
                              onClick={() => handlePay(inv)}
                            >
                              Thanh toán
                            </button>
                          )}
                        </td>
                      </tr>
                    );
                  })
                ) : (
                  <tr>
                    <td colSpan={8} className="text-center py-3">
                      Không có hóa đơn
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default InvoiceListLayer;
