import React, { useState, useEffect } from "react";
import MasterLayout from "../masterLayout/MasterLayout";
import Breadcrumb from "../components/Breadcrumb";
import { Icon } from "@iconify/react";
import useBankApi from "../callapi/Bank";
import useAffiliateApi from "../callapi/AffiliateApi";

const AffWithDraw = ({ report = {} }) => {
  const [activeTab, setActiveTab] = useState("system");
  const [selectedAccount, setSelectedAccount] = useState("");
  const [amount, setAmount] = useState("");
  const [bankName, setBankName] = useState("");
  const [accountName, setAccountName] = useState("");
  const [accountNumber, setAccountNumber] = useState("");
  const [systemAccounts, setSystemAccounts] = useState([]);
  const [allBanks, setAllBanks] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Alert states
  const [alertMessage, setAlertMessage] = useState("");

  // Hook để gọi API bank_account
  const {
    data: bankData,
    loading: bankLoading,
    error: bankError,
    callApi: getBankAccounts,
  } = useBankApi();

  // Hook để gọi API list_bank
  const {
    data: bankListData,
    loading: bankListLoading,
    error: bankListError,
    callApi: getBankList,
  } = useBankApi();

  // Hook để gọi API affiliate withdrawal
  const {
    data: withdrawData,
    loading: withdrawLoading,
    error: withdrawError,
    callApi: callWithdrawApi,
  } = useAffiliateApi();

  // Fetch danh sách tài khoản ngân hàng từ API
  useEffect(() => {
    const userId = localStorage.getItem("user_id");
    if (userId) {
      getBankAccounts({ action: "bank_account", user_id: userId });
      getBankList({ action: "list_bank", user_id: userId });
    }
  }, [getBankAccounts, getBankList]);

  // Xử lý response từ API bank_account
  useEffect(() => {
    if (bankData && bankData.banks && Array.isArray(bankData.banks)) {
      const accounts = [];

      bankData.banks.forEach((bank) => {
        // Add main account
        accounts.push({
          value: bank.id,
          label: `${bank.shortBankName} - ${bank.accountNumber}`,
          bankName: bank.bankName,
          accountNumber: bank.accountNumber,
          accountName: bank.accountName || "Nguyen Trong Vy",
          accountType: "main",
        });

        // Add VA account if exists
        if (bank.vaNumber && bank.vaNumber !== bank.accountNumber) {
          accounts.push({
            value: `${bank.id}_va`,
            label: `${bank.shortBankName} - ${bank.vaNumber} (VA)`,
            bankName: bank.bankName,
            accountNumber: bank.vaNumber,
            accountName: bank.accountName || "Nguyen Trong Vy",
            accountType: "va",
          });
        }
      });

      setSystemAccounts(accounts);
    }
  }, [bankData]);

  // Xử lý response từ API list_bank
  useEffect(() => {
    if (
      bankListData &&
      bankListData.message &&
      Array.isArray(bankListData.message)
    ) {
      const banks = bankListData.message.map((bank) => ({
        value: bank.shortBankName,
        label: bank.bankName,
        shortName: bank.shortBankName,
        status: bank.status,
      }));
      setAllBanks(banks);
    }
  }, [bankListData]);

  const handleSubmit = async (e) => {
    e.preventDefault();

    setIsSubmitting(true);

    try {
      const userId = localStorage.getItem("user_id");

      let withdrawData;

      if (activeTab === "system") {
        // Rút tiền vào tài khoản đã có trong hệ thống
        const selectedAccountData = systemAccounts.find(
          (acc) => acc.value === selectedAccount
        );

        withdrawData = {
          action: "requestPayout",
          user_id: userId,
          amount: amount,
          accountNumber: selectedAccountData?.accountNumber || "",
          accountName: selectedAccountData?.accountName || "",
          bank_id:
            selectedAccountData?.accountType === "va"
              ? selectedAccountData.value.replace("_va", "")
              : selectedAccount,
        };
      } else {
        // Rút tiền vào tài khoản khác
        withdrawData = {
          action: "requestPayout",
          user_id: userId,
          amount: amount,
          accountNumber: accountNumber,
          accountName: accountName,
          bank_id: bankName, // Using bank short name as bank_id
        };
      }

      const result = await callWithdrawApi(withdrawData);

      // Check if there's any error in the hook
      if (withdrawError) {
        setAlertMessage("Lỗi API: " + withdrawError);
        return;
      }

      if (result && result.status === true) {
        setAlertMessage("Yêu cầu rút tiền đã được gửi thành công!");
        // Reset form
        setAmount("");
        if (activeTab === "system") {
          setSelectedAccount("");
        } else {
          setBankName("");
          setAccountName("");
          setAccountNumber("");
        }
      } else {
        const errorMsg =
          result?.message || "Có lỗi xảy ra khi gửi yêu cầu rút tiền!";
        setAlertMessage(errorMsg);
      }
    } catch (error) {
      setAlertMessage("Có lỗi xảy ra: " + error.message);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <MasterLayout>
        <Breadcrumb title="Rút tiền hoa hồng" />

        <div className="row">
          <div className="col-xxl-6 col-xl-6 mt-3">
            <div className="card">
              <div className="card-body">
                {/* Tab Navigation */}
                <ul className="nav nav-tabs mb-20 usertab" role="tablist">
                  <li className="nav-item" role="presentation">
                    <button
                      className={`nav-link ${
                        activeTab === "system" ? "active" : ""
                      }`}
                      onClick={() => setActiveTab("system")}
                      type="button"
                    >
                      Tài khoản trên hệ thống
                    </button>
                  </li>
                  <li className="nav-item" role="presentation">
                    <button
                      className={`nav-link ${
                        activeTab === "other" ? "active" : ""
                      }`}
                      onClick={() => setActiveTab("other")}
                      type="button"
                    >
                      Tài khoản khác
                    </button>
                  </li>
                </ul>

                {/* Tab Content */}
                <div className="tab-content">
                  {/* Tab 1: Tài khoản trên hệ thống */}
                  {activeTab === "system" && (
                    <div className="tab-pane fade show active">
                      <form onSubmit={handleSubmit}>
                        <div className="row">
                          <div className="col-md-12">
                            <div className="mb-20">
                              <label
                                htmlFor="accountSelect"
                                className="form-label fw-semibold text-primary-light"
                              >
                                Chọn tài khoản nhận tiền
                              </label>
                              <select
                                id="accountSelect"
                                className="form-select"
                                value={selectedAccount}
                                onChange={(e) =>
                                  setSelectedAccount(e.target.value)
                                }
                                required
                                disabled={bankLoading}
                              >
                                <option value="">
                                  {bankLoading
                                    ? "Đang tải..."
                                    : "Chọn tài khoản"}
                                </option>
                                {systemAccounts.map((account) => (
                                  <option
                                    key={account.value}
                                    value={account.value}
                                  >
                                    {account.label}
                                  </option>
                                ))}
                              </select>
                              {bankError && (
                                <div className="text-danger small mt-1">
                                  Lỗi tải danh sách tài khoản: {bankError}
                                </div>
                              )}
                              {!bankLoading &&
                                !bankError &&
                                systemAccounts.length === 0 && (
                                  <div className="text-muted small mt-1">
                                    Bạn chưa có tài khoản ngân hàng nào. Vui
                                    lòng thêm tài khoản trước.
                                  </div>
                                )}
                            </div>

                            <div className="mb-20">
                              <label
                                htmlFor="amountInput"
                                className="form-label fw-semibold text-primary-light"
                              >
                                Nhập số tiền{" "}
                                <span className="text-danger">*</span>
                              </label>
                              <input
                                type="number"
                                id="amountInput"
                                className="form-control"
                                placeholder="1.000"
                                value={amount}
                                onChange={(e) => setAmount(e.target.value)}
                                required
                                min="1000"
                                step="1000"
                              />
                            </div>

                            <button
                              type="submit"
                              className="btn btn-success"
                              disabled={
                                !selectedAccount ||
                                !amount ||
                                isSubmitting ||
                                withdrawLoading
                              }
                            >
                              {isSubmitting || withdrawLoading ? (
                                <>
                                  <span
                                    className="spinner-border spinner-border-sm me-2"
                                    role="status"
                                  ></span>
                                  Đang xử lý...
                                </>
                              ) : (
                                "RÚT TIỀN"
                              )}
                            </button>

                            {/* Alert Message */}
                            {alertMessage && (
                              <div
                                className={`alert ${
                                  alertMessage.includes("thành công")
                                    ? "alert-success"
                                    : "alert-danger"
                                } alert-dismissible mt-3 small`}
                              >
                                <Icon
                                  icon={
                                    alertMessage.includes("thành công")
                                      ? "material-symbols:check-circle"
                                      : "material-symbols:error"
                                  }
                                  className="me-1"
                                />
                                {alertMessage}
                                <button
                                  type="button"
                                  className="btn-close btn-close-sm"
                                  onClick={() => setAlertMessage("")}
                                ></button>
                              </div>
                            )}

                            {/* Display withdrawal error for system account */}
                            {withdrawError && activeTab === "system" && (
                              <div className="alert alert-danger mt-3 small">
                                <Icon
                                  icon="material-symbols:error"
                                  className="me-1"
                                />
                                {withdrawError}
                              </div>
                            )}
                          </div>
                        </div>
                      </form>
                    </div>
                  )}

                  {/* Tab 2: Tài khoản khác */}
                  {activeTab === "other" && (
                    <div className="tab-pane fade show active">
                      <form onSubmit={handleSubmit}>
                        <div className="row">
                          <div className="col-md-12">
                            <div className="mb-20">
                              <label
                                htmlFor="bankSelect"
                                className="form-label fw-semibold text-primary-light"
                              >
                                Chọn ngân hàng
                              </label>
                              <select
                                id="bankSelect"
                                className="form-select"
                                value={bankName}
                                onChange={(e) => setBankName(e.target.value)}
                                required
                                disabled={bankListLoading}
                              >
                                <option value="">
                                  {bankListLoading
                                    ? "Đang tải..."
                                    : "Chọn ngân hàng"}
                                </option>
                                {allBanks.map((bank) => (
                                  <option key={bank.value} value={bank.value}>
                                    {bank.label}
                                  </option>
                                ))}
                              </select>
                              {bankListError && (
                                <div className="text-danger small mt-1">
                                  Lỗi tải danh sách ngân hàng: {bankListError}
                                </div>
                              )}
                              {!bankListLoading &&
                                !bankListError &&
                                allBanks.length === 0 && (
                                  <div className="text-muted small mt-1">
                                    Không có ngân hàng nào khả dụng.
                                  </div>
                                )}
                            </div>

                            <div className="mb-20">
                              <label
                                htmlFor="accountNameInput"
                                className="form-label fw-semibold text-primary-light"
                              >
                                Họ và tên chủ tài khoản{" "}
                                <span className="text-danger">*</span>
                              </label>
                              <input
                                type="text"
                                id="accountNameInput"
                                className="form-control"
                                value={accountName}
                                onChange={(e) => setAccountName(e.target.value)}
                                required
                              />
                            </div>

                            <div className="mb-20">
                              <label
                                htmlFor="accountNumberInput"
                                className="form-label fw-semibold text-primary-light"
                              >
                                Số tài khoản{" "}
                                <span className="text-danger">*</span>
                              </label>
                              <input
                                type="text"
                                id="accountNumberInput"
                                className="form-control"
                                value={accountNumber}
                                onChange={(e) =>
                                  setAccountNumber(e.target.value)
                                }
                                required
                              />
                            </div>

                            <div className="mb-20">
                              <label
                                htmlFor="amountInputOther"
                                className="form-label fw-semibold text-primary-light"
                              >
                                Nhập số tiền{" "}
                                <span className="text-danger">*</span>
                              </label>
                              <input
                                type="number"
                                id="amountInputOther"
                                className="form-control"
                                value={amount}
                                onChange={(e) => setAmount(e.target.value)}
                                required
                                min="1000"
                                step="1000"
                              />
                            </div>

                            <button
                              type="submit"
                              className="btn btn-success px-4 py-2"
                              disabled={
                                !bankName ||
                                !accountName ||
                                !accountNumber ||
                                !amount ||
                                isSubmitting ||
                                withdrawLoading
                              }
                            >
                              {isSubmitting || withdrawLoading ? (
                                <>
                                  <span
                                    className="spinner-border spinner-border-sm me-2"
                                    role="status"
                                  ></span>
                                  Đang xử lý...
                                </>
                              ) : (
                                "RÚT TIỀN"
                              )}
                            </button>

                            {/* Alert Message */}
                            {alertMessage && (
                              <div
                                className={`alert ${
                                  alertMessage.includes("thành công")
                                    ? "alert-success"
                                    : "alert-danger"
                                } alert-dismissible mt-3 small`}
                              >
                                <Icon
                                  icon={
                                    alertMessage.includes("thành công")
                                      ? "material-symbols:check-circle"
                                      : "material-symbols:error"
                                  }
                                  className="me-1"
                                />
                                {alertMessage}
                                <button
                                  type="button"
                                  className="btn-close btn-close-sm"
                                  onClick={() => setAlertMessage("")}
                                ></button>
                              </div>
                            )}

                            {/* Display withdrawal error for other account */}
                            {withdrawError && activeTab === "other" && (
                              <div className="alert alert-danger mt-3 small">
                                <Icon
                                  icon="material-symbols:error"
                                  className="me-1"
                                />
                                {withdrawError}
                              </div>
                            )}
                          </div>
                        </div>
                      </form>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar - Số dư cố định */}
          <div className="col-xxl-6 col-xl-6 mt-3 ">
            <div className="card h-100 ">
              <div className="card-body p-5">
                <div className="row w-100">
                  <div className="money-income d-flex p-1 align-items-center gap-3 ">
                    <div className="border bg-neutral-200 rounded p-10 h-100">
                      <Icon
                        icon="streamline-ultimate:accounting-coins-bold"
                        width={"25px"}
                      />
                    </div>
                    <div className="text-start">
                      <span className="text-muted d-block">Số dư</span>
                      <span className="fw-bold fs-5">
                        {(report.available_balance || 0).toLocaleString(
                          "vi-VN"
                        )}{" "}
                        ₫
                      </span>
                    </div>
                  </div>

                  <div className="alert alert-success small mt-3">
                    <Icon icon="material-symbols:info" className="me-1" />
                    Vui lòng nhập đầy đủ và chính xác thông tin để rút tiền. Số
                    dư rút tối thiểu là <strong>1.000.000 ₫</strong>
                    <br />
                    Xem thêm{" "}
                    <a href="#" className="text-decoration-underline">
                      Tài liệu hướng dẫn
                    </a>
                    .
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </MasterLayout>
    </>
  );
};

export default AffWithDraw;
