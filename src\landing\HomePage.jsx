import React from "react";
import SEOHead from "../components/SEOHead";
import Header from "./Header";
import Footer from "./Footer";
import Hero from "./home/<USER>";
import Brand from "./home/<USER>";
import About from "./home/<USER>";
import Process from "./home/<USER>";
import Service from "./home/<USER>";
import Feature from "./home/<USER>";
import Testimonial from "./home/<USER>";
import Cta from "./home/<USER>";
import usePageLoading from "../hooks/usePageLoading";
import OgImage from "../assets/landing/img/hero/meta-open-graph-img.jpg";

const HomePage = () => {
  // Complete loading khi component đã mount
  usePageLoading();

  return (
    <>
      <SEOHead pageKey="home" />
      <Header />
      <Hero />
      <Brand />
      <About />
      <Process />
      <Service />
      <Feature />
      <Testimonial />
      <Cta />
      <Footer />
    </>
  );
};

export default HomePage;
