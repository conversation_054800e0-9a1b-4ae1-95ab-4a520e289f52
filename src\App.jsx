import {
  BrowserRouter,
  Route,
  Routes,
  useLocation,
  Navigate,
} from "react-router-dom";
import "./assets/css/style.css";
import "./assets/css/extra.css";
import { useEffect } from "react";
import ProtectedRoute from "./router/ProtectedRoute";
import RouteScrollToTop from "./helper/RouteScrollToTop";
import { motion } from "framer-motion";
import useAffiliateApi from "./callapi/AffiliateApi";
import Cursor from "./landing/Cursor.jsx";
import GlobalLoadingBar, {
  LoadingProvider,
} from "./components/GlobalLoadingBar";

// Client

import HomePage from "./landing/HomePage";
import OpenApiBank from "./landing/OpenApiBank";
import CongThanhToan from "./landing/CongThanhToan";
import LienHe from "./landing/LienHe";
import BangGia from "./landing/BangGia";
import ChiaSeBienDongSoDu from "./landing/ChiaSeBienDongSoDu";
import TiepNhanXuLy from "./landing/TiepNhanXuLy";
import ChinhSachBaoMat from "./landing/ChinhSachBaoMat";
import ThoaThuan from "./landing/ThoaThuan";

// Blog Components
import {
  MainBlog,
  BlogPost,
  BlogSearch,
  BlogTag,
  BlogAuthor,
  BlogRouter,
} from "./blog";
import BlogTransition from "./components/BlogTransition";

import AffiliateUser from "./client/AffiliateUser";
import AffWithDraw from "./client/AffWithDraw";
import BankTrans from "./client/BankTrans";
import BankList from "./client/BankList";
import ClientLogin from "./client/ClientLogin";
import ClientSignup from "./client/ClientSignup";
import DashBoard from "./client/DashBoard";
import ForgotPass from "./client/ForgotPass";

import IntegrationWebApp from "./client/IntegrationWebApp";
import InvoiceUser from "./client/InvoiceUser";
import PriceTable from "./client/PriceTable";
import QrCreate from "./client/QrCreate";
import TelegramAdd from "./client/TelegramAdd";
import TelegramPage from "./client/TelegramPage";
import Transaction from "./client/Transaction";
import UserProfile from "./client/UserProfile";
import WebhookAdd from "./client/WebhookAdd";
import WebhookHistory from "./client/WebhookHistory";
import WebhookPage from "./client/WebhookPage";
import LarkSuitePage from "./client/LarkSuitePage";
import AcountBank from "./client/AcountBank";
import AddBank from "./client/AddBank";
import CheckOutPage from "./client/CheckOutPage";
import BankAccountDetail from "./client/BankAccountDetail";

// Component để theo dõi affiliate hit khi có ?aff=
const AffiliateHitTracker = () => {
  const location = useLocation();
  const { callApi } = useAffiliateApi();
  useEffect(() => {
    const affiliateId = new URLSearchParams(location.search).get("aff");
    if (affiliateId) {
      callApi({ action: "affiliatehit", affiliate_id: affiliateId }, true);
    }
  }, [location.search, callApi]);
  return null;
};

// Component để render Cursor có điều kiện
const ConditionalCursor = () => {
  const location = useLocation();
  const isClientPage = location.pathname.startsWith("/client");

  // Không render cursor trên các trang /client
  if (isClientPage) {
    return null;
  }

  return <Cursor />;
};

function App() {
  return (
    <BrowserRouter>
      <LoadingProvider>
        <GlobalLoadingBar />
        <ConditionalCursor />
        <AffiliateHitTracker />
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5 }}
        >
          <RouteScrollToTop />

          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/open-api-banking" element={<OpenApiBank />} />
            <Route path="/cong-thanh-toan" element={<CongThanhToan />} />
            <Route path="/lien-he" element={<LienHe />} />
            <Route path="/bang-gia" element={<BangGia />} />
            <Route
              path="/chia-se-bien-dong-so-du"
              element={<ChiaSeBienDongSoDu />}
            />
            <Route path="/tiep-nhan-xu-ly" element={<TiepNhanXuLy />} />
            <Route path="/chinh-sach-bao-mat" element={<ChinhSachBaoMat />} />
            <Route path="/thoa-thuan" element={<ThoaThuan />} />

            {/* Blog Routes with Persistent Layout */}
            <Route path="/blog" element={<BlogTransition />}>
              <Route index element={<MainBlog />} />
              <Route path="search" element={<BlogSearch />} />
              <Route path="tag/:slug" element={<BlogTag />} />
              <Route path="author/:slug" element={<BlogAuthor />} />
              <Route path=":slug" element={<BlogRouter />} />
            </Route>

            <Route path="/client/login" element={<ClientLogin />} />
            <Route path="/client/signup" element={<ClientSignup />} />
            <Route path="/client/forgot" element={<ForgotPass />} />
            <Route element={<ProtectedRoute />}>
              <Route path="/" element={<ClientLogin />} />
              <Route path="/client/dashboard" element={<DashBoard />} />
              <Route path="/client/affiliate" element={<AffiliateUser />} />
              <Route path="/client/affwithdraw" element={<AffWithDraw />} />
              <Route path="/client/banktrans" element={<BankTrans />} />
              <Route path="/client/bank" element={<BankList />} />

              <Route
                path="/client/integration/webapp"
                element={<IntegrationWebApp />}
              />
              <Route path="/client/invoice" element={<InvoiceUser />} />
              <Route path="/client/pricetable" element={<PriceTable />} />
              <Route path="/client/qr" element={<QrCreate />} />
              <Route path="/client/qrcreate" element={<QrCreate />} />
              <Route path="/client/telegram-add" element={<TelegramAdd />} />
              <Route path="/client/telegram" element={<TelegramPage />} />
              <Route path="/client/transaction" element={<Transaction />} />
              <Route path="/client/profile" element={<UserProfile />} />
              <Route path="/client/webhook-add" element={<WebhookAdd />} />
              <Route
                path="/client/webhook-history"
                element={<WebhookHistory />}
              />
              <Route path="/client/webhook" element={<WebhookPage />} />
              <Route path="/client/larksuite" element={<LarkSuitePage />} />
              <Route path="/client/add-bank" element={<AddBank />} />
              <Route path="/client/checkout" element={<CheckOutPage />} />
              <Route
                path="/client/account-bank/:bankShortName"
                element={<AcountBank />}
              />
              <Route
                path="/client/account-bank/:bankShortName/:accountId"
                element={<BankAccountDetail />}
              />
            </Route>

            {/* Catch all 404 routes and redirect to home */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </motion.div>
      </LoadingProvider>
    </BrowserRouter>
  );
}

export default App;
