import React from "react";
import cardBankImage from "../../assets/images/banks/card-bank.png";
import { bankLogos } from "../ImportImage";

const BankCard = ({ accountDetail, formatCurrency }) => {
  // Mapping logo ngân hàng white từ ImportImage
  const bankLogoMap = {
    ACB: bankLogos.logoAcbWhite,
    BIDV: bankLogos.logoBidvWhite,
    MBB: bankLogos.logoMbbWhite,
    MOMO: bankLogos.logoMomoWhite,
    SEAB: bankLogos.logoSeabWhite,
    TCB: bankLogos.logoTcbWhite,
    TPB: bankLogos.logoTpbWhite,
    VCB: bankLogos.logoVcbWhite,
    VTB: bankLogos.logoVtbWhite,
  };

  // Helper function để lấy logo ngân hàng
  const getBankLogo = (shortName) => {
    return bankLogoMap[shortName?.toUpperCase()] || null;
  };

  return (
    <div className="card bank-card-container">
      <div
        className="bank-card-bg"
        style={{
          backgroundImage: `url(${cardBankImage})`,
        }}
      ></div>

      <div className="card-body bank-card-content">
        {/* Logo ngân hàng */}
        <div className="mb-2 d-flex flex-column">
          <div
            className="text-white"
            style={{ fontSize: "0.85rem", fontWeight: "500" }}
          ></div>
          {getBankLogo(accountDetail?.shortBankName) && (
            <img
              src={getBankLogo(accountDetail?.shortBankName)}
              alt={accountDetail?.bankName || accountDetail?.shortBankName}
              style={{
                height: "45px",
                width: "auto",
                maxWidth: "90px",
                objectFit: "contain",
              }}
            />
          )}
          <div
            className="text-white"
            style={{ fontSize: "0.85rem", fontWeight: "500" }}
          >
            {accountDetail?.bankName || accountDetail?.shortBankName}
          </div>
        </div>

        {accountDetail?.balance !== undefined && (
          <div className="mt-2">
            <div className="row mt-3">
              <div className="col-6">
                <div className="bank-card-label text-white">Số tài khoản</div>
                <h6 className="bank-card-account-number">
                  {accountDetail?.accountNumber?.replace(
                    /(\d{4})(?=\d)/g,
                    "$1 "
                  ) ||
                    accountDetail?.vaNumber ||
                    "N/A"}
                </h6>
              </div>
              <div className="col-6">
                <div className="bank-card-label text-white">Chủ tài khoản</div>
                <h6 className="fw-bold mb-0 fs-6 text-white">
                  {accountDetail?.name || accountDetail?.username || "N/A"}
                </h6>
              </div>
            </div>
            <div className="bank-card-label text-white mt-3">
              Tổng giao dịch
            </div>
            <h6 className="fw-bold mb-0 text-white">
              {formatCurrency(accountDetail.total_amount)}
            </h6>
          </div>
        )}
      </div>
    </div>
  );
};

export default BankCard;
