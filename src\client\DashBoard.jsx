import MasterLayout from "../masterLayout/MasterLayout";
import Breadcrumb from "../components/Breadcrumb";
import UnitCountMaster from "../components/child/UnitCountMaster";
import BankLists from "../components/child/BankList";
import ChartTotal from "../components/child/ChartTotal";
import usePageLoading from "../hooks/usePageLoading";

const BlankPagePage = () => {
  // Complete loading khi component đã mount
  usePageLoading();

  return (
    <>
      {/* MasterLayout */}
      <MasterLayout>
        {/* Breadcrumb */}
        <section className="row">
          <Breadcrumb title="Tổng quan" />

          <UnitCountMaster />
        </section>
        <section>
          <div className="row gy-4 mt-4 flex-container">
            {/* Crypto Home Widgets Start */}
            <div className="col-xxl-6 column-auto-height">
              {/* MyCardsOne */}
              <BankLists />
            </div>

            <div className="col-xxl-6 column-auto-height">
              <ChartTotal />.
            </div>
          </div>
        </section>
      </MasterLayout>
    </>
  );
};

export default BlankPagePage;
