import React from "react";
import SEOHead from "../components/SEOHead";
import Header from "./Header";
import Footer from "./Footer";
import Breadcrumb from "./Breadcrumb";

const ThoaThuan = () => {
  return (
    <>
      <SEOHead pageKey="thoaThuan" />
      <Header />
      <Breadcrumb title="Thoả thuận sử dụng phần mềm Pay2S" />

      {/* Content */}
      <section className="space-top space-extra-bottom">
        <div className="container">
          <div className="row">
            <div className="col-xxl-8 col-lg-8">
              <div className="page-single">
                <div className="kt-inside-inner-col">
                  <p>
                    Đ<PERSON>y là thỏa thuận pháp lý giữa khách hàng với Công Ty Cổ
                    Phần FUTE. Quy định các điều khoản trong việc khách hàng sử
                    dụng dịch vụ phần mềm Pay2S. Thỏa thuận này là hợp đồng điện
                    tử giữa hai bên.
                  </p>

                  <p>
                    Bằng cách nhấp chuột vào nút "Đồng ý" khi đăng ký sử dụng,
                    khách hàng đồng ý rằng các điều khoản này sẽ được áp dụng
                    nếu khách hàng lựa chọn truy cập hoặc sử dụng dịch vụ và
                    thao tác nhấp chuột này tương đương với việc hai bên đã ký
                    kết hợp đồng.
                  </p>

                  <h2>1. Các thuật ngữ sử dụng trong thỏa thuận</h2>
                  <p>
                    1.1 Phần mềm: Phần mềm mang tên Pay2S do Công Ty Cổ Phần
                    FUTE cung cấp.
                  </p>
                  <p>
                    1.2 Hệ thống: Bao gồm các máy chủ đặt tại trung tâm dữ liệu
                    của Pay2S, được cài đặt các phần mềm hệ thống và phần mềm
                    Pay2S.
                  </p>
                  <p>
                    1.3 Pay2S: Là Công Ty Cổ Phần FUTE, nhà cung cấp dịch vụ
                    phần mềm Pay2S.
                  </p>

                  <h2>3. Giá cả và phương thức thanh toán</h2>
                  <p>
                    3.1 Lần đầu tiên khi bắt đầu sử dụng phần mềm, khách hàng
                    phải thanh toán phí khởi tạo và phí thuê bao cho Pay2S.
                  </p>
                  <p>
                    3.2 Khách hàng chịu trách nhiệm thanh toán cho Pay2S 100%
                    giá trị của gói sản phẩm/dịch vụ mà khách hàng chọn mua ngay
                    sau khi khách hàng gửi đơn đặt hàng cho Pay2S.
                  </p>

                  <h2>16. Căn cứ pháp lý</h2>
                  <p>
                    <strong>16.1</strong> Căn cứ Bộ Luật dân sự số 91/2015/QH13
                  </p>
                  <p>
                    <strong>16.2</strong> Căn cứ Luật thương mại nước CHXHCN
                    Việt Nam năm 2005;
                  </p>
                  <p>
                    <strong>16.3</strong> Căn cứ Luật Công nghệ thông tin nước
                    CHXHCN Việt Nam năm 2006
                  </p>
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="col-xxl-4 col-lg-4">
              <aside className="sidebar-area">
                <div className="widget widget_banner">
                  <h4 className="widget_title">Tài liệu tích hợp</h4>
                  <div className="download-widget-wrap">
                    <a href="https://docs.pay2s.vn" className="th-btn">
                      <i className="fa-light fa-file-pdf me-2"></i>Xem Docs
                    </a>
                  </div>
                </div>
                <div className="widget widget_banner">
                  <div className="widget-banner">
                    <span className="text">LIÊN HỆ NGAY</span>
                    <h2 className="title">Bạn có thắc mắc?</h2>
                    <a href="/lien-he" className="th-btn style3">
                      Liên hệ<i className="fas fa-arrow-right ms-2"></i>
                    </a>
                  </div>
                </div>
              </aside>
            </div>
          </div>
        </div>
      </section>
      <Footer />
    </>
  );
};

export default ThoaThuan;
