/* Blog Container */
.blog-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Custom styles for Bootstrap navbar */
.navbar-brand img {
  transition: transform 0.3s ease;
}

.navbar-brand:hover img {
  transform: scale(1.05);
}

/* Custom navbar styles */
.nav-link.active {
  color: var(--success-main) !important;
  font-weight: 600;
}

.nav-link:hover {
  color: var(--success-main) !important;
}

/* Bootstrap button override for login */
.btn-success {
  background-color: var(--success-main) !important;
  border-color: var(--success-main) !important;
}

.btn-success:hover {
  background-color: var(--success-hover) !important;
  border-color: var(--success-hover) !important;
}

/* Main Content */
.blog-main {
  flex: 1;
  padding: 2rem 0;
  background: #f8f9fa;
}

.blog-header {
  text-align: center;
  margin-bottom: 3rem;
}

.blog-header h1 {
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 0.5rem;
}

.blog-header p {
  font-size: 1.1rem;
  color: #666;
}

/* Author Header */
.author-header {
  margin-bottom: 3rem;
  padding: 2rem;
  background: white;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.author-info {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.author-avatar {
  flex-shrink: 0;
}

.author-avatar img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #e9ecef;
}

.author-details h1 {
  font-size: 2rem;
  color: #333;
  margin-bottom: 0.5rem;
}

.author-description {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 0.5rem;
}

.posts-count {
  font-size: 0.9rem;
  color: #999;
  margin: 0;
}

.tag-description {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 1rem;
}

.search-results-count {
  font-size: 1rem;
  color: #666;
  margin-bottom: 1rem;
}

/* No Results */
.no-results,
.no-posts {
  text-align: center;
  padding: 3rem 2rem;
  background: white;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.no-results h3,
.no-posts h3 {
  color: #333;
  margin-bottom: 1rem;
}

.no-results p,
.no-posts p {
  color: #666;
  margin-bottom: 2rem;
}

.search-suggestions {
  text-align: left;
  max-width: 400px;
  margin: 2rem auto;
}

.search-suggestions h4 {
  color: #333;
  margin-bottom: 1rem;
}

.search-suggestions ul {
  list-style: none;
  padding: 0;
}

.search-suggestions li {
  padding: 0.5rem 0;
  color: #666;
  border-bottom: 1px solid #eee;
}

.search-suggestions li:before {
  content: "•";
  color: var(--success-main, #28a745);
  margin-right: 0.5rem;
}

.back-to-blog-btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  background: var(--success-main, #28a745);
  color: white;
  text-decoration: none;
  border-radius: 5px;
  transition: background-color 0.3s ease;
}

.back-to-blog-btn:hover {
  background: var(--success-hover, #218838);
  color: white;
  text-decoration: none;
}



/* Blog Grid */
.blog-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.blog-card {
  background: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.blog-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.blog-card-image {
  height: 200px;
  overflow: hidden;
}

.blog-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.blog-card:hover .blog-card-image img {
  transform: scale(1.05);
}

.blog-card-content {
  padding: 1.5rem;
}

.blog-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.blog-date {
  color: #666;
}

.blog-category {
  background: var(--success-main);
  color: white !important;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
}

.blog-title {
  margin-bottom: 1rem;
}

.blog-title a {
  text-decoration: none;
  color: #333;
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.4;
  transition: color 0.3s ease;
}

.blog-title a:hover {
  color: var(--success-main);
}

.blog-excerpt {
  color: #666;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.read-more-btn {
  display: inline-block;
  color: var(--success-main);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.read-more-btn:hover {
  color: var(--success-hover);
}

/* Load More Section */
.load-more-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 3rem;
  gap: 2rem;
}

.load-more-btn {
  padding: 1rem 2rem;
  border: 2px solid var(--success-main);
  background: linear-gradient(135deg, var(--success-main), var(--success-hover));
  color: white;
  border-radius: 50px;
  cursor: pointer;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 200px;
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.load-more-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--success-hover), #1e7e34);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.load-more-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.load-more-btn .fas {
  transition: transform 0.3s ease;
}

.load-more-btn:hover:not(:disabled) .fa-chevron-down {
  transform: translateY(2px);
}

.loading-more-posts {
  width: 100%;
  margin-top: 1rem;
}

/* Loading Spinner */
.loading-spinner {
  text-align: center;
  padding: 3rem 0;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid var(--success-main);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* Error Message */
.error-message {
  text-align: center;
  padding: 3rem 0;
}

.error-message h2 {
  color: #dc3545;
  margin-bottom: 1rem;
}

.retry-btn {
  background: var(--success-main);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1rem;
  transition: background 0.3s ease;
}

.retry-btn:hover {
  background: var(--success-hover);
}

/* Footer */
.blog-footer {
  background: #333;
  color: white;
  padding: 3rem 0 1rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-section h3 {
  color: var(--success-main);
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.footer-brand {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.footer-logo {
  height: 30px;
  width: auto;
  margin-right: 10px;
}

.footer-brand-text {
  font-size: 1.2rem;
  font-weight: bold;
  color: var(--success-main);
}

.footer-description {
  color: #ccc;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.footer-social {
  display: flex;
  gap: 1rem;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 35px;
  height: 35px;
  background: var(--success-main);
  color: white;
  border-radius: 50%;
  text-decoration: none;
  transition: background 0.3s ease;
}

.social-link:hover {
  background: var(--success-hover);
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 0.5rem;
}

.footer-links a {
  color: #ccc;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: var(--success-main);
}

.footer-contact .contact-item {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
  color: #ccc;
}

.footer-contact .contact-item i {
  margin-right: 0.75rem;
  color: var(--success-main);
  width: 16px;
}

.footer-contact a {
  color: #ccc;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-contact a:hover {
  color: var(--success-main);
}

.footer-bottom {
  border-top: 1px solid #555;
  padding-top: 1rem;
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.copyright {
  color: #ccc;
  margin: 0;
}

.footer-bottom-links {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.footer-bottom-links a {
  color: #ccc;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-bottom-links a:hover {
  color: var(--success-main);
}

.separator {
  color: #666;
}

/* Blog Post Styles */
.breadcrumb {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
  font-size: 0.9rem;
}

.breadcrumb a {
  color: var(--success-main);
  text-decoration: none;
  transition: color 0.3s ease;
}

.breadcrumb a:hover {
  color: var(--success-hover);
}

.breadcrumb-separator {
  margin: 0 0.5rem;
  color: #666;
}

.breadcrumb-current {
  color: #666;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.blog-post {
  background: white;
  border-radius: 10px;
  padding: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin-bottom: 3rem;
}

.post-header {
  margin-bottom: 2rem;
}

.post-meta {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  color: #666;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.meta-separator {
  color: #ccc;
}

.post-category {
  background: var(--success-main);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
}

.post-title {
  font-size: 2.5rem !important;
  color: #333;
  line-height: 1.2;
  margin-bottom: 1.5rem;
}

.post-featured-image {
  aspect-ratio: 16/9;
  margin-bottom: 2rem;
  border-radius: 10px;
  overflow: hidden;
}

.post-featured-image img {
  width: 100%;
  height: auto;
  object-fit: cover;
}

.post-content {
  margin-bottom: 2rem;
}

.post-body {
  line-height: 1.8;
  color: #333;
  font-size: 1.1rem;
}

.post-body h1,
.post-body h2,
.post-body h3,
.post-body h4,
.post-body h5,
.post-body h6 {
  margin-top: 2rem;
  margin-bottom: 1rem;
  color: #333;
}

.post-body h2 {
  font-size: 1.8rem !important;
  border-bottom: 2px solid var(--success-main);
  padding-bottom: 0.5rem;
}

.post-body h3 {
  font-size: 1.5rem !important;
  color: var(--success-main);
}

.post-body h4 {
  font-size: 1.2rem !important;
  color: var(--success-main);
}

.post-body p {
  margin-bottom: 1.5rem;
}

.post-body img {
  max-width: 100%;
  height: auto;
  border-radius: 5px;
  margin: 1rem 0;
}

.post-body blockquote {
  border-left: 4px solid var(--success-main);
  padding-left: 1.5rem;
  margin: 1.5rem 0;
  font-style: italic;
  color: #666;
  background: var(--success-surface);
  padding: 1rem 1.5rem;
  border-radius: 0 5px 5px 0;
}

.post-body ul,
.post-body ol {
  margin-bottom: 1.5rem;
  padding-left: 2rem;
}

.post-body li {
  margin-bottom: 0.5rem;
}

.post-body a {
  color: var(--success-main);
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: border-color 0.3s ease;
}

.post-body a:hover {
  border-bottom-color: var(--success-main);
}

.post-footer {
  border-top: 1px solid #eee;
  padding-top: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.post-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.post-tag {
  background: #f8f9fa;
  color: #666;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  border: 1px solid #e9ecef;
}

.post-share {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.post-share span {
  color: #666;
  font-weight: 500;
}

.share-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  color: white;
  text-decoration: none;
  transition: transform 0.3s ease;
}

.share-btn:hover {
  transform: scale(1.1);
}

.share-btn.facebook {
  background: #1877f2;
}

.share-btn.twitter {
  background: #1da1f2;
}

.share-btn.linkedin {
  background: #0077b5;
}

/* Related Posts */
.related-posts {
  margin-bottom: 3rem;
}

.related-posts h2 {
  font-size: 1.8rem;
  color: #333;
  margin-bottom: 1.5rem;
  text-align: center;
}

.related-posts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.related-post-card {
  background: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.related-post-card:hover {
  transform: translateY(-3px);
}

.related-post-image {
  height: 150px;
  overflow: hidden;
}

.related-post-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.related-post-content {
  padding: 1rem;
}

.related-post-title {
  margin-bottom: 0.5rem;
}

.related-post-title a {
  color: #333;
  text-decoration: none;
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.4;
  transition: color 0.3s ease;
}

.related-post-title a:hover {
  color: var(--success-main);
}

.related-post-date {
  color: #666;
  font-size: 0.9rem;
  margin: 0;
}

/* Post Navigation */
.post-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.back-btn {
  display: inline-block;
  background: var(--success-main);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 5px;
  text-decoration: none;
  font-weight: 500;
  transition: background 0.3s ease;
  text-align: center;
}

.back-btn:hover {
  background: var(--success-hover);
}

.category-btn {
  background: var(--primary-main);
}

.category-btn:hover {
  background: var(--primary-hover);
}

/* Back to Blog (legacy support) */
.back-to-blog {
  text-align: center;
  margin-bottom: 2rem;
}

@media (max-width: 768px) {
  .post-navigation {
    flex-direction: column;
    text-align: center;
  }

  .back-btn {
    width: 100%;
    max-width: 300px;
  }

  /* Blog navbar mobile styles */
  .navbar-nav .btn-success {
    margin-top: 10px;
    width: 100%;
    text-align: center;
  }

  .navbar-collapse.show .navbar-nav {
    padding-bottom: 15px;
  }
}

/* No posts section */
.no-posts {
  text-align: center;
  padding: 3rem 0;
  background: white;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.no-posts h3 {
  color: #333;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.no-posts p {
  color: #666;
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 768px) {

  .blog-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .blog-header h1 {
    font-size: 2rem;
  }

  .load-more-btn {
    min-width: 180px;
    padding: 0.875rem 1.5rem;
    font-size: 0.9rem;
  }

  .load-more-section {
    margin-top: 2rem;
  }

  .footer-bottom-content {
    flex-direction: column;
    text-align: center;
  }

  /* Blog Post Mobile Styles */
  .breadcrumb {
    font-size: 0.8rem;
    flex-wrap: wrap;
  }

  .breadcrumb-current {
    max-width: 200px;
  }

  .blog-post {
    padding: 1.5rem;
  }

  .post-title {
    font-size: 1.8rem;
  }

  .post-meta {
    font-size: 0.8rem;
  }

  .post-body {
    font-size: 1rem;
  }

  .post-body h2 {
    font-size: 1.5rem;
  }

  .post-body h3 {
    font-size: 1.3rem;
  }

  .post-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .post-share {
    align-self: center;
  }

  .related-posts-grid {
    grid-template-columns: 1fr;
  }

  .related-post-card {
    max-width: 100%;
  }

  /* Author Header Responsive */
  .author-info {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .author-details h1 {
    font-size: 1.5rem;
  }

  .author-header {
    padding: 1.5rem;
  }

  .search-suggestions {
    text-align: center;
  }
}