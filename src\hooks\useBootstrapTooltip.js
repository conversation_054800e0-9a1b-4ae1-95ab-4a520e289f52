import { useEffect } from "react";

/**
 * Custom hook để tự động kích hoạt Bootstrap tooltip cho các phần tử có data-bs-toggle='tooltip'.
 * @param {Array} deps - Dependency array để re-init tooltip khi dữ liệu thay đổi.
 */
const useBootstrapTooltip = (deps = []) => {
    useEffect(() => {
        // Kiểm tra Bootstrap đã có trong window chưa
        const bootstrap = window.bootstrap || window.Bootstrap;
        if (!bootstrap || !bootstrap.Tooltip) return;

        // Lấy tất cả các phần tử có tooltip
        const tooltipTriggerList = Array.from(
            document.querySelectorAll('[data-bs-toggle="tooltip"]')
        );
        // Khởi tạo tooltip cho từng phần tử
        tooltipTriggerList.forEach((tooltipTriggerEl) => {
            try {
                // Nếu đã có instance thì dispose trước
                if (bootstrap.Tooltip.getInstance(tooltipTriggerEl)) {
                    bootstrap.Tooltip.getInstance(tooltipTriggerEl).dispose();
                }
                new bootstrap.Tooltip(tooltipTriggerEl);
            } catch (e) {
                // Nếu lỗi thì bỏ qua
            }
        });
        // Cleanup: dispose tooltip khi unmount
        return () => {
            tooltipTriggerList.forEach((tooltipTriggerEl) => {
                if (bootstrap.Tooltip.getInstance(tooltipTriggerEl)) {
                    bootstrap.Tooltip.getInstance(tooltipTriggerEl).dispose();
                }
            });
        };
    }, deps);
};

export default useBootstrapTooltip;
