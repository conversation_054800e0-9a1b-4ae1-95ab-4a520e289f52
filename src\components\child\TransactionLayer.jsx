import React, { useEffect, useState, useMemo } from "react";
import * as XLSX from "xlsx";
import { Icon } from "@iconify/react/dist/iconify.js";
import useInvoiceApi from "../../callapi/InvoiceUserBank";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

// Import hình ảnh logo ngân hàng
import bgAcb from "../../assets/images/banks/acb.jpg";
import bgBidv from "../../assets/images/banks/bidv.jpg";
import bgMbb from "../../assets/images/banks/mbb.jpg";
import bgMomo from "../../assets/images/banks/momo.jpg";
import bgSeab from "../../assets/images/banks/seab.jpg";
import bgTcb from "../../assets/images/banks/tcb.jpg";
import bgTpb from "../../assets/images/banks/tpb.jpg";
import bgVcb from "../../assets/images/banks/vcb.jpg";
import bgVtb from "../../assets/images/banks/vtb.jpg";

// === COMPONENT MODAL ===
const TransactionDetailModal = ({
  isOpen,
  onClose,
  transaction,
  onConfirmUpdate,
  loading,
}) => {
  const [newStatus, setNewStatus] = useState("");

  useEffect(() => {
    if (transaction) {
      setNewStatus(transaction.status);
    }
  }, [transaction]);

  if (!isOpen) return null;

  const handleUpdateClick = () => {
    onConfirmUpdate(newStatus);
  };
  const handleModalContentClick = (e) => e.stopPropagation();
  const formatCurrency = (amount) =>
    new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(amount || 0);

  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(0, 0, 0, 0.6)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        zIndex: 1050,
      }}
      onClick={onClose}
    >
      <div
        style={{
          background: "white",
          borderRadius: "8px",
          width: "90%",
          maxWidth: "600px",
          boxShadow: "0 5px 15px rgba(0,0,0,.5)",
        }}
        onClick={handleModalContentClick}
      >
        <div
          style={{
            padding: "1rem",
            borderBottom: "1px solid #dee2e6",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <p style={{ margin: 0 }}>
            Chi tiết hóa đơn: {transaction.invoice_number}
          </p>
          <button
            type="button"
            onClick={onClose}
            style={{
              background: "none",
              border: "none",
              fontSize: "1.5rem",
              cursor: "pointer",
              padding: "0 1rem",
            }}
          >
            &times;
          </button>
        </div>
        <div style={{ padding: "1rem" }}>
          {[
            { label: "Kênh kết nối:", value: transaction.website },
            { label: "Số tiền:", value: formatCurrency(transaction.amount) },
            { label: "Mô tả:", value: transaction.description },
            { label: "Thời gian:", value: transaction.created_at },
            { label: "Cổng thanh toán:", value: transaction.bankName },
            { label: "Mã giao dịch:", value: transaction.transactionNumber },
            {
              label: "Số tiền giao dịch:",
              value: formatCurrency(transaction.paid_amount),
            },
            { label: "ipn_url:", value: transaction.ipn_url },
            { label: "redirect_url:", value: transaction.redirect_url },
          ].map((item) => (
            <div key={item.label} className="row mb-3">
              <label className="col-sm-4 col-form-label fw-medium">
                {item.label}
              </label>
              <div className="col-sm-8">
                <input
                  type="text"
                  readOnly
                  className="form-control-plaintext bg-neutral-300 px-3 rounded"
                  value={item.value || ""}
                />
              </div>
            </div>
          ))}
          <div className="row mb-3">
            <label className="col-sm-4 col-form-label fw-medium">
              Trạng thái:
            </label>
            <div className="col-sm-8">
              <select
                className="form-select"
                value={newStatus}
                onChange={(e) => setNewStatus(e.target.value)}
              >
                <option value="completed">Đã thanh toán</option>
                <option value="pending">Chờ thanh toán</option>
                <option value="fault">Lỗi</option>
                <option value="cancelled">Đã hủy</option>
              </select>
            </div>
          </div>
        </div>
        <div
          style={{
            padding: "1rem",
            borderTop: "1px solid #dee2e6",
            display: "flex",
            justifyContent: "flex-end",
            gap: "0.5rem",
          }}
        >
          <button
            type="button"
            className="btn btn-success"
            onClick={handleUpdateClick}
            disabled={loading}
          >
            {loading ? "Đang cập nhật..." : "CẬP NHẬT"}
          </button>
          <button
            type="button"
            className="btn btn-light border"
            onClick={onClose}
          >
            ĐÓNG
          </button>
        </div>
      </div>
    </div>
  );
};

// === COMPONENT CHÍNH ===
const TransactionLayer = () => {
  const [transactions, setTransactions] = useState([]);
  const {
    data: listData,
    loading: listLoading,
    error: listError,
    callApi: fetchTransactions,
  } = useInvoiceApi();
  const {
    data: updateData,
    loading: updateLoading,
    callApi: updateTransaction,
  } = useInvoiceApi();

  // Filters and Pagination
  const [connectionFilter, setConnectionFilter] = useState("");
  const [accountFilter, setAccountFilter] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [dateFrom, setDateFrom] = useState(null);
  const [dateTo, setDateTo] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Modal
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState(null);

  // Sorting
  const [sortConfig, setSortConfig] = useState({
    key: "created_at",
    direction: "descending",
  });

  // Row Selection
  const [selectedRows, setSelectedRows] = useState(new Set());

  const bankLogoMap = {
    ACB: bgAcb,
    BIDV: bgBidv,
    MBB: bgMbb,
    MOMO: bgMomo,
    SEAB: bgSeab,
    TCB: bgTcb,
    TPB: bgTpb,
    VCB: bgVcb,
    VTB: bgVtb,
  };

  const getBankLogo = (bankName) => {
    if (!bankName) return null;
    const upperCaseBankName = bankName.toUpperCase();
    const bankCode = Object.keys(bankLogoMap).find((code) =>
      upperCaseBankName.includes(code)
    );
    return bankCode ? bankLogoMap[bankCode] : null;
  };

  const statusMap = {
    completed: "Đã thanh toán",
    pending: "Chờ thanh toán",
    fault: "Lỗi",
    cancelled: "Đã hủy",
  };

  const fetchAndSetTransactions = React.useCallback(() => {
    const userId = localStorage.getItem("user_id");
    if (userId) {
      fetchTransactions({ action: "list_bank_invoices", user_id: userId });
    }
  }, [fetchTransactions]);

  useEffect(() => {
    fetchAndSetTransactions();
  }, [fetchAndSetTransactions]);

  useEffect(() => {
    if (listData && listData.invoices) {
      setTransactions(listData.invoices);
    }
  }, [listData]);

  useEffect(() => {
    if (updateData) {
      handleCloseModal();
      fetchAndSetTransactions();
    }
  }, [updateData, fetchAndSetTransactions]);

  // Reset page and selections on filter change
  useEffect(() => {
    setCurrentPage(1);
  }, [
    itemsPerPage,
    connectionFilter,
    accountFilter,
    statusFilter,
    dateFrom,
    dateTo,
    searchTerm,
  ]);

  useEffect(() => {
    setSelectedRows(new Set());
  }, [
    connectionFilter,
    accountFilter,
    statusFilter,
    dateFrom,
    dateTo,
    searchTerm,
  ]);

  const handleOpenModal = (transaction) => {
    setSelectedTransaction(transaction);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedTransaction(null);
  };

  const handleConfirmUpdate = (newStatus) => {
    const userId = localStorage.getItem("user_id");
    updateTransaction({
      action: "edit_bank_invoices",
      user_id: userId,
      invoice_number: selectedTransaction.invoice_number,
      status: newStatus,
    });
  };

  const handleResetFilters = () => {
    setConnectionFilter("");
    setAccountFilter("");
    setStatusFilter("");
    setDateFrom(null);
    setDateTo(null);
    setSearchTerm("");
    setCurrentPage(1);
    setSelectedRows(new Set());
  };

  const handleSort = (key) => {
    let direction = "ascending";
    if (sortConfig.key === key && sortConfig.direction === "ascending") {
      direction = "descending";
    }
    setSortConfig({ key, direction });
    setCurrentPage(1);
  };

  const filteredData = useMemo(() => {
    return transactions.filter((trans) => {
      const matchConnection =
        !connectionFilter || trans.website === connectionFilter;
      const matchAccount = !accountFilter || trans.bankName === accountFilter;
      const matchStatus = !statusFilter || trans.status === statusFilter;
      const transDate = new Date(trans.created_at.replace(" ", "T"));
      const fromDate = dateFrom ? new Date(dateFrom) : null;
      const toDate = dateTo ? new Date(dateTo) : null;
      if (fromDate) fromDate.setHours(0, 0, 0, 0);
      if (toDate) toDate.setHours(23, 59, 59, 999);
      const matchDate =
        (!fromDate || transDate >= fromDate) &&
        (!toDate || transDate <= toDate);
      const term = searchTerm.toLowerCase();
      const matchSearch =
        !term ||
        trans.id.toString().includes(term) ||
        (trans.invoice_number &&
          trans.invoice_number.toLowerCase().includes(term)) ||
        (trans.description && trans.description.toLowerCase().includes(term));
      return (
        matchConnection &&
        matchAccount &&
        matchStatus &&
        matchDate &&
        matchSearch
      );
    });
  }, [
    transactions,
    connectionFilter,
    accountFilter,
    statusFilter,
    dateFrom,
    dateTo,
    searchTerm,
  ]);

  const sortedData = useMemo(() => {
    let sortableItems = [...filteredData];
    if (sortConfig.key) {
      sortableItems.sort((a, b) => {
        const valA = a[sortConfig.key];
        const valB = b[sortConfig.key];
        if (valA < valB) return sortConfig.direction === "ascending" ? -1 : 1;
        if (valA > valB) return sortConfig.direction === "ascending" ? 1 : -1;
        return 0;
      });
    }
    return sortableItems;
  }, [filteredData, sortConfig]);

  const summaryData = useMemo(() => {
    const paid = filteredData.filter((t) => t.status === "completed");
    const pending = filteredData.filter((t) => t.status === "pending");
    const cancelled = filteredData.filter(
      (t) => t.status === "cancelled" || t.status === "fault"
    );
    return {
      paid: {
        amount: paid.reduce((sum, t) => sum + t.amount, 0),
        count: paid.length,
      },
      pending: {
        amount: pending.reduce((sum, t) => sum + t.amount, 0),
        count: pending.length,
      },
      cancelled: {
        amount: cancelled.reduce((sum, t) => sum + t.amount, 0),
        count: cancelled.length,
      },
    };
  }, [filteredData]);

  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return sortedData.slice(startIndex, startIndex + itemsPerPage);
  }, [sortedData, currentPage, itemsPerPage]);

  const totalPages = Math.ceil(sortedData.length / itemsPerPage);

  const formatCurrency = (amount) =>
    new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(amount || 0);

  const getStatusBadge = (status) => {
    switch (status) {
      case "completed":
        return "bg-success-focus text-success-main";
      case "pending":
        return "bg-warning-focus text-warning-main";
      case "fault":
        return "bg-danger-focus text-danger-main";
      case "cancelled":
        return "bg-light text-dark";
      default:
        return "bg-light text-dark";
    }
  };

  // Lấy dữ liệu cho các hành động (in, excel)
  const getDataForAction = () => {
    if (selectedRows.size > 0) {
      return sortedData.filter((trans) => selectedRows.has(trans.id));
    }
    return sortedData;
  };

  const exportExcel = () => {
    const dataToProcess = getDataForAction();
    if (dataToProcess.length === 0) {
      alert("Không có dữ liệu để xuất file Excel.");
      return;
    }
    const dataToExport = dataToProcess.map((t, index) => ({
      STT: index + 1,
      ID: t.id,
      "Kênh kết nối": t.website,
      "Tài khoản": t.bankName,
      "Mã Hóa Đơn": t.invoice_number,
      "Số tiền": t.amount,
      "Mô tả": t.description,
      "Thời gian": t.created_at,
      "Trạng thái": statusMap[t.status] || t.status,
    }));
    const ws = XLSX.utils.json_to_sheet(dataToExport);
    ws["!cols"] = [
      { wch: 5 },
      { wch: 10 },
      { wch: 20 },
      { wch: 20 },
      { wch: 25 },
      { wch: 18 },
      { wch: 40 },
      { wch: 20 },
      { wch: 15 },
    ];
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "GiaoDichDonHang");
    XLSX.writeFile(wb, "danh_sach_giao_dich_don_hang.xlsx");
  };

  const handlePrint = () => {
    const dataToProcess = getDataForAction();
    if (dataToProcess.length === 0) {
      alert("Không có dữ liệu để in.");
      return;
    }
    const tableRows = dataToProcess
      .map(
        (t, index) => `
        <tr>
          <td style="text-align: center;">${index + 1}</td>
          <td>${t.website}</td>
          <td>${t.bankName}</td>
          <td>${t.invoice_number}</td>
          <td style="text-align: right;">${formatCurrency(t.amount)}</td>
          <td>${t.description}</td>
          <td>${t.created_at}</td>
          <td style="text-align: center;">${
            statusMap[t.status] || t.status
          }</td>
        </tr>
      `
      )
      .join("");
    const printContent = `
      <html>
        <head>
          <title>Lịch sử Đơn hàng</title>
          <style>
            @media print {
              body {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
                background-repeat: repeat;
              }
            }
            body { font-family: Arial, sans-serif; margin: 20px; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; font-size: 10px; }
            th, td { border: 1px solid #ddd; padding: 6px; text-align: left; word-break: break-word; }
            th { background-color: #f2f2f2; font-weight: bold; }
            .header { text-align: center; margin-bottom: 20px; }
          </style>
        </head>
        <body>
          <div class="header">
            <h2>Lịch sử Đơn hàng</h2>
            <p>Ngày xuất: ${new Date().toLocaleString("vi-VN")}</p>
            <p>Tổng số đơn hàng: ${dataToProcess.length}</p>
          </div>
          <table>
            <thead>
              <tr>
                <th style="width: 40px; text-align: center;">STT</th>
                <th>Kênh kết nối</th>
                <th>Tài khoản</th>
                <th>Mã Hóa Đơn</th>
                <th style="text-align: right;">Số tiền</th>
                <th>Mô tả</th>
                <th>Thời gian</th>
                <th style="text-align: center;">Trạng thái</th>
              </tr>
            </thead>
            <tbody>${tableRows}</tbody>
          </table>
        </body>
      </html>
    `;
    const printWindow = window.open("");
    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.focus();
    setTimeout(() => {
      printWindow.print();
      printWindow.close();
    }, 250);
  };

  // Row selection handlers
  const handleSelectRow = (rowId) => {
    const newSelectedRows = new Set(selectedRows);
    if (newSelectedRows.has(rowId)) {
      newSelectedRows.delete(rowId);
    } else {
      newSelectedRows.add(rowId);
    }
    setSelectedRows(newSelectedRows);
  };

  const handleSelectAll = () => {
    const currentIds = paginatedData.map((row) => row.id);
    const newSelectedRows = new Set(selectedRows);
    const allOnPageSelected =
      currentIds.length > 0 && currentIds.every((id) => selectedRows.has(id));
    if (allOnPageSelected) {
      currentIds.forEach((id) => newSelectedRows.delete(id));
    } else {
      currentIds.forEach((id) => newSelectedRows.add(id));
    }
    setSelectedRows(newSelectedRows);
  };

  const isAllOnPageSelected = useMemo(() => {
    if (paginatedData.length === 0) return false;
    return paginatedData.every((row) => selectedRows.has(row.id));
  }, [paginatedData, selectedRows]);

  const SortableHeader = ({ columnKey, title }) => {
    const isSorted = sortConfig.key === columnKey;
    const icon =
      sortConfig.direction === "ascending" ? "mdi:arrow-up" : "mdi:arrow-down";
    return (
      <th
        onClick={() => handleSort(columnKey)}
        style={{ cursor: "pointer", whiteSpace: "nowrap" }}
      >
        {title}
        {isSorted && <Icon icon={icon} className="ms-1" />}
      </th>
    );
  };

  const Pagination = () => {
    if (totalPages <= 1) return null;
    const pageNumbers = [];
    const maxPagesToShow = 5;
    if (totalPages <= maxPagesToShow + 2) {
      for (let i = 1; i <= totalPages; i++) pageNumbers.push(i);
    } else {
      pageNumbers.push(1);
      if (currentPage > 3) pageNumbers.push("...");
      let start = Math.max(2, currentPage - 1);
      let end = Math.min(totalPages - 1, currentPage + 1);
      if (currentPage <= 2) end = 3;
      if (currentPage >= totalPages - 1) start = totalPages - 2;
      for (let i = start; i <= end; i++) pageNumbers.push(i);
      if (currentPage < totalPages - 2) pageNumbers.push("...");
      pageNumbers.push(totalPages);
    }
    return (
      <nav aria-label="pagination">
        <ul className="pagination mb-0">
          <li className={`page-item ${currentPage === 1 ? "disabled" : ""}`}>
            <button
              className="page-link"
              onClick={() => setCurrentPage((c) => c - 1)}
            >
              &laquo;
            </button>
          </li>
          {pageNumbers.map((num, index) =>
            num === "..." ? (
              <li key={`ellipsis-${index}`} className="page-item disabled">
                <span className="page-link">...</span>
              </li>
            ) : (
              <li
                key={num}
                className={`page-item ${currentPage === num ? "active" : ""}`}
              >
                <button
                  className="page-link"
                  onClick={() => setCurrentPage(num)}
                >
                  {num}
                </button>
              </li>
            )
          )}
          <li
            className={`page-item ${
              currentPage === totalPages ? "disabled" : ""
            }`}
          >
            <button
              className="page-link"
              onClick={() => setCurrentPage((c) => c + 1)}
            >
              &raquo;
            </button>
          </li>
        </ul>
      </nav>
    );
  };

  return (
    <>
      <div className="container-fluid mt-4">
        <div className="card">
          <div className="card-body">
            <div className="row g-3 align-items-center">
              <div className="col">
                <select
                  className="form-select"
                  value={connectionFilter}
                  onChange={(e) => setConnectionFilter(e.target.value)}
                >
                  <option value="">Tất cả kênh</option>
                  {[...new Set(transactions.map((t) => t.website))].map(
                    (c) =>
                      c && (
                        <option key={c} value={c}>
                          {c}
                        </option>
                      )
                  )}
                </select>
              </div>
              <div className="col">
                <select
                  className="form-select"
                  value={accountFilter}
                  onChange={(e) => setAccountFilter(e.target.value)}
                >
                  <option value="">Tất cả tài khoản</option>
                  {[...new Set(transactions.map((t) => t.bankName))].map(
                    (a) =>
                      a && (
                        <option key={a} value={a}>
                          {a}
                        </option>
                      )
                  )}
                </select>
              </div>
              <div className="col">
                <select
                  className="form-select"
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                >
                  <option value="">Tất cả trạng thái</option>
                  {Object.entries(statusMap).map(([key, value]) => (
                    <option key={key} value={key}>
                      {value}
                    </option>
                  ))}
                </select>
              </div>
              <div className="col-md-4">
                <div className="input-group" style={{ flexWrap: "nowrap" }}>
                  <DatePicker
                    selected={dateFrom}
                    onChange={(date) => setDateFrom(date)}
                    selectsStart
                    startDate={dateFrom}
                    endDate={dateTo}
                    className="form-control"
                    placeholderText="Từ ngày"
                    dateFormat="dd/MM/yyyy"
                    isClearable
                    autoComplete="off"
                  />
                  <span className="input-group-text bg-light border-0">
                    <Icon icon="iconoir:arrow-right" className="text-muted" />
                  </span>
                  <DatePicker
                    selected={dateTo}
                    onChange={(date) => setDateTo(date)}
                    selectsEnd
                    startDate={dateFrom}
                    endDate={dateTo}
                    minDate={dateFrom}
                    className="form-control"
                    placeholderText="Đến ngày"
                    dateFormat="dd/MM/yyyy"
                    isClearable
                    autoComplete="off"
                  />
                </div>
              </div>
              <div className="col-auto">
                <button
                  className="btn btn-primary w-100"
                  onClick={handleResetFilters}
                >
                  <Icon icon="mdi:filter-off" className="me-1" />
                  Chọn lại
                </button>
              </div>
            </div>
          </div>
        </div>

        <div className="row g-3 mt-1">
          <div className="col-md-4">
            <div className="card shadow-sm h-100">
              <div
                className="card-header text-center p-2 fw-medium"
                style={{ backgroundColor: "#dcfce7", color: "#166534" }}
              >
                Tổng tiền đã thanh toán
              </div>
              <div className="card-body p-3">
                <div className="row align-items-center text-center">
                  <div className="col-6 border-end">
                    <small className="text-muted">Giá trị</small>
                    <p className="fs-5 fw-bold mb-0 text-success">
                      {formatCurrency(summaryData.paid.amount)}
                    </p>
                  </div>
                  <div className="col-6">
                    <small className="text-muted">Số lượng</small>
                    <p className="fs-5 fw-bold mb-0">
                      {summaryData.paid.count}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="col-md-4">
            <div className="card shadow-sm h-100">
              <div
                className="card-header text-center p-2 fw-medium"
                style={{ backgroundColor: "#fffbeb", color: "#b45309" }}
              >
                Tổng tiền chờ thanh toán
              </div>
              <div className="card-body p-3">
                <div className="row align-items-center text-center">
                  <div className="col-6 border-end">
                    <small className="text-muted">Giá trị</small>
                    <p className="fs-5 fw-bold mb-0 text-warning">
                      {formatCurrency(summaryData.pending.amount)}
                    </p>
                  </div>
                  <div className="col-6">
                    <small className="text-muted">Số lượng</small>
                    <p className="fs-5 fw-bold mb-0">
                      {summaryData.pending.count}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="col-md-4">
            <div className="card shadow-sm h-100">
              <div
                className="card-header text-center p-2 fw-medium"
                style={{ backgroundColor: "#fee2e2", color: "#991b1b" }}
              >
                Tổng tiền đã hủy/lỗi
              </div>
              <div className="card-body p-3">
                <div className="row align-items-center text-center">
                  <div className="col-6 border-end">
                    <small className="text-muted">Giá trị</small>
                    <p className="fs-5 fw-bold mb-0 text-danger">
                      {formatCurrency(summaryData.cancelled.amount)}
                    </p>
                  </div>
                  <div className="col-6">
                    <small className="text-muted">Số lượng</small>
                    <p className="fs-5 fw-bold mb-0">
                      {summaryData.cancelled.count}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="card mt-3">
          <div className="card-body">
            <div className="d-flex justify-content-between align-items-center mb-3">
              <input
                type="text"
                className="form-control"
                placeholder="Tìm kiếm ID, mã hóa đơn, mô tả..."
                style={{ maxWidth: "300px" }}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <div>
                {selectedRows.size > 0 && (
                  <span className="me-3 text-success fw-bold">
                    Đã chọn: {selectedRows.size}
                  </span>
                )}
                <button
                  className="btn btn-outline-success ms-2"
                  onClick={exportExcel}
                >
                  <Icon icon="mdi:file-excel-outline" /> Excel
                </button>
                <button
                  className="btn btn-outline-secondary ms-2"
                  onClick={handlePrint}
                >
                  <Icon icon="mdi:printer-outline" /> Print
                </button>
              </div>
            </div>

            {listLoading ? (
              <p className="text-center p-5">Đang tải danh sách đơn hàng...</p>
            ) : listError ? (
              <div className="alert alert-danger">{listError}</div>
            ) : (
              <div className="table-responsive">
                <table className="table table-hover basic-border-table mb-0">
                  <thead className="table-light">
                    <tr>
                      <th style={{ width: "60px" }} className="text-center">
                        <div className="form-check">
                          <input
                            type="checkbox"
                            className="form-check-input"
                            checked={isAllOnPageSelected}
                            onChange={handleSelectAll}
                            disabled={paginatedData.length === 0}
                            title="Chọn/Bỏ chọn tất cả trên trang này"
                          />
                        </div>
                      </th>
                      <th style={{ width: "50px" }} className="text-center">
                        STT
                      </th>
                      <SortableHeader
                        columnKey="website"
                        title="Kênh kết nối"
                      />
                      <SortableHeader columnKey="bankName" title="Tài khoản" />
                      <SortableHeader
                        columnKey="invoice_number"
                        title="Mã Hóa Đơn"
                      />
                      <SortableHeader columnKey="amount" title="Số tiền" />
                      <th>Mô tả</th>
                      <SortableHeader
                        columnKey="created_at"
                        title="Thời gian"
                      />
                      <SortableHeader columnKey="status" title="Trạng thái" />
                      <th>Cập nhật</th>
                    </tr>
                  </thead>
                  <tbody>
                    {paginatedData.map((trans, index) => {
                      const isSelected = selectedRows.has(trans.id);
                      return (
                        <tr
                          key={trans.id}
                          className={isSelected ? "table-success" : ""}
                          onClick={() => handleSelectRow(trans.id)}
                          style={{ cursor: "pointer" }}
                        >
                          <td
                            className="text-center"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div className="form-check">
                              <input
                                type="checkbox"
                                className="form-check-input"
                                checked={isSelected}
                                onChange={() => handleSelectRow(trans.id)}
                              />
                            </div>
                          </td>
                          <td className="text-center">
                            {(currentPage - 1) * itemsPerPage + index + 1}
                          </td>
                          <td>{trans.website}</td>
                          <td>
                            {getBankLogo(trans.bankName) ? (
                              <img
                                src={getBankLogo(trans.bankName)}
                                alt={trans.bankName}
                                style={{ height: "40px", borderRadius: "4px" }}
                              />
                            ) : (
                              trans.bankName || "-"
                            )}
                          </td>
                          <td>{trans.invoice_number}</td>
                          <td className="text-end">
                            {formatCurrency(trans.amount)}
                          </td>
                          <td>{trans.description}</td>
                          <td>{trans.created_at}</td>
                          <td>
                            <span
                              className={`badge rounded-pill ${getStatusBadge(
                                trans.status
                              )}`}
                            >
                              {statusMap[trans.status] || trans.status}
                            </span>
                          </td>
                          <td onClick={(e) => e.stopPropagation()}>
                            <button
                              className="btn btn-sm btn-success"
                              onClick={() => handleOpenModal(trans)}
                            >
                              <Icon icon="solar:pen-bold" />
                            </button>
                          </td>
                        </tr>
                      );
                    })}
                    {paginatedData.length === 0 && (
                      <tr>
                        <td colSpan="10" className="text-center py-4">
                          Không tìm thấy đơn hàng nào.
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            )}

            {!listLoading && !listError && sortedData.length > 0 && (
              <div className="d-flex justify-content-between align-items-center mt-3">
                <div className="d-flex align-items-center gap-2">
                  <span className="text-muted">Hiển thị</span>
                  <select
                    className="form-select form-select-sm"
                    style={{ width: "auto" }}
                    value={itemsPerPage}
                    onChange={(e) => {
                      setItemsPerPage(Number(e.target.value));
                    }}
                  >
                    <option value="10">10</option>
                    <option value="20">20</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                  </select>
                  <span className="text-muted">
                    trên {sortedData.length} đơn hàng
                  </span>
                </div>
                {totalPages > 1 && <Pagination />}
              </div>
            )}
          </div>
        </div>
      </div>

      {selectedTransaction && (
        <TransactionDetailModal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          transaction={selectedTransaction}
          onConfirmUpdate={handleConfirmUpdate}
          loading={updateLoading}
        />
      )}
    </>
  );
};

export default TransactionLayer;
