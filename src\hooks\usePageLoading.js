import { useEffect } from 'react';
import { useLoading } from '../components/GlobalLoadingBar';

// Hook để auto complete loading khi component đã mount và data đã load
const usePageLoading = (isDataLoading = false) => {
  const { completeLoading } = useLoading();

  useEffect(() => {
    // Complete loading khi component mount và data không còn loading
    if (!isDataLoading) {
      const timer = setTimeout(() => {
        completeLoading();
      }, 100); // Small delay để đảm bảo UI đã render

      return () => clearTimeout(timer);
    }
  }, [isDataLoading, completeLoading]);

  useEffect(() => {
    // Force complete loading sau 5 giây nếu vẫn chưa complete
    const forceCompleteTimer = setTimeout(() => {
      completeLoading();
    }, 5000); // 5 giây timeout

    return () => clearTimeout(forceCompleteTimer);
  }, [completeLoading]);
};

export default usePageLoading;
