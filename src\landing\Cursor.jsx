"use client";

// React GSAP Cursor Effect for Header
import { useEffect, useRef } from "react";
import gsap from "gsap";
import "../assets/css/landing.css";

const Cursor = () => {
  const cursorRef = useRef(null);
  const followerRef = useRef(null);

  useEffect(() => {
    let posX = 0,
      posY = 0,
      mouseX = 0,
      mouseY = 0;

    const mouseMoveHandler = (e) => {
      mouseX = e.clientX;
      mouseY = e.clientY;
    };

    document.addEventListener("mousemove", mouseMoveHandler);

    // Lưu callback để cleanup đúng chuẩn GSAP v3
    const tickerCallback = () => {
      posX += (mouseX - posX) / 9;
      posY += (mouseY - posY) / 9;
      if (cursorRef.current && followerRef.current) {
        gsap.set(cursorRef.current, {
          x: mouseX,
          y: mouseY,
        });
        gsap.set(followerRef.current, {
          x: posX,
          y: posY,
        });
      }
    };
    gsap.ticker.add(tickerCallback);

    return () => {
      document.removeEventListener("mousemove", mouseMoveHandler);
      gsap.ticker.remove(tickerCallback);
    };
  }, []);

  return (
    <>
      <div ref={cursorRef} className="cursor" />
      <div ref={followerRef} className="cursor2" />
    </>
  );
};

export default Cursor;
