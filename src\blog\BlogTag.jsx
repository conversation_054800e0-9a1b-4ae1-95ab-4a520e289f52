import React, { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, <PERSON> } from "react-router-dom";
import { Helmet } from "react-helmet-async";
import usePageLoading from "../hooks/usePageLoading";
import LoadingSkeleton from "./LoadingSkeleton";
import blogApi from "../callapi/BlogApiNew";
import "./blog.css";

const BlogTag = () => {
  const { slug } = useParams();
  const [tag, setTag] = useState(null);
  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [total, setTotal] = useState(0);
  const [hasMore, setHasMore] = useState(false);

  // Complete loading khi data đã load xong
  usePageLoading(loading);

  // Fetch tag and posts
  const fetchTagAndPosts = async (page = 1) => {
    try {
      if (page === 1) {
        setLoading(true);
        setPosts([]);

        // Fetch tag info
        const tagData = await blogApi.getTagBySlug(slug);
        setTag(tagData);
      } else {
        setLoadingMore(true);
      }

      setError(null);

      // Fetch posts by tag
      const result = await blogApi.getPostsByTag(
        tag?.id || (await blogApi.getTagBySlug(slug)).id,
        page,
        6
      );

      if (page === 1) {
        setPosts(result.posts);
      } else {
        setPosts((prev) => [...prev, ...result.posts]);
      }

      setCurrentPage(result.currentPage);
      setTotalPages(result.totalPages);
      setTotal(result.total);
      setHasMore(result.currentPage < result.totalPages);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  // Load more posts
  const loadMorePosts = async () => {
    if (loadingMore || !hasMore) return;
    await fetchTagAndPosts(currentPage + 1);
  };

  useEffect(() => {
    setCurrentPage(1);
    fetchTagAndPosts(1);
  }, [slug]);

  // Format date
  const formatDate = (dateString) => {
    const options = {
      year: "numeric",
      month: "long",
      day: "numeric",
      timeZone: "Asia/Ho_Chi_Minh",
    };
    return new Date(dateString).toLocaleDateString("vi-VN", options);
  };

  // Extract excerpt from content
  const getExcerpt = (content, maxLength = 150) => {
    const text = content.replace(/<[^>]*>/g, "");
    return text.length > maxLength
      ? text.substring(0, maxLength) + "..."
      : text;
  };

  // Get featured image
  const getFeaturedImage = (post) => {
    if (
      post._embedded &&
      post._embedded["wp:featuredmedia"] &&
      post._embedded["wp:featuredmedia"][0]
    ) {
      return post._embedded["wp:featuredmedia"][0].source_url;
    }
    return "/src/assets/images/blog/blog1.png";
  };

  if (loading) {
    return null; // Loading bar sẽ hiển thị ở BlogTransition
  }

  if (error) {
    return (
      <main className="blog-main">
        <div className="container">
          <div className="error-message">
            <h2>Không tìm thấy tag</h2>
            <p>{error}</p>
            <Link to="/blog" className="retry-btn">
              Quay lại Blog
            </Link>
          </div>
        </div>
      </main>
    );
  }

  return (
    <>
      <Helmet>
        <title>{`Tag: ${tag?.name || slug} - Blog Pay2S`}</title>
        <meta
          name="description"
          content={`Tất cả bài viết với tag "${
            tag?.name || slug
          }" trên Blog Pay2S`}
        />
      </Helmet>

      <main className="blog-main">
        <div className="container">
          {/* Breadcrumb */}
          <nav className="breadcrumb">
            <Link to="/">Trang chủ</Link>
            <span className="breadcrumb-separator">›</span>
            <Link to="/blog">Blog</Link>
            <span className="breadcrumb-separator">›</span>
            <span className="breadcrumb-current">Tag: {tag?.name || slug}</span>
          </nav>

          <div className="blog-header">
            <h1>Tag: {tag?.name || slug}</h1>
            {tag?.description && (
              <p className="tag-description">{tag.description}</p>
            )}
            <p className="posts-count">
              {total > 0 ? `${total} bài viết` : "Không có bài viết nào"}
            </p>
          </div>

          {posts.length > 0 ? (
            <>
              <div className="blog-grid">
                {posts.map((post) => (
                  <article key={post.id} className="blog-card">
                    <div className="blog-card-image">
                      <img
                        src={getFeaturedImage(post)}
                        alt={post.title.rendered}
                        onError={(e) => {
                          e.target.src = "/src/assets/images/blog/blog1.png";
                        }}
                      />
                    </div>

                    <div className="blog-card-content">
                      <div className="blog-meta">
                        <span className="blog-date">
                          {formatDate(post.date)}
                        </span>
                        {post._embedded &&
                          post._embedded["wp:term"] &&
                          post._embedded["wp:term"][0] && (
                            <span className="blog-category">
                              {post._embedded["wp:term"][0][0]?.name}
                            </span>
                          )}
                      </div>

                      <h2 className="blog-title">
                        <Link
                          to={`/blog/${post.slug}`}
                          dangerouslySetInnerHTML={{
                            __html: post.title.rendered,
                          }}
                        />
                      </h2>

                      <div className="blog-excerpt">
                        {getExcerpt(post.excerpt.rendered)}
                      </div>

                      <Link to={`/blog/${post.slug}`} className="read-more-btn">
                        Đọc thêm →
                      </Link>
                    </div>
                  </article>
                ))}
              </div>

              {/* Load More Button */}
              {hasMore && (
                <div className="load-more-section">
                  <button
                    onClick={loadMorePosts}
                    disabled={loadingMore}
                    className="load-more-btn"
                  >
                    {loadingMore ? (
                      <>
                        <i className="fas fa-spinner fa-spin me-2"></i>
                        Đang tải...
                      </>
                    ) : (
                      <>
                        Xem thêm bài viết
                        <i className="fas fa-chevron-down ms-2"></i>
                      </>
                    )}
                  </button>

                  {loadingMore && (
                    <div className="loading-more-posts">
                      <LoadingSkeleton type="posts" count={6} />
                    </div>
                  )}
                </div>
              )}
            </>
          ) : (
            <div className="no-posts">
              <h3>Không có bài viết nào</h3>
              <p>Chưa có bài viết nào được gắn tag "{tag?.name || slug}"</p>
              <Link to="/blog" className="back-to-blog-btn">
                Quay lại Blog
              </Link>
            </div>
          )}
        </div>
      </main>
    </>
  );
};

export default BlogTag;
