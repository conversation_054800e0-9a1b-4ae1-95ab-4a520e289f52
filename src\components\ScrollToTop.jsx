import React from "react";
import { useLocation } from "react-router-dom";
import { useSmoothScroll } from "../hooks/useSmoothScroll";

const ScrollToTop = () => {
  const { scrollToTop, showScrollTop } = useSmoothScroll();
  const location = useLocation();

  // Chỉ hiển thị scroll to top trên landing pages
  const isLandingPage =
    [
      "/",
      "/open-api-banking",
      "/cong-thanh-toan",
      "/chia-se-bien-dong-so-du",
      "/bang-gia",
      "/lien-he",
      "/blog",
      "/chinh-sach-bao-mat",
      "/thoa-thuan",
      "/tiep-nhan-xu-ly",
    ].includes(location.pathname) || location.pathname.startsWith("/blog/");

  // Không hiển thị nếu không phải landing page
  if (!isLandingPage) {
    return null;
  }

  return (
    <button
      className={`scroll-to-top ${showScrollTop ? "show" : ""}`}
      onClick={scrollToTop}
      aria-label="Scroll to top"
      title="Scroll to top"
    >
      <i className="fas fa-chevron-up"></i>
    </button>
  );
};

export default ScrollToTop;
