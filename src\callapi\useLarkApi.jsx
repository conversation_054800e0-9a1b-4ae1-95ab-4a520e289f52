import { useState, useCallback } from "react";
import axios from "axios";
import { API_BASE_URL } from "./apiConfig";

const useLarkApi = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const callApi = useCallback(async (params) => {
    setLoading(true);
    setError(null);

    try {
      const user_id = localStorage.getItem("user_id");
      const token = localStorage.getItem("token");

      if (!user_id || !token) {
        throw new Error("Missing user_id or token");
      }

      const requestBody = new URLSearchParams({
        ...params,
        user_id,
      });

      const response = await axios.post(
        `${API_BASE_URL}/lark`,
        requestBody.toString(),
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/x-www-form-urlencoded",
          },
        }
      );

      if (response.data.status) {
        setData(response.data.data || []);
        return response.data;
      } else {
        throw new Error(response.data.message || "API call failed");
      }
    } catch (err) {
      setError(err.message || "An error occurred");
      setData([]);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchList = useCallback(() => {
    return callApi({ action: "list" });
  }, [callApi]);

  const addLark = useCallback(
    (user_bank_id, hook_id, type, name = "", status = 1) => {
      return callApi({
        action: "add",
        user_bank_id,
        hook_id,
        type,
        name,
        status,
      });
    },
    [callApi]
  );

  const editLark = useCallback(
    (lark_id, updates) => {
      return callApi({
        action: "edit",
        lark_id,
        ...updates,
      });
    },
    [callApi]
  );

  const deleteLark = useCallback(
    (lark_id) => {
      return callApi({
        action: "delete",
        lark_id,
      });
    },
    [callApi]
  );

  const toggleStatus = useCallback(
    (lark_id, status) => {
      return callApi({
        action: "edit",
        lark_id,
        status,
      });
    },
    [callApi]
  );

  return {
    data,
    setData,
    loading,
    error,
    callApi,
    fetchList,
    addLark,
    editLark,
    deleteLark,
    toggleStatus,
  };
};

export default useLarkApi;
