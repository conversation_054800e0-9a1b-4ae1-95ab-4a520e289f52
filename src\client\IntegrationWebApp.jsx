import React, { useEffect, useState, useRef } from "react";
import MasterLayout from "../masterLayout/MasterLayout";
import Breadcrumb from "../components/Breadcrumb";

const mask = (str) => (str ? str.slice(0, 8) + "************" : "");
const copyToClipboard = async (val) => {
  if (!val) return;
  try {
    await navigator.clipboard.writeText(val);
  } catch {}
};

// Custom style for z-index and overflow
const cardStyle = { zIndex: 1, overflow: "visible" };

// Custom hook for copy tooltip
const useCopyTooltip = () => {
  const [copied, setCopied] = useState(false);
  const timer = useRef();
  const doCopy = async (val) => {
    if (!val) return;
    try {
      await navigator.clipboard.writeText(val);
      setCopied(true);
      clearTimeout(timer.current);
      timer.current = setTimeout(() => setCopied(false), 1200);
    } catch {}
  };
  return [copied, doCopy];
};

const IntegrationWebApp = () => {
  // Hiện/ẩn giá trị mục 1
  const [showPartner, setShowPartner] = useState(false);
  const [showAccess, setShowAccess] = useState(false);
  const [showSecret, setShowSecret] = useState(false);
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  useEffect(() => {
    const fetchProfile = async () => {
      setLoading(true);
      setError("");
      try {
        const token = localStorage.getItem("token");
        const userId = localStorage.getItem("user_id");
        if (!token || !userId) throw new Error("Chưa đăng nhập");
        const axios = (await import("axios")).default;
        const res = await axios.post(
          "https://api.pay2s.vn/api/v1/user",
          new URLSearchParams({ action: "get_profile", user_id: userId }),
          {
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/x-www-form-urlencoded",
            },
          }
        );
        if (res.data.status) setProfile(res.data.message);
        else setError("Không lấy được thông tin tích hợp");
      } catch (e) {
        setError("Không lấy được thông tin tích hợp");
      } finally {
        setLoading(false);
      }
    };
    fetchProfile();
  }, []);

  // Tooltip copy state cho từng field
  const [copiedPartner, copyPartner] = useCopyTooltip();
  const [copiedAccess, copyAccess] = useCopyTooltip();
  const [copiedSecret, copySecret] = useCopyTooltip();
  const [copiedApi, copyApi] = useCopyTooltip();
  const [copiedSandboxAcc, copySandboxAcc] = useCopyTooltip();
  const [copiedSandboxBank, copySandboxBank] = useCopyTooltip();
  const [copiedSandboxPartner, copySandboxPartner] = useCopyTooltip();
  const [copiedSandboxAccess, copySandboxAccess] = useCopyTooltip();
  const [copiedSandboxSecret, copySandboxSecret] = useCopyTooltip();
  const [copiedSandboxApi, copySandboxApi] = useCopyTooltip();

  return (
    <MasterLayout>
      <style>{`
        .copied-tooltip {
          position: absolute;
          top: -28px;
          left: 50%;
          transform: translateX(-50%);
          background: #198754;
          color: #fff;
          padding: 2px 10px;
          border-radius: 8px;
          font-size: 12px;
          z-index: 100;
          white-space: nowrap;
          pointer-events: none;
          opacity: 0.95;
        }
      `}</style>
      <Breadcrumb title="Tích hợp Website/Application" />
      {/* Card 1: Thông tin tích hợp */}
      <div className="card mb-5" style={cardStyle}>
        <div className="card-header">
          <h6 className="mb-0">1. Thông tin tích hợp</h6>
        </div>
        <div className="card-body">
          {loading ? (
            <div>Đang tải...</div>
          ) : error ? (
            <div className="text-danger">{error}</div>
          ) : profile ? (
            <div className="row gy-4 gx-4 align-items-end">
              <div className="col-md-4">
                <label className="form-label">Partner code</label>
                <div
                  className="input-group"
                  style={{ zIndex: 2, position: "relative" }}
                >
                  <input
                    type={showPartner ? "text" : "password"}
                    className="form-control"
                    value={
                      showPartner
                        ? profile.partner_code
                        : mask(profile.partner_code)
                    }
                    readOnly
                    style={{ background: showPartner ? "#f8f9fa" : undefined }}
                  />
                  <button
                    className="btn btn-outline-secondary"
                    title={showPartner ? "Ẩn" : "Hiện"}
                    onClick={() => setShowPartner((v) => !v)}
                    tabIndex={-1}
                    type="button"
                  >
                    <i
                      className={
                        showPartner ? "ri-eye-off-line" : "ri-eye-line"
                      }
                    ></i>
                  </button>
                  <button
                    className="btn btn-success position-relative"
                    title="Copy"
                    onClick={() => copyPartner(profile.partner_code)}
                    tabIndex={-1}
                    type="button"
                  >
                    <i className="ri-file-copy-line"></i>
                    {copiedPartner && (
                      <span className="copied-tooltip">Đã copy</span>
                    )}
                  </button>
                </div>
              </div>
              <div className="col-md-4">
                <label className="form-label">Access key</label>
                <div
                  className="input-group"
                  style={{ zIndex: 2, position: "relative" }}
                >
                  <input
                    type={showAccess ? "text" : "password"}
                    className="form-control"
                    value={
                      showAccess ? profile.access_key : mask(profile.access_key)
                    }
                    readOnly
                    style={{ background: showAccess ? "#f8f9fa" : undefined }}
                  />
                  <button
                    className="btn btn-outline-secondary"
                    title={showAccess ? "Ẩn" : "Hiện"}
                    onClick={() => setShowAccess((v) => !v)}
                    tabIndex={-1}
                    type="button"
                  >
                    <i
                      className={showAccess ? "ri-eye-off-line" : "ri-eye-line"}
                    ></i>
                  </button>
                  <button
                    className="btn btn-success position-relative"
                    title="Copy"
                    onClick={() => copyAccess(profile.access_key)}
                    tabIndex={-1}
                    type="button"
                  >
                    <i className="ri-file-copy-line"></i>
                    {copiedAccess && (
                      <span className="copied-tooltip">Đã copy</span>
                    )}
                  </button>
                </div>
              </div>
              <div className="col-md-4">
                <label className="form-label">Secret key</label>
                <div
                  className="input-group"
                  style={{ zIndex: 2, position: "relative" }}
                >
                  <input
                    type={showSecret ? "text" : "password"}
                    className="form-control"
                    value={
                      showSecret ? profile.secret_key : mask(profile.secret_key)
                    }
                    readOnly
                    style={{ background: showSecret ? "#f8f9fa" : undefined }}
                  />
                  <button
                    className="btn btn-outline-secondary"
                    title={showSecret ? "Ẩn" : "Hiện"}
                    onClick={() => setShowSecret((v) => !v)}
                    tabIndex={-1}
                    type="button"
                  >
                    <i
                      className={showSecret ? "ri-eye-off-line" : "ri-eye-line"}
                    ></i>
                  </button>
                  <button
                    className="btn btn-success position-relative"
                    title="Copy"
                    onClick={() => copySecret(profile.secret_key)}
                    tabIndex={-1}
                    type="button"
                  >
                    <i className="ri-file-copy-line"></i>
                    {copiedSecret && (
                      <span className="copied-tooltip">Đã copy</span>
                    )}
                  </button>
                </div>
              </div>
            </div>
          ) : null}
        </div>
      </div>

      {/* Card 2: API EndPoint */}
      <div className="card mb-5" style={cardStyle}>
        <div className="card-header">
          <h6 className="mb-0">
            2. API EndPoint{" "}
            <a
              href="https://docs.pay2s.vn"
              target="_blank"
              rel="noopener noreferrer"
              className="btn btn-success btn-sm ms-2"
            >
              Hướng dẫn tích hợp
            </a>
          </h6>
        </div>
        <div className="card-body">
          <div className="row gy-4 gx-4 align-items-end">
            <div className="col-md-6">
              <label className="form-label">API Endpoint</label>
              <div
                className="input-group"
                style={{ zIndex: 2, position: "relative" }}
              >
                <input
                  type="text"
                  className="form-control"
                  value="https://payment.pay2s.vn/v1/gateway/api/create"
                  readOnly
                />
                <button
                  className="btn btn-success position-relative"
                  title="Copy"
                  onClick={() =>
                    copyApi("https://payment.pay2s.vn/v1/gateway/api/create")
                  }
                  tabIndex={-1}
                  type="button"
                >
                  <i className="ri-file-copy-line"></i>
                  {copiedApi && <span className="copied-tooltip">Đã copy</span>}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Card 3: Sandbox */}
      <div className="card mb-5" style={cardStyle}>
        <div className="card-header">
          <h6 className="mb-0 ">
            3. Hệ thống Sandbox - Thử nghiệm{" "}
            <a
              href="https://sandbox.pay2s.vn/"
              target="_blank"
              rel="noopener noreferrer"
              className="btn btn-success btn-sm ms-2"
            >
              Truy cập hệ thống Sandbox thử nghiệm
            </a>
          </h6>
        </div>
        <div className="card-body">
          <div className="mb-2">
            Quý khách có thể tự tạo tài khoản thử nghiệm và thêm ngân hàng ảo
            tại đây:{" "}
            <a
              href="https://sandbox.pay2s.vn/"
              target="_blank"
              rel="noopener noreferrer"
              className="text-success"
            >
              https://sandbox.pay2s.vn/
            </a>{" "}
            hoặc dùng tài khoản có sẵn:
            <br />
            <span className="ms-3">
              - Username: pay2s
              <br />- Password: 123456789
            </span>
          </div>
          <div className="row g-3">
            <div className="col-md-3">
              <label className="form-label">Số tài khoản Sandbox</label>
              <div
                className="input-group"
                style={{ zIndex: 2, position: "relative" }}
              >
                <input
                  type="text"
                  className="form-control"
                  value="99999999"
                  readOnly
                />
                <button
                  className="btn btn-success position-relative"
                  title="Copy"
                  onClick={() => copySandboxAcc("99999999")}
                  tabIndex={-1}
                  type="button"
                >
                  <i className="ri-file-copy-line"></i>
                  {copiedSandboxAcc && (
                    <span className="copied-tooltip">Đã copy</span>
                  )}
                </button>
              </div>
            </div>
            <div className="col-md-3">
              <label className="form-label">Ngân hàng</label>
              <div
                className="input-group"
                style={{ zIndex: 2, position: "relative" }}
              >
                <input
                  type="text"
                  className="form-control"
                  value="ACB"
                  readOnly
                />
                <button
                  className="btn btn-success position-relative"
                  title="Copy"
                  onClick={() => copySandboxBank("ACB")}
                  tabIndex={-1}
                  type="button"
                >
                  <i className="ri-file-copy-line"></i>
                  {copiedSandboxBank && (
                    <span className="copied-tooltip">Đã copy</span>
                  )}
                </button>
              </div>
            </div>
            <div className="col-md-6">
              <label className="form-label">Partner code</label>
              <div
                className="input-group"
                style={{ zIndex: 2, position: "relative" }}
              >
                <input
                  type="text"
                  className="form-control"
                  value="PAY2S7EPF0SB1ZP27W71"
                  readOnly
                />
                <button
                  className="btn btn-success position-relative"
                  title="Copy"
                  onClick={() => copySandboxPartner("PAY2S7EPF0SB1ZP27W71")}
                  tabIndex={-1}
                  type="button"
                >
                  <i className="ri-file-copy-line"></i>
                  {copiedSandboxPartner && (
                    <span className="copied-tooltip">Đã copy</span>
                  )}
                </button>
              </div>
            </div>
            <div className="col-md-6">
              <label className="form-label">Access key</label>
              <div
                className="input-group"
                style={{ zIndex: 2, position: "relative" }}
              >
                <input
                  type="text"
                  className="form-control"
                  value="66e862c89d4d4d1f34063dc1967fbd64deec4da3cba90af65167fbb8503b2eb3"
                  readOnly
                />
                <button
                  className="btn btn-success position-relative"
                  title="Copy"
                  onClick={() =>
                    copySandboxAccess(
                      "66e862c89d4d4d1f34063dc1967fbd64deec4da3cba90af65167fbb8503b2eb3"
                    )
                  }
                  tabIndex={-1}
                  type="button"
                >
                  <i className="ri-file-copy-line"></i>
                  {copiedSandboxAccess && (
                    <span className="copied-tooltip">Đã copy</span>
                  )}
                </button>
              </div>
            </div>
            <div className="col-md-6">
              <label className="form-label">Secret key</label>
              <div
                className="input-group"
                style={{ zIndex: 2, position: "relative" }}
              >
                <input
                  type="text"
                  className="form-control"
                  value="3cb0ba535605a7f1bad779d727bd234e822703f3c3f531b394524c2e4644ff97"
                  readOnly
                />
                <button
                  className="btn btn-success position-relative"
                  title="Copy"
                  onClick={() =>
                    copySandboxSecret(
                      "3cb0ba535605a7f1bad779d727bd234e822703f3c3f531b394524c2e4644ff97"
                    )
                  }
                  tabIndex={-1}
                  type="button"
                >
                  <i className="ri-file-copy-line"></i>
                  {copiedSandboxSecret && (
                    <span className="copied-tooltip">Đã copy</span>
                  )}
                </button>
              </div>
            </div>
            <div className="col-md-12">
              <label className="form-label">API Endpoint</label>
              <div
                className="input-group"
                style={{ zIndex: 2, position: "relative" }}
              >
                <input
                  type="text"
                  className="form-control"
                  value="https://sandbox-payment.pay2s.vn/v1/gateway/api/create"
                  readOnly
                />
                <button
                  className="btn btn-success position-relative"
                  title="Copy"
                  onClick={() =>
                    copySandboxApi(
                      "https://sandbox-payment.pay2s.vn/v1/gateway/api/create"
                    )
                  }
                  tabIndex={-1}
                  type="button"
                >
                  <i className="ri-file-copy-line"></i>
                  {copiedSandboxApi && (
                    <span className="copied-tooltip">Đã copy</span>
                  )}
                </button>
              </div>
            </div>
            <div className="mb-2">
              Tạo giao dịch chuyển tiền ảo:{" "}
              <a
                href="https://sandbox.pay2s.vn/demo/transfer_demo.html"
                target="_blank"
                className="text-success mb-3"
                style={{ wordBreak: "break-all" }}
              >
                https://sandbox.pay2s.vn/demo/transfer_demo.html
              </a>{" "}
              <hr />
              Kiểm thử IPN:
              <a
                href="https://sandbox.pay2s.vn/demo/ipn_demo.html"
                target="_blank"
                className="text-success mt-3"
                style={{ wordBreak: "break-all" }}
              >
                https://sandbox.pay2s.vn/demo/ipn_demo.html
              </a>{" "}
            </div>
          </div>
        </div>
      </div>
    </MasterLayout>
  );
};

export default IntegrationWebApp;
