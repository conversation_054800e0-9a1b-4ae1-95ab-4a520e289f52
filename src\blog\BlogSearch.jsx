import React, { useState, useEffect } from "react";
import { useSearchParams, Link } from "react-router-dom";
import { Helmet } from "react-helmet-async";
import usePageLoading from "../hooks/usePageLoading";
import LoadingSkeleton from "./LoadingSkeleton";
import blogApi from "../callapi/BlogApiNew";
import "./blog.css";

const BlogSearch = () => {
  const [searchParams] = useSearchParams();
  const query = searchParams.get("q") || "";

  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [total, setTotal] = useState(0);
  const [hasMore, setHasMore] = useState(false);

  // Complete loading khi data đã load xong
  usePageLoading(loading);

  // Search posts
  const searchPosts = async (searchQuery, page = 1) => {
    if (!searchQuery.trim()) {
      setSearchResults([]);
      setLoading(false);
      return;
    }

    try {
      if (page === 1) {
        setLoading(true);
        setSearchResults([]);
      } else {
        setLoadingMore(true);
      }
      setError(null);

      const result = await blogApi.searchPosts(searchQuery, page, 6);

      if (page === 1) {
        setSearchResults(result.posts);
      } else {
        setSearchResults((prev) => [...prev, ...result.posts]);
      }

      setCurrentPage(result.currentPage);
      setTotalPages(result.totalPages);
      setTotal(result.total);
      setHasMore(result.currentPage < result.totalPages);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  // Load more results
  const loadMoreResults = async () => {
    if (loadingMore || !hasMore) return;
    await searchPosts(query, currentPage + 1);
  };

  useEffect(() => {
    setCurrentPage(1);
    searchPosts(query, 1);
  }, [query]);

  // Format date
  const formatDate = (dateString) => {
    const options = {
      year: "numeric",
      month: "long",
      day: "numeric",
      timeZone: "Asia/Ho_Chi_Minh",
    };
    return new Date(dateString).toLocaleDateString("vi-VN", options);
  };

  // Extract excerpt from content
  const getExcerpt = (content, maxLength = 150) => {
    const text = content.replace(/<[^>]*>/g, "");
    return text.length > maxLength
      ? text.substring(0, maxLength) + "..."
      : text;
  };

  // Get featured image
  const getFeaturedImage = (post) => {
    if (
      post._embedded &&
      post._embedded["wp:featuredmedia"] &&
      post._embedded["wp:featuredmedia"][0]
    ) {
      return post._embedded["wp:featuredmedia"][0].source_url;
    }
    return "/src/assets/images/blog/blog1.png";
  };

  if (loading) {
    return null; // Loading bar sẽ hiển thị ở BlogTransition
  }

  return (
    <>
      <Helmet>
        <title>{`Tìm kiếm: ${query} - Blog Pay2S`}</title>
        <meta
          name="description"
          content={`Kết quả tìm kiếm cho "${query}" trên Blog Pay2S`}
        />
        <meta name="robots" content="noindex, follow" />
      </Helmet>

      <main className="blog-main">
        <div className="container">
          {/* Breadcrumb */}
          <nav className="breadcrumb">
            <Link to="/">Trang chủ</Link>
            <span className="breadcrumb-separator">›</span>
            <Link to="/blog">Blog</Link>
            <span className="breadcrumb-separator">›</span>
            <span className="breadcrumb-current">Tìm kiếm</span>
          </nav>

          <div className="blog-header">
            <h1>Tìm kiếm: "{query}"</h1>
            {!loading && (
              <p className="search-results-count">
                {total > 0
                  ? `Tìm thấy ${total} kết quả`
                  : "Không tìm thấy kết quả nào"}
              </p>
            )}
          </div>

          {error && (
            <div className="error-message">
              <p>{error}</p>
              <button
                onClick={() => searchPosts(query, 1)}
                className="retry-btn"
              >
                Thử lại
              </button>
            </div>
          )}

          {!error && searchResults.length > 0 && (
            <>
              <div className="blog-grid">
                {searchResults.map((post) => (
                  <article key={post.id} className="blog-card">
                    <div className="blog-card-image">
                      <img
                        src={getFeaturedImage(post)}
                        alt={post.title.rendered}
                        onError={(e) => {
                          e.target.src = "/src/assets/images/blog/blog1.png";
                        }}
                      />
                    </div>

                    <div className="blog-card-content">
                      <div className="blog-meta">
                        <span className="blog-date">
                          {formatDate(post.date)}
                        </span>
                        {post._embedded &&
                          post._embedded["wp:term"] &&
                          post._embedded["wp:term"][0] && (
                            <span className="blog-category">
                              {post._embedded["wp:term"][0][0]?.name}
                            </span>
                          )}
                      </div>

                      <h2 className="blog-title">
                        <Link
                          to={`/blog/${post.slug}`}
                          dangerouslySetInnerHTML={{
                            __html: post.title.rendered,
                          }}
                        />
                      </h2>

                      <div className="blog-excerpt">
                        {getExcerpt(post.excerpt.rendered)}
                      </div>

                      <Link to={`/blog/${post.slug}`} className="read-more-btn">
                        Đọc thêm →
                      </Link>
                    </div>
                  </article>
                ))}
              </div>

              {/* Load More Button */}
              {hasMore && (
                <div className="load-more-section">
                  <button
                    onClick={loadMoreResults}
                    disabled={loadingMore}
                    className="load-more-btn"
                  >
                    {loadingMore ? (
                      <>
                        <i className="fas fa-spinner fa-spin me-2"></i>
                        Đang tải...
                      </>
                    ) : (
                      <>
                        Xem thêm kết quả
                        <i className="fas fa-chevron-down ms-2"></i>
                      </>
                    )}
                  </button>

                  {loadingMore && (
                    <div className="loading-more-posts">
                      <LoadingSkeleton type="posts" count={6} />
                    </div>
                  )}
                </div>
              )}
            </>
          )}

          {!error && !loading && searchResults.length === 0 && query.trim() && (
            <div className="no-results">
              <h3>Không tìm thấy kết quả</h3>
              <p>Không có bài viết nào phù hợp với từ khóa "{query}"</p>
              <div className="search-suggestions">
                <h4>Gợi ý:</h4>
                <ul>
                  <li>Kiểm tra lại chính tả</li>
                  <li>Thử sử dụng từ khóa khác</li>
                  <li>Sử dụng từ khóa ngắn gọn hơn</li>
                </ul>
              </div>
              <Link to="/blog" className="back-to-blog-btn">
                Quay lại Blog
              </Link>
            </div>
          )}
        </div>
      </main>
    </>
  );
};

export default BlogSearch;
