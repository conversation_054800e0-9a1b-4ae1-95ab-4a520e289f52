// src/utils/bankUtils.js

/**
 * Utility function để navigate về trang danh sách tài khoản ngân hàng
 * với force refresh để đảm bảo dữ liệu mới được hiển thị
 * 
 * @param {function} navigate - React Router navigate function
 * @param {string} bankName - Tên ngân hàng (shortName)
 * @param {number} delay - Delay trước khi navigate (ms), default 2000
 */
export const navigateToAccountList = (navigate, bankName, delay = 2000) => {
  // Trigger event ngay lập tức để bắt đầu invalidate cache
  window.dispatchEvent(new CustomEvent('bankAccountAdded', {
    detail: { bankName, timestamp: Date.now(), immediate: true }
  }));

  setTimeout(() => {

    // Chỉ navigate với state, không trigger event nữa để tránh double refresh
    // location.state sẽ handle việc refresh
    navigate(`/client/account-bank/${bankName}`, {
      state: {
        justAddedBank: true,
        forceRefresh: true,
        timestamp: Date.now(),
      },
    });
  }, delay);
};

/**
 * Utility function để invalidate cache cho tài khoản ngân hàng
 * 
 * @param {function} invalidateCache - Function từ useBankApi hook
 * @param {string} userId - User ID
 */
export const invalidateBankAccountCache = (invalidateCache, userId) => {
  if (invalidateCache && userId) {
    invalidateCache({ action: "bank_account", user_id: userId });
  }
};

/**
 * Utility function để force refresh danh sách tài khoản ngân hàng
 * Có thể được gọi từ bất kỳ đâu trong ứng dụng
 */
export const triggerBankAccountRefresh = () => {
  window.dispatchEvent(new CustomEvent('bankAccountAdded'));
};
