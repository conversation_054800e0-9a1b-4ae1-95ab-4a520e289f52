import React, { useEffect, useState } from "react";
import axios from "axios";
import { Icon } from "@iconify/react/dist/iconify.js";
import { <PERSON> } from "react-router-dom";
import Slider from "react-slick";
import { bankLogos } from "../../client/ImportImage";

const bankAssets = {
  ACB: { bg: bankLogos.logoAcb },
  BIDV: { bg: bankLogos.logoBidv },
  MBB: { bg: bankLogos.logoMbb },
  MoMo: { bg: bankLogos.logoMomo },
  SEAB: { bg: bankLogos.logoSeab },
  TCB: { bg: bankLogos.logoTcb },
  // TPB: { bg: bankLogos.logoTpb },
  VCB: { bg: bankLogos.logoVcb },
  VTB: { bg: bankLogos.logoVtb },
};

const BankList = () => {
  const [cardsData, setCardsData] = useState([]);

  const settings = {
    dots: true,
    arrows: false,
    infinite: true,
    speed: 1000,
    slidesToShow: 2,
    slidesToScroll: 1,
    initialSlide: 0,
    responsive: [
      { breakpoint: 992, settings: { slidesToShow: 2, slidesToScroll: 1 } },
      { breakpoint: 576, settings: { slidesToShow: 1, slidesToScroll: 1 } },
    ],
  };

  useEffect(() => {
    const token = localStorage.getItem("token");
    const userId = localStorage.getItem("user_id");
    axios
      .post(
        "https://api.pay2s.vn/api/v1/bank",
        new URLSearchParams({ action: "list_bank", user_id: userId }),
        { headers: { Authorization: `Bearer ${token}` } }
      )
      .then((res) => {
        if (res.data.status) {
          const activeBanks = res.data.message.filter(
            (b) => b.status === "Active"
          );
          const mapped = activeBanks.map((b) => {
            const key = b.shortBankName;
            const asset = bankAssets[key] || {};
            const isPartner =
              b.isPartner === true ||
              (typeof b.isPartner === "string" &&
                b.isPartner.trim() === "true");
            return {
              id: b.id,
              title: key,
              bg: asset.bg,
              isPartner,
            };
          });
          // Đưa ngân hàng đối tác lên đầu
          const sorted = mapped.sort(
            (a, b) => Number(b.isPartner) - Number(a.isPartner)
          );
          setCardsData(sorted);
        }
      })
      .catch((err) => console.error("Error loading banks", err));
  }, []);

  return (
    <div className="card h-100 radius-8 border-0">
      <div className="card-body">
        <div className="d-flex align-items-center flex-wrap gap-2 justify-content-between mb-20">
          <h6 className="mb-2 fw-bold text-lg">
            Danh sách ngân hàng hỗ trợ biến động số dư đang hoạt động
          </h6>
          <Link
            to="/client/add-bank"
            className="btn btn-outline-primary text-white d-inline-flex align-items-center gap-2 text-sm btn-sm px-8 py-6"
          >
            <Icon icon="ph:plus-circle" className="icon text-xl" /> Thêm ngân
            hàng
          </Link>
        </div>
        <div className="row">
          {cardsData.map((card) => (
            <div
              key={card.id}
              className="col-4 radius-8 col-sm-4 col-lg-4 col-xl-4 g-2"
            >
              <div className="p-10 radius-8 overflow-hidden position-relative z-1 ">
                <img
                  src={card.bg}
                  alt={`${card.title} background`}
                  className="w-100 radius-8 border"
                />
                {card.isPartner && (
                  <div
                    className="position-absolute top-0 end-0 p-1"
                    data-bs-toggle="tooltip"
                    data-bs-placement="top"
                    data-bs-custom-class="custom-tooltip"
                    data-bs-title="Ngân hàng đối tác chính thức"
                  >
                    <Icon
                      icon="mdi:check-decagram"
                      className="text-success"
                      fontSize={20}
                    />
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default BankList;
