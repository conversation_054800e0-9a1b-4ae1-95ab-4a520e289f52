import React, { useState, useEffect } from "react";
import { Icon } from "@iconify/react/dist/iconify.js";
import invoiceUser from "../../callapi/InvoiceUserBank";

const StatWidget = ({
  gradientClass,
  iconClass,
  icon,
  title,
  value,
  loading,
}) => {
  return (
    <div className="col-xxl-3 col-sm-6 mb-4">
      <div
        className={`card px-24 py-16 shadow-none radius-8 border h-100 ${gradientClass}`}
      >
        <div className="card-body p-0">
          <div className="d-flex flex-wrap align-items-center justify-content-between gap-1 mb-8">
            <div className="d-flex align-items-center">
              <div className="w-64-px h-64-px radius-16 bg-base-50 d-flex justify-content-center align-items-center me-20">
                <span
                  className={`mb-0 w-40-px h-40-px flex-shrink-0 text-white d-flex justify-content-center align-items-center radius-8 h6 mb-0 ${iconClass}`}
                >
                  <Icon icon={icon} className="icon" />
                </span>
              </div>
              <div>
                <span className="mb-2 fw-medium text-secondary-light text-md">
                  {title}
                </span>
                <h6 className="fw-semibold my-1">{loading ? "..." : value}</h6>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const TotalResult = () => {
  const { data: apiData, loading, error, callApi } = invoiceUser();
  const [displayWidgets, setDisplayWidgets] = useState([]);
  const [authError, setAuthError] = useState(null);

  const widgetTemplates = [
    {
      key: "pending_payment",
      gradientClass: "bg-gradient-start-5",
      iconClass: "bg-orange",
      icon: "ic:round-access-time",
      title: "Chờ thanh toán",
      value: 0,
    },
    {
      key: "today_transactions",
      gradientClass: "bg-gradient-start-2",
      iconClass: "bg-purple",
      icon: "solar:calendar-date-bold",
      title: "GD hôm nay",
      value: 0,
    },
    {
      key: "yesterday_transactions",
      gradientClass: "bg-gradient-start-3",
      iconClass: "bg-primary-600",
      icon: "solar:calendar-date-bold",
      title: "GD hôm qua",
      value: 0,
    },
    {
      key: "this_month_transactions",
      gradientClass: "bg-gradient-start-4",
      iconClass: "bg-success-main",
      icon: "solar:chart-bold",
      title: "GD tháng này",
      value: 0,
    },
  ];

  useEffect(() => {
    const userId = localStorage.getItem("user_id");

    if (userId) {
      callApi({
        action: "list_bank_invoices",
        user_id: userId,
      });
    } else {
      const message = "Không tìm thấy User ID. Vui lòng đăng nhập.";
      console.error(message);
      setAuthError(message);
    }
  }, []);

  useEffect(() => {
    if (loading || authError || !apiData || !apiData.invoices) {
      setDisplayWidgets(widgetTemplates);
      return;
    }

    const invoices = apiData.invoices;
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);

    const pendingCount = invoices.filter(
      (inv) => inv.status === "pending"
    ).length;
    const todayCount = invoices.filter((inv) => {
      const createdAt = new Date(inv.created_at.replace(" ", "T"));
      createdAt.setHours(0, 0, 0, 0);
      return createdAt.getTime() === today.getTime();
    }).length;
    const yesterdayCount = invoices.filter((inv) => {
      const createdAt = new Date(inv.created_at.replace(" ", "T"));
      createdAt.setHours(0, 0, 0, 0);
      return createdAt.getTime() === yesterday.getTime();
    }).length;
    const thisMonthCount = invoices.filter((inv) => {
      const createdAt = new Date(inv.created_at.replace(" ", "T"));
      return (
        createdAt.getMonth() === today.getMonth() &&
        createdAt.getFullYear() === today.getFullYear()
      );
    }).length;

    const calculatedValues = {
      pending_payment: pendingCount,
      today_transactions: todayCount,
      yesterday_transactions: yesterdayCount,
      this_month_transactions: thisMonthCount,
    };

    const updatedWidgets = widgetTemplates.map((widget) => ({
      ...widget,
      value: calculatedValues[widget.key],
    }));

    setDisplayWidgets(updatedWidgets);
  }, [apiData, loading, authError]);

  if (authError) {
    return <div className="col-12 alert alert-warning">{authError}</div>;
  }
  if (error) {
    return (
      <div className="col-12 alert alert-danger">
        Lỗi khi tải dữ liệu: {error}
      </div>
    );
  }

  return (
    <>
      {displayWidgets.map((widget) => {
        const { key, ...props } = widget;
        return (
          <StatWidget key={key} {...props} loading={loading && !authError} />
        );
      })}
    </>
  );
};

export default TotalResult;
