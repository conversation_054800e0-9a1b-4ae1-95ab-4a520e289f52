import React from "react";
import SEOHead from "../components/SEOHead";
import Header from "./Header";
import Footer from "./Footer";
import usePageLoading from "../hooks/usePageLoading";
import Breadcrumb from "./Breadcrumb";
import LienHeContact from "./product/LienHeContact";
import Cta from "./product/OpenApiCta";

const LienHe = () => {
  // Complete loading khi component đã mount
  usePageLoading();

  return (
    <>
      <SEOHead pageKey="lienHe" />
      <Header />
      <Breadcrumb />
      <LienHeContact />
      <Cta />
      <Footer />
    </>
  );
};

export default LienHe;
