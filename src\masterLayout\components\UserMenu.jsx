import React from "react";
import { <PERSON> } from "react-router-dom";
import { Icon } from "@iconify/react/dist/iconify.js";
import ProfileIcon from "../../assets/images/profile.png";

const UserMenu = ({ displayName, displayPlan }) => (
  <div className="dropdown user-button">
    <button
      className="d-flex justify-content-center align-items-center rounded-circle border-0 p-0"
      type="button"
      data-bs-toggle="dropdown"
      aria-expanded="false"
    >
      <img
        src={ProfileIcon}
        alt="User Avatar"
        className="w-40-px h-40-px object-fit-cover rounded-circle"
      />
    </button>
    <div className="dropdown-menu to-top dropdown-menu-sm">
      <div className="py-12 px-16 radius-8 bg-primary-50 mb-16 d-flex align-items-center justify-content-between gap-2">
        <div>
          <h6 className="text-lg text-primary-light fw-semibold mb-2">
            {displayName}
          </h6>
          <span className="text-secondary-light fw-medium text-sm">
            {displayPlan}
          </span>
        </div>
      </div>
      <ul className="list-unstyled px-16">
        <li>
          <Link
            to="/client/profile"
            className="dropdown-item text-black px-0 py-8 hover-bg-transparent hover-text-primary d-flex align-items-center gap-3"
          >
            <Icon icon="solar:user-linear" className="icon text-xl" /> Hồ sơ
          </Link>
        </li>
        <li>
          <Link
            to="/client/invoice"
            className="dropdown-item text-black px-0 py-8 hover-bg-transparent hover-text-primary d-flex align-items-center gap-3"
          >
            <Icon
              icon="icon-park-outline:setting-two"
              className="icon text-xl"
            />{" "}
            Hóa đơn
          </Link>
        </li>
        <li>
          <button
            className="dropdown-item text-black px-0 py-8 hover-bg-transparent hover-text-danger d-flex align-items-center gap-3 bg-transparent border-0 w-100 text-start"
            onClick={() => {
              // Đổi từ sessionStorage sang localStorage
              ["token", "user_id", "username"].forEach((k) =>
                localStorage.removeItem(k)
              );
              window.location.href = "/client/login";
            }}
          >
            <Icon icon="lucide:power" className="icon text-xl" /> Đăng xuất
          </button>
        </li>
      </ul>
    </div>
  </div>
);

export default UserMenu;
