import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { Helmet } from "react-helmet-async";
import usePageLoading from "../hooks/usePageLoading";
import LoadingSkeleton from "./LoadingSkeleton";
import blogCache from "../utils/blogCache";
import blogApi from "../callapi/BlogApiNew";
import "./blog.css";

const BlogPost = () => {
  const { slug } = useParams();
  const [post, setPost] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [relatedPosts, setRelatedPosts] = useState([]);
  const [skeletonReady, setSkeletonReady] = useState(false);

  // Complete loading khi data đã load xong
  usePageLoading(loading);

  // Fetch single post by slug
  const fetchPost = async () => {
    try {
      setError(null);

      // Check cache first
      const cacheKey = blogCache.generateKey("post", { slug });
      const cachedData = blogCache.get(cacheKey);

      if (cachedData) {
        // Use cached data immediately but keep loading for a moment
        setPost(cachedData);

        // Fetch related posts if available
        if (cachedData.categories && cachedData.categories.length > 0) {
          fetchRelatedPosts(cachedData.categories[0], cachedData.id);
        }

        // Small delay to show loading bar briefly even with cache
        setTimeout(() => {
          setLoading(false);
        }, 300);
        return;
      }

      // No cache, show loading
      setLoading(true);

      const post = await blogApi.getPostBySlug(slug);

      // Cache the result
      blogCache.set(cacheKey, post);

      setPost(post);

      // Fetch related posts
      if (post.categories && post.categories.length > 0) {
        fetchRelatedPosts(post.categories[0], post.id);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Fetch related posts
  const fetchRelatedPosts = async (categoryId, currentPostId) => {
    try {
      const posts = await blogApi.getPostsByCategory(
        categoryId,
        currentPostId,
        3
      );
      setRelatedPosts(posts);
    } catch (err) {
      console.error("Failed to fetch related posts:", err);
    }
  };

  useEffect(() => {
    if (slug) {
      fetchPost();
    }
  }, [slug]);

  // Format date
  const formatDate = (dateString) => {
    const options = {
      year: "numeric",
      month: "long",
      day: "numeric",
      timeZone: "Asia/Ho_Chi_Minh",
    };
    return new Date(dateString).toLocaleDateString("vi-VN", options);
  };

  // Get featured image
  const getFeaturedImage = (post) => {
    if (
      post._embedded &&
      post._embedded["wp:featuredmedia"] &&
      post._embedded["wp:featuredmedia"][0]
    ) {
      return post._embedded["wp:featuredmedia"][0].source_url;
    }
    return "/src/assets/images/blog/blog1.png";
  };

  // Get author name
  const getAuthorName = (post) => {
    if (post._embedded && post._embedded.author && post._embedded.author[0]) {
      return post._embedded.author[0].name;
    }
    return "Pay2S Team";
  };

  // Get category name
  const getCategoryName = (post) => {
    if (
      post._embedded &&
      post._embedded["wp:term"] &&
      post._embedded["wp:term"][0] &&
      post._embedded["wp:term"][0][0]
    ) {
      return post._embedded["wp:term"][0][0].name;
    }
    return "";
  };

  // Get category slug
  const getCategorySlug = (post) => {
    if (
      post._embedded &&
      post._embedded["wp:term"] &&
      post._embedded["wp:term"][0] &&
      post._embedded["wp:term"][0][0]
    ) {
      return post._embedded["wp:term"][0][0].slug;
    }
    return null;
  };

  if (loading) {
    // Set skeleton ready after a brief delay to ensure loading bar shows
    if (!skeletonReady) {
      setTimeout(() => {
        setSkeletonReady(true);
      }, 500); // Loading bar chạy 500ms trước khi skeleton xuất hiện
    }

    return (
      <main className="blog-main">
        <div className="container py-5">
          {/* Skeleton loaders - chỉ hiển thị sau khi loading bar đã chạy */}
          {skeletonReady && (
            <>
              {/* Post skeleton */}
              <div className="row">
                <div className="col-lg-8 mx-auto">
                  <LoadingSkeleton type="post-detail" />
                </div>
              </div>

              {/* Related posts skeleton */}
              <div className="row mt-5">
                <div className="col-12">
                  <h3 className="mb-4">Bài viết liên quan</h3>
                </div>
                {[...Array(3)].map((_, index) => (
                  <div key={index} className="col-lg-4 col-md-6 mb-4">
                    <LoadingSkeleton type="post" />
                  </div>
                ))}
              </div>
            </>
          )}
        </div>
      </main>
    );
  }

  if (error || !post) {
    return (
      <main className="blog-main">
        <div className="container">
          <div className="error-message">
            <h2>Không tìm thấy bài viết</h2>
            <p>{error || "Bài viết không tồn tại hoặc đã bị xóa."}</p>
            <Link to="/blog" className="retry-btn">
              Quay lại Blog
            </Link>
          </div>
        </div>
      </main>
    );
  }

  return (
    <div className="blog-container">
      <Helmet>
        <title>{post.title.rendered} - Blog Pay2S</title>
        <meta
          name="description"
          content={post.excerpt.rendered
            .replace(/<[^>]*>/g, "")
            .substring(0, 160)}
        />
        <meta
          name="keywords"
          content={`pay2s, blog, ${getCategoryName(post)}, thanh toán điện tử`}
        />
        <meta property="og:title" content={post.title.rendered} />
        <meta
          property="og:description"
          content={post.excerpt.rendered
            .replace(/<[^>]*>/g, "")
            .substring(0, 160)}
        />
        <meta property="og:type" content="article" />
        <meta property="og:url" content={`https://pay2s.vn/blog/${slug}`} />
        <meta property="og:image" content={getFeaturedImage(post)} />
        <meta property="article:published_time" content={post.date} />
        <meta property="article:author" content={getAuthorName(post)} />
        <link rel="canonical" href={`https://pay2s.vn/blog/${slug}`} />
      </Helmet>

      <main className="blog-main">
        <div className="container">
          {/* Breadcrumb */}
          <nav className="breadcrumb">
            <Link to="/">Trang chủ</Link>
            <span className="breadcrumb-separator">›</span>
            <Link to="/blog">Blog</Link>
            <span className="breadcrumb-separator">›</span>
            {getCategoryName(post) && getCategorySlug(post) && (
              <>
                <Link to={`/blog/${getCategorySlug(post)}`}>
                  {getCategoryName(post)}
                </Link>
                <span className="breadcrumb-separator">›</span>
              </>
            )}
            <span
              className="breadcrumb-current"
              dangerouslySetInnerHTML={{ __html: post.title.rendered }}
            ></span>
          </nav>

          <article className="blog-post">
            {/* Post Header */}
            <header className="post-header">
              <div className="post-meta">
                <span className="post-date">{formatDate(post.date)}</span>
                {getCategoryName(post) && (
                  <>
                    <span className="meta-separator">•</span>
                    <span className="post-category">
                      {getCategoryName(post)}
                    </span>
                  </>
                )}
                <span className="meta-separator">•</span>
                <span className="post-author">Bởi {getAuthorName(post)}</span>
              </div>

              <h1
                className="post-title"
                dangerouslySetInnerHTML={{ __html: post.title.rendered }}
              />

              {getFeaturedImage(post) && (
                <div className="post-featured-image">
                  <img
                    src={getFeaturedImage(post)}
                    alt={post.title.rendered}
                    onError={(e) => {
                      e.target.src = "/src/assets/images/blog/blog1.png";
                    }}
                  />
                </div>
              )}
            </header>

            {/* Post Content */}
            <div className="post-content">
              <div
                className="post-body"
                dangerouslySetInnerHTML={{ __html: post.content.rendered }}
              />
            </div>

            {/* Post Footer */}
            <footer className="post-footer">
              <div className="post-tags">
                {post._embedded &&
                  post._embedded["wp:term"] &&
                  post._embedded["wp:term"][1] &&
                  post._embedded["wp:term"][1].map((tag) => (
                    <span key={tag.id} className="post-tag">
                      #{tag.name}
                    </span>
                  ))}
              </div>

              <div className="post-share">
                <span>Chia sẻ:</span>
                <a
                  href={`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(
                    window.location.href
                  )}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="share-btn facebook"
                >
                  <i className="fab fa-facebook-f"></i>
                </a>
                <a
                  href={`https://twitter.com/intent/tweet?url=${encodeURIComponent(
                    window.location.href
                  )}&text=${encodeURIComponent(post.title.rendered)}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="share-btn twitter"
                >
                  <i className="fab fa-twitter"></i>
                </a>
                <a
                  href={`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(
                    window.location.href
                  )}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="share-btn linkedin"
                >
                  <i className="fab fa-linkedin-in"></i>
                </a>
              </div>
            </footer>
          </article>

          {/* Related Posts */}
          {relatedPosts.length > 0 && (
            <section className="related-posts">
              <h2>Bài viết liên quan</h2>
              <div className="related-posts-grid">
                {relatedPosts.map((relatedPost) => (
                  <article key={relatedPost.id} className="related-post-card">
                    <div className="related-post-image">
                      <img
                        src={getFeaturedImage(relatedPost)}
                        alt={relatedPost.title.rendered}
                        onError={(e) => {
                          e.target.src = "/src/assets/images/blog/blog1.png";
                        }}
                      />
                    </div>
                    <div className="related-post-content">
                      <h3 className="related-post-title">
                        <Link
                          to={`/blog/${relatedPost.slug}`}
                          dangerouslySetInnerHTML={{
                            __html: relatedPost.title.rendered,
                          }}
                        />
                      </h3>
                      <p className="related-post-date">
                        {formatDate(relatedPost.date)}
                      </p>
                    </div>
                  </article>
                ))}
              </div>
            </section>
          )}

          {/* Navigation */}
          <div className="post-navigation">
            <Link to="/blog" className="back-btn">
              ← Quay lại Blog
            </Link>
            {getCategoryName(post) && getCategorySlug(post) && (
              <Link
                to={`/blog/${getCategorySlug(post)}`}
                className="back-btn category-btn"
              >
                Xem thêm bài viết trong {getCategoryName(post)} →
              </Link>
            )}
          </div>
        </div>
      </main>
    </div>
  );
};

export default BlogPost;
