import React, { useState } from "react";

const ConfirmOtpModal = ({ isOpen, onClose, onSubmit, loading, error }) => {
  const [otp, setOtp] = useState("");

  if (!isOpen) return null;

  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(0,0,0,0.6)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        zIndex: 1050,
      }}
      onClick={onClose}
    >
      <div
        style={{
          background: "white",
          borderRadius: 8,
          width: "90%",
          maxWidth: 400,
          padding: "1rem",
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <h5 className="fw-bold">Nhập mã OTP</h5>
        <p className="text-muted">
          Vui lòng nhập mã OTP đã được g<PERSON><PERSON> đến số điện tho<PERSON>i của bạn.
        </p>
        <input
          type="text"
          className="form-control mb-2"
          value={otp}
          onChange={(e) => setOtp(e.target.value)}
          placeholder="OTP code"
        />
        {error && <p className="text-danger small">{error}</p>}
        <div className="mt-3 d-flex justify-content-end gap-2">
          <button
            className="btn btn-secondary"
            onClick={onClose}
            disabled={loading}
          >
            Hủy
          </button>
          <button
            className="btn btn-primary"
            onClick={() => onSubmit(otp)}
            disabled={loading || !otp}
          >
            {loading ? "Đang xác nhận..." : "Xác nhận"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ConfirmOtpModal;
