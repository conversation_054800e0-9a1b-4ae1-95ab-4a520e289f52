/* Landing Pages Global Styles */

/* Smooth scroll behavior - ONLY for landing pages */
.landing-page {
  scroll-behavior: smooth;
}

/* Specific scroll offsets for different elements - ONLY for landing pages */
.landing-page h1,
.landing-page h2,
.landing-page h3,
.landing-page h4,
.landing-page h5,
.landing-page h6 {
  scroll-margin-top: 100px;
}

/* Landing page container */
.landing-container {
  min-height: 100vh;
}

/* Section spacing - ONLY for landing pages */
.landing-page .landing-section {
  padding: 60px 0;
  scroll-margin-top: 80px;
}

/* Hero section - ONLY for landing pages */
.landing-page .hero-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  scroll-margin-top: 0;
}

/* Navigation smooth scroll enhancement */
.navbar-nav .nav-link {
  transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
  transform: translateY(-2px);
}

/* Button hover effects */
.btn {
  transition: all 0.3s ease;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Scroll to top button */
.scroll-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  background: var(--success-main, #28a745);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
}

.scroll-to-top.show {
  opacity: 1;
  visibility: visible;
}

.scroll-to-top:hover {
  background: var(--success-dark, #1e7e34);
  transform: translateY(-3px);
}

/* Smooth animations for sections */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Card hover effects */
.card {
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Feature section animations */
.feature-item {
  transition: all 0.3s ease;
}

.feature-item:hover {
  transform: scale(1.05);
}

/* Responsive adjustments */
@media (max-width: 768px) {

  section,
  .section,
  [id] {
    scroll-margin-top: 70px;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    scroll-margin-top: 80px;
  }

  .landing-section {
    padding: 40px 0;
  }

  .scroll-to-top {
    bottom: 20px;
    right: 20px;
    width: 45px;
    height: 45px;
  }
}

@media (max-width: 576px) {

  section,
  .section,
  [id] {
    scroll-margin-top: 60px;
  }

  .landing-section {
    padding: 30px 0;
  }
}