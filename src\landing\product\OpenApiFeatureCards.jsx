import React from "react";
import featureCard1 from "../../assets/landing/img/icon/feature_card_1.png";
import featureCard2 from "../../assets/landing/img/icon/feature_card_2.png";
import featureCard3 from "../../assets/landing/img/icon/feature_card_3.png";

const featureList = [
  {
    img: featureCard1,
    title: "Tự động hóa đối soát",
    text: "Cập nhật ngay theo thời gian thực trạng thái giao dịch",
  },
  {
    img: featureCard2,
    title: "Tạo QR thanh toán siêu tốc",
    text: "<PERSON><PERSON><PERSON><PERSON> qua trung gian, tỷ lệ thành công 99,9%",
  },
  {
    img: featureCard3,
    title: "Tr<PERSON>ch lương tự động",
    text: "Webhook gửi data thẳng vào hệ thống payroll",
  },
];

const OpenApiFeatureCards = () => (
  <section className="about-sec-v4 space-bottom">
    <div className="container">
      <div className="row gy-4 justify-content-center">
        {featureList.map((item, idx) => (
          <div className="col-xl-4 col-md-6" key={idx}>
            <div className="feature-card">
              <div className="shape-icon">
                <img src={item.img} alt="icon" />
              </div>
              <h3 className="box-title">{item.title}</h3>
              <p className="feature-card_text">{item.text}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  </section>
);

export default OpenApiFeatureCards;
