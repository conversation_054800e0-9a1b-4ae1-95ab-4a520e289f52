import React from "react";

const comparisonRows = [
  ["Thanh toán QR Code", "✅", "✅", "✅"],
  ["Xác nhận thanh toán ngay", "✅", "✅", "✅"],
  [
    "Công nghệ",
    "Trung gian thanh toán VietQR",
    "API ngân hàng VietQR",
    "API ngân hàng VietQR",
  ],
  [
    "Mức phí mỗi giao dịch",
    "1.000đ + 1 đến 3% (*)",
    "~350đ (**)",
    "✅ Không tính phí trên giao dịch",
  ],
  [
    "Dòng tiền",
    "Giữ tại trung gian thanh toán",
    "Về ngay tài khoản ngân hàng",
    "✅Về ngay tài khoản ngân hàng",
  ],
  ["Đối soát", "Kh<PERSON> khăn", "<PERSON><PERSON>", "✅ Rất dễ thông qua dashboard quản lý"],
  [
    "Thủ tục đăng ký",
    "<PERSON><PERSON><PERSON> c<PERSON><PERSON> gi<PERSON>y phép đăng ký kinh doanh",
    "Không yêu cầu, đăng ký sử dụng ngay",
    "✅Không yêu cầu, đăng ký sử dụng ngay",
  ],
  ["Hỗ trợ cá nhân", "❌", "✅", "✅"],
  ["Tích hợp nhanh chóng", "❌", "✅", "✅"],
];

const OpenApiComparisonTable = () => (
  <div className="container space" style={{ color: "black" }}>
    <h2 className="sec-title text-center">So sánh giữa các nền tảng</h2>
    <table
      border="1"
      cellSpacing="0"
      cellPadding="10"
      className="comparison-table"
    >
      <thead>
        <tr>
          <th></th>
          <th>Thanh toán truyền thống</th>
          <th>Cổng thanh toán khác</th>
          <th>
            Cổng thanh toán Pay2s <span className="tag-hot">Hot</span>
          </th>
        </tr>
      </thead>
      <tbody>
        {comparisonRows.map((row, idx) => (
          <tr key={idx}>
            <td data-label="Tính năng">{row[0]}</td>
            <td data-label="Truyền thống">{row[1]}</td>
            <td data-label="Khác">{row[2]}</td>
            <td data-label="Pay2s">{row[3]}</td>
          </tr>
        ))}
      </tbody>
    </table>
  </div>
);

export default OpenApiComparisonTable;
