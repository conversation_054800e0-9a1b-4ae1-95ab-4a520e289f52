import React from "react";
import Header from "./Header";
import Footer from "./Footer";
import usePageLoading from "../hooks/usePageLoading";
import CongThanhToanHero from "./product/CongThanhToanHero";
import CongThanhToanAbout from "./product/CongThanhToanAbout";
import OpenApiFeatureCards from "./product/OpenApiFeatureCards";
import Solution from "./product/Solution";
import OpenApiAbout from "./product/OpenApiAbout";
import OpenApiComparisonTable from "./product/OpenApiComparisonTable";
import BankPartnersSection from "./product/BankPartnersSection";
import OpenApiCta from "./product/OpenApiCta";

const CongThanhToan = () => {
  // Complete loading khi component đã mount
  usePageLoading();

  return (
    <>
      <Header />
      <CongThanhToanHero />
      <CongThanhToanAbout />
      <Solution />

      <OpenApiComparisonTable />
      <BankPartnersSection />
      <OpenApiCta />
      <Footer />
    </>
  );
};

export default CongThanhToan;
