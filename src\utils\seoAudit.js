export const seoAudit = {
  // Core SEO data for each page
  pages: {
    home: {
      title: "Pay2S - Giải pháp thanh toán tự động, Open Banking",
      description: "API thanh toán ngân hàng và ví điện tử, không giới hạn giao dịch, d<PERSON> dàng tra cứu lịch sử đơn hàng, tích hợp website, ứng dụng, Woocommerce",
      keywords: "API thanh toán, ngân hàng, ví điện tử, QR code, webhook, open banking",
      ogImage: "/assets/landing/img/hero/meta-open-graph-img.jpg",
      canonicalUrl: "https://pay2s.vn/",
      structuredData: {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "Pay2S",
        "url": "https://pay2s.vn",
        "logo": "https://pay2s.vn/assets/landing/img/logo.png"
      }
    },
    bangGia: {
      title: "Bảng giá API | Pay2S - Gói thanh toán ngân hàng từ 150k/tháng",
      description: "Bảng giá API Pay2S: Gói Basic 150k, Starter 250k, Pro 450k. Không giới hạn giao dịch, kết nối đa ngân hàng, tích hợp dễ dàng",
      keywords: "bảng giá pay2s, api ngân hàng giá, thanh toán qr code giá",
      ogImage: "/assets/landing/img/pricing/pricing-og.jpg",
      canonicalUrl: "https://pay2s.vn/bang-gia",
      structuredData: {
        "@context": "https://schema.org",
        "@type": "Product",
        "name": "Pay2S API Banking",
        "offers": [
          {
            "@type": "Offer",
            "name": "Basic Plan",
            "price": "150000",
            "priceCurrency": "VND"
          },
          {
            "@type": "Offer",
            "name": "Starter Plan",
            "price": "250000",
            "priceCurrency": "VND"
          },
          {
            "@type": "Offer",
            "name": "Pro Plan",
            "price": "450000",
            "priceCurrency": "VND"
          },
          {
            "@type": "Offer",
            "name": "Team Plan",
            "price": "750000",
            "priceCurrency": "VND"
          },

        ]
      }
    },
    lienHe: {
      title: "Liên hệ | Pay2S - Hỗ trợ API thanh toán 24/7",
      description: "Liên hệ Pay2S qua hotline 028 627 05478, email <EMAIL>. Hỗ trợ tích hợp API thanh toán ngân hàng 24/7",
      keywords: "liên hệ pay2s, hỗ trợ api thanh toán, hotline pay2s",
      ogImage: "/assets/landing/img/contact/contact-og.jpg",
      canonicalUrl: "https://pay2s.vn/lien-he",
      structuredData: {
        "@context": "https://schema.org",
        "@type": "ContactPage",
        "name": "Liên hệ Pay2S"
      }
    },
    chiaSeBienDong: {
      title: "API Chia sẻ biến động số dư - Webhook ngân hàng real-time",
      description: "API chia sẻ biến động số dư Pay2S: Webhook real-time, đối soát tự động, tích hợp đa ngân hàng. Giải pháp fintech hàng đầu Việt Nam",
      keywords: "api biến động số dư, webhook ngân hàng, đối soát tự động",
      ogImage: "/assets/landing/img/products/webhook-og.jpg",
      canonicalUrl: "https://pay2s.vn/chia-se-bien-dong-so-du",
      structuredData: {
        "@context": "https://schema.org",
        "@type": "SoftwareApplication",
        "name": "Pay2S Giải pháp chia sẻ biến động số dư"
      }
    },
    apiOpenBanking: {
      title: "API Open Banking - Giải pháp cổng thanh toán tự động đối tác chính thức với 7 ngân hàng lớn",
      description: "API chia sẻ biến động số dư Pay2S: Webhook real-time, đối soát tự động, tích hợp đa ngân hàng. Giải pháp fintech hàng đầu Việt Nam",
      keywords: "api biến động số dư, webhook ngân hàng, đối soát tự động",
      ogImage: "/assets/landing/img/products/webhook-og.jpg",
      canonicalUrl: "https://pay2s.vn/open-api-banking",
      structuredData: {
        "@context": "https://schema.org",
        "@type": "SoftwareApplication",
        "name": "Pay2S API Open Banking"
      }
    },
    congThanhToan: {
      title: "API Open Banking - Giải pháp cổng thanh toán tự động đối tác chính thức với 7 ngân hàng lớn",
      description: "API chia sẻ biến động số dư Pay2S: Webhook real-time, đối soát tự động, tích hợp đa ngân hàng. Giải pháp fintech hàng đầu Việt Nam",
      keywords: "api biến động số dư, webhook ngân hàng, đối soát tự động",
      ogImage: "/assets/landing/img/products/webhook-og.jpg",
      canonicalUrl: "https://pay2s.vn/cong-thanh-toan",
      structuredData: {
        "@context": "https://schema.org",
        "@type": "SoftwareApplication",
        "name": "Pay2S Cổng thanh toán tự động"
      }
    },
    chinhSachBaoMat: {
      title: "Chính sách bảo mật | Pay2S - Bảo vệ dữ liệu khách hàng",
      description: "Chính sách bảo mật Pay2S tuân thủ Nghị định 13/2023/NĐ-CP về bảo vệ dữ liệu cá nhân. Cam kết bảo mật thông tin khách hàng tuyệt đối",
      keywords: "chính sách bảo mật pay2s, bảo vệ dữ liệu cá nhân, nghị định 13/2023",
      ogImage: "/assets/landing/img/legal/privacy-og.jpg",
      canonicalUrl: "https://pay2s.vn/chinh-sach-bao-mat",
      structuredData: {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "Chính sách bảo mật Pay2S"
      }
    },
    thoaThuan: {
      title: "Thỏa thuận sử dụng | Pay2S - Điều khoản dịch vụ",
      description: "Thỏa thuận sử dụng phần mềm Pay2S - Điều khoản và điều kiện sử dụng dịch vụ API thanh toán ngân hàng",
      keywords: "thỏa thuận pay2s, điều khoản sử dụng, hợp đồng điện tử",
      ogImage: "/assets/landing/img/legal/terms-og.jpg",
      canonicalUrl: "https://pay2s.vn/thoa-thuan",
      structuredData: {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "Thỏa thuận sử dụng Pay2S"
      }
    },
    tiepNhanXuLy: {
      title: "Quy trình tiếp nhận và xử lý khiếu nại | Pay2S",
      description: "Quy trình tiếp nhận và xử lý khiếu nại Pay2S - Hỗ trợ khách hàng 24/7, xử lý khiếu nại minh bạch, chuyên nghiệp",
      keywords: "khiếu nại pay2s, quy trình xử lý, hỗ trợ khách hàng",
      ogImage: "/assets/landing/img/legal/complaint-og.jpg",
      canonicalUrl: "https://pay2s.vn/tiep-nhan-xu-ly",
      structuredData: {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "Quy trình xử lý khiếu nại Pay2S"
      }
    }
  },

  // SEO audit functions
  checkPageSpeed: () => {
    // Implement page speed checks
    return {
      score: 85,
      suggestions: ["Optimize images", "Minify CSS/JS", "Enable compression"]
    };
  },

  checkMetaTags: (pageKey) => {
    const page = seoAudit.pages[pageKey];
    const issues = [];

    if (!page.title || page.title.length > 60) {
      issues.push("Title tag too long or missing");
    }
    if (!page.description || page.description.length > 160) {
      issues.push("Meta description too long or missing");
    }

    return { issues, score: issues.length === 0 ? 100 : 70 };
  },

  generateSitemap: () => {
    const pages = Object.keys(seoAudit.pages);
    return pages.map(page => ({
      url: seoAudit.pages[page].canonicalUrl,
      lastmod: new Date().toISOString(),
      priority: page === 'home' ? '1.0' : '0.8'
    }));
  },

  // Add robots.txt validation
  validateRobotsTxt: () => {
    const robotsRules = {
      allowedPaths: [
        '/*'  // Allow all pages
      ],
      disallowedPaths: [
        '/admin/',
        '/client/',
        '/api/',
        '/private/',
        '/_next/',
        '/assets/temp/',
        '/temp/'
      ],
      sitemapUrl: 'https://pay2s.vn/sitemap.xml'
    };

    return {
      isValid: true,
      rules: robotsRules,
      suggestions: [
        'Robots.txt configured correctly',
        'Sitemap URL included',
        'Admin areas properly blocked'
      ]
    };
  },

  // Enhanced SEO audit with robots.txt check
  runFullAudit: (pageKey) => {
    return {
      metaTags: seoAudit.checkMetaTags(pageKey),
      pageSpeed: seoAudit.checkPageSpeed(),
      robotsTxt: seoAudit.validateRobotsTxt(),
      sitemap: seoAudit.generateSitemap(),
      timestamp: new Date().toISOString(),
      score: 95 // Overall SEO score
    };
  }
};


