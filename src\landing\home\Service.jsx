import React from "react";
import clsx from "clsx";
import ctaBg from "../../assets/landing/img/bg/cta_bg_7_1.png";

const counters = [
  {
    value: "10",
    suffix: "k+",
    text: "<PERSON><PERSON><PERSON><PERSON> hàng sử dụng thường xuyên",
  },
  {
    value: "99",
    suffix: "%",
    text: "<PERSON>ệ thống ổn định, nhanh, 99% uptime",
  },
  {
    value: "4.9",
    suffix: "",
    text: "Khách hàng luôn đánh giá 5* cho dịch vụ",
  },
];

const Service = () => (
  <>
    <section
      className={clsx(
        "cta-sec7 overflow-hidden m-4 mt-0 mb-0 bg-theme space-top"
      )}
      style={{ backgroundImage: `url(${ctaBg})` }}
    >
      <div className="container th-container4">
        <div className="row justify-content-center">
          <div className="col-lg-6">
            <div className="title-area text-center mb-30">
              <h2 className="sec-title text-white">
                Pay2S đã đạt được những gì?
              </h2>
            </div>
            <div className="btn-wrap justify-content-center mb-60">
              <a href="/client/signup" className="th-btn style4 style-radius">
                Đăng ký ngay
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
    <div className="counter-area-5 overflow-hidden">
      <div className="container th-container4">
        <div className="row justify-content-md-between justify-content-center gy-4">
          {counters.map((counter, idx) => (
            <div className="col-xl-auto col-md-4 col-sm-6" key={idx}>
              <div className="counter-card5">
                <h3 className="box-number">
                  <span className="counter-number">{counter.value}</span>
                  {counter.suffix}
                </h3>
                <div className="media-body">
                  <p className="counter-text mb-n1">{counter.text}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  </>
);

export default Service;
