import MasterLayout from "../masterLayout/MasterLayout";
import Breadcrumb from "../components/Breadcrumb";
import { useEffect, useState } from "react";
import axios from "axios";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useNavigate } from "react-router-dom"; // 1. Thêm import useNavigate

const ProfilePage = () => {
  const [tab, setTab] = useState("account");
  const navigate = useNavigate(); // 2. Khởi tạo hook useNavigate
  const [form, setForm] = useState({
    firstName: "",
    lastName: "",
    username: "",
    email: "",
    phone: "",
    company: "",
    tax: "",
    address: "",
    tfa: false,
  });

  const [accountInfo, setAccountInfo] = useState({
    credit: 0,
    plan: "",
    expire: "",
    billing: "",
  });

  const [passwordForm, setPasswordForm] = useState({
    current: "",
    newpass: "",
    confirm: "",
  });

  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const token = localStorage.getItem("token");
    const userId = localStorage.getItem("user_id");

    axios
      .post(
        "https://api.pay2s.vn/api/v1/user",
        new URLSearchParams({
          action: "get_profile",
          user_id: userId || "",
        }),
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
            Authorization: `Bearer ${token}`,
          },
        }
      )
      .then((res) => {
        const data = res.data;
        if (data.status && data.message) {
          const m = data.message;
          setForm({
            firstName: m.firstname || "",
            lastName: m.lastname || "",
            username: m.username || "",
            email: m.email || "",
            phone: m.phone || "",
            company: m.company_name || "",
            tax: m.tax_number || "",
            address: m.address || "",
            tfa: m.otp_login === 1,
          });
          setAccountInfo({
            credit: m.credit || 0,
            plan: m.current_plan || "",
            expire: m.expire_date || "",
            billing: m.billingcycle || "",
          });
        }
      });
  }, []);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setForm({ ...form, [name]: type === "checkbox" ? checked : value });
  };

  const handlePasswordChange = (e) => {
    const { name, value } = e.target;
    setPasswordForm({ ...passwordForm, [name]: value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    const token = localStorage.getItem("token");
    const userId = localStorage.getItem("user_id");

    try {
      const response = await axios.post(
        "https://api.pay2s.vn/api/v1/user",
        new URLSearchParams({
          action: "update_profile",
          firstname: form.firstName,
          lastname: form.lastName,
          user_id: userId,
          company_name: form.company,
          tax_number: form.tax,
          address: form.address,
          otp_login: form.tfa ? "1" : "0",
        }),
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.data.status) {
        toast.success("Cập nhật hồ sơ thành công");
      } else {
        toast.error("Cập nhật thất bại: " + response.data.message);
      }
    } catch (err) {
      toast.error("Lỗi khi cập nhật hồ sơ");
    }

    setLoading(false);
  };

  // 3. Sửa đổi hàm handlePasswordSubmit
  const handlePasswordSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    if (passwordForm.newpass !== passwordForm.confirm) {
      toast.error("Mật khẩu mới không khớp với xác nhận");
      setLoading(false);
      return;
    }

    const token = localStorage.getItem("token");
    const userId = localStorage.getItem("user_id");

    try {
      const response = await axios.post(
        "https://api.pay2s.vn/api/v1/user",
        new URLSearchParams({
          action: "change_password",
          user_id: userId,
          currentPassword: passwordForm.current,
          newPassword: passwordForm.newpass,
          confirmNewPassword: passwordForm.confirm,
        }),
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.data.status) {
        toast.success("Đổi mật khẩu thành công! Vui lòng đăng nhập lại.");
        // Xóa thông tin đăng nhập
        localStorage.removeItem("token");
        localStorage.removeItem("user_id");
        localStorage.removeItem("username");

        // Chờ 1.5 giây để người dùng đọc thông báo rồi chuyển hướng tới trang đăng nhập
        setTimeout(() => {
          navigate("/client/login");
        }, 1500);
      } else {
        toast.error("Đổi mật khẩu thất bại: " + response.data.message);
        setLoading(false);
      }
    } catch (err) {
      toast.error("Lỗi khi đổi mật khẩu");
      setLoading(false);
    }
    // Không cần setLoading(false) ở đây nữa vì trang sẽ chuyển hướng
  };

  return (
    <MasterLayout>
      <Breadcrumb title="Hồ sơ" />
      <ToastContainer position="top-right" autoClose={1500} />
      <div className="container py-5">
        <div className="row">
          <div className="col-lg-8">
            <div className="card p-5">
              <ul className="nav nav-tabs mb-4 usertab">
                <li className="nav-item">
                  <button
                    className={`nav-link ${tab === "account" ? "active" : ""}`}
                    onClick={() => setTab("account")}
                  >
                    Thông tin tài khoản
                  </button>
                </li>
                <li className="nav-item">
                  <button
                    className={`nav-link ${tab === "password" ? "active" : ""}`}
                    onClick={() => setTab("password")}
                  >
                    Đổi mật khẩu
                  </button>
                </li>
              </ul>

              {tab === "account" ? (
                <form onSubmit={handleSubmit}>
                  <div className="row mb-3">
                    <div className="col">
                      <label className="form-label">Họ</label>
                      <input
                        type="text"
                        name="firstName"
                        className="form-control"
                        value={form.firstName}
                        onChange={handleChange}
                      />
                    </div>
                    <div className="col">
                      <label className="form-label">Tên</label>
                      <input
                        type="text"
                        name="lastName"
                        className="form-control"
                        value={form.lastName}
                        onChange={handleChange}
                      />
                    </div>
                  </div>
                  <div className="mb-3">
                    <label className="form-label">Tên đăng nhập *</label>
                    <input
                      type="text"
                      name="username"
                      className="form-control"
                      value={form.username}
                      readOnly
                    />
                  </div>
                  <div className="mb-3">
                    <label className="form-label">Email *</label>
                    <input
                      type="email"
                      name="email"
                      className="form-control"
                      value={form.email}
                      readOnly
                    />
                  </div>
                  <div className="mb-3">
                    <label className="form-label">Điện thoại *</label>
                    <input
                      type="tel"
                      name="phone"
                      className="form-control"
                      value={form.phone}
                      readOnly
                    />
                  </div>
                  <div className="mb-3">
                    <label className="form-label">Tên công ty</label>
                    <input
                      type="text"
                      name="company"
                      className="form-control"
                      value={form.company}
                      onChange={handleChange}
                    />
                  </div>
                  <div className="mb-3">
                    <label className="form-label">Mã số thuế</label>
                    <input
                      type="text"
                      name="tax"
                      className="form-control"
                      value={form.tax}
                      onChange={handleChange}
                    />
                  </div>
                  <div className="mb-3">
                    <label className="form-label">Địa chỉ công ty</label>
                    <input
                      type="text"
                      name="address"
                      className="form-control"
                      value={form.address}
                      onChange={handleChange}
                    />
                  </div>
                  <div className="form-check mb-3">
                    <input
                      type="checkbox"
                      name="tfa"
                      className="form-check-input"
                      checked={form.tfa}
                      onChange={handleChange}
                      id="tfaCheck"
                    />
                    <label htmlFor="tfaCheck" className="form-check-label">
                      Bật xác thực 2 lớp
                    </label>
                  </div>
                  <button
                    type="submit"
                    className="btn btn-success"
                    disabled={loading}
                  >
                    {loading ? "Đang cập nhật..." : "CẬP NHẬT"}
                  </button>
                </form>
              ) : (
                <form onSubmit={handlePasswordSubmit}>
                  <div className="mb-3">
                    <label className="form-label">Mật khẩu hiện tại *</label>
                    <input
                      type="password"
                      name="current"
                      className="form-control"
                      value={passwordForm.current}
                      onChange={handlePasswordChange}
                      required
                    />
                  </div>
                  <div className="mb-3">
                    <label className="form-label">Mật khẩu mới *</label>
                    <input
                      type="password"
                      name="newpass"
                      className="form-control"
                      value={passwordForm.newpass}
                      onChange={handlePasswordChange}
                      required
                    />
                  </div>
                  <div className="mb-3">
                    <label className="form-label">
                      Xác nhận mật khẩu mới *
                    </label>
                    <input
                      type="password"
                      name="confirm"
                      className="form-control"
                      value={passwordForm.confirm}
                      onChange={handlePasswordChange}
                      required
                    />
                  </div>
                  <button
                    type="submit"
                    className="btn btn-success"
                    disabled={loading}
                  >
                    {loading ? "Đang cập nhật..." : "CẬP NHẬT"}
                  </button>
                </form>
              )}
            </div>
          </div>

          <div className="col-lg-4">
            <div className="card p-5 dark:bg-dark dark:text-white">
              <p className="mb-1">Số dư tài khoản</p>
              <h4 className="fw-bold">
                {accountInfo.credit.toLocaleString()} đ
              </h4>
              <hr />
              <p className="mb-1">
                Gói đã đăng ký:{" "}
                <strong className="text-success">{accountInfo.plan}</strong>
              </p>
              <p className="mb-1">
                Kỳ hạn:{" "}
                {accountInfo.billing === "annually"
                  ? "1 năm"
                  : accountInfo.billing}
              </p>
              <p className="mb-0">Hạn sử dụng: {accountInfo.expire}</p>
            </div>
          </div>
        </div>
      </div>
    </MasterLayout>
  );
};

export default ProfilePage;
