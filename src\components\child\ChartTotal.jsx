import React, { useEffect, useState } from "react";
import useInvoiceApi from "../../callapi/InvoiceUserBank";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";

const ChartWeekly = () => {
  const [series, setSeries] = useState([
    { name: "Tuần này", data: Array(7).fill(0) },
    { name: "Tuần trước", data: Array(7).fill(0) },
  ]);

  // use custom hook for invoices API
  const { data: apiData, loading, error, callApi } = useInvoiceApi();

  // fetch weekly totals
  useEffect(() => {
    const userId = localStorage.getItem("user_id");
    if (userId) callApi({ action: "total_bank_invoices", user_id: userId });
  }, [callApi]);

  // process API data into series
  useEffect(() => {
    if (apiData?.total_bank_invoices) {
      const data = apiData.total_bank_invoices;
      const now = new Date();
      const day = now.getDay();
      const diffToMonday = (day + 6) % 7;
      const startCurrent = new Date(now);
      startCurrent.setDate(now.getDate() - diffToMonday);
      startCurrent.setHours(0, 0, 0, 0);
      const startPrev = new Date(startCurrent);
      startPrev.setDate(startCurrent.getDate() - 7);

      const currArr = Array(7).fill(0);
      const prevArr = Array(7).fill(0);
      data.forEach((item) => {
        const rDate = new Date(item.day);
        rDate.setHours(0, 0, 0, 0);
        const idx = (rDate.getDay() + 6) % 7;
        if (
          rDate >= startCurrent &&
          rDate < new Date(startCurrent.getTime() + 7 * 24 * 3600 * 1000)
        ) {
          currArr[idx] += Number(item.total_amount_by_day) || 0;
        } else if (rDate >= startPrev && rDate < startCurrent) {
          prevArr[idx] += Number(item.total_amount_by_day) || 0;
        }
      });
      setSeries([
        { name: "Tuần này", data: currArr },
        { name: "Tuần trước", data: prevArr },
      ]);
    }
  }, [apiData]);

  // prepare data for Recharts
  const categories = [
    "Thứ Hai",
    "Thứ Ba",
    "Thứ Tư",
    "Thứ Năm",
    "Thứ Sáu",
    "Thứ Bảy",
    "Chủ Nhật",
  ];
  const chartData = categories.map((name, idx) => ({
    name,
    "Tuần này": series[0]?.data[idx] || 0,
    "Tuần trước": series[1]?.data[idx] || 0,
  }));

  return (
    <div className="card">
      <div className="card-header">
        <h6 className="mb-0">Tổng tiền giao dịch hàng tuần</h6>
      </div>
      <div className="card-body">
        <ResponsiveContainer width="100%" height={350}>
          <BarChart
            data={chartData}
            margin={{ top: 20, right: 20, left: 60, bottom: 40 }}
            barCategoryGap="20%"
            barGap={8}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
            <XAxis
              dataKey="name"
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: "#555" }}
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: "#555" }}
              tickFormatter={(val) => val.toLocaleString("vi-VN") + " đ"}
            />
            <Tooltip formatter={(val) => val.toLocaleString("vi-VN") + " đ"} />
            <Legend
              verticalAlign="bottom"
              align="center"
              iconType="circle"
              wrapperStyle={{ paddingTop: 10 }}
            />
            <Bar dataKey="Tuần này" fill="#4CAF50" radius={[4, 4, 0, 0]} />
            <Bar dataKey="Tuần trước" fill="#90EE90" radius={[4, 4, 0, 0]} />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default ChartWeekly;
